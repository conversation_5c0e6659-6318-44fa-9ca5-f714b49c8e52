name: Process Docs Embeddings

on:
  push:
    branches: [main, staging]
    paths:
      - 'apps/docs/**'
  workflow_dispatch: # Allow manual triggering

jobs:
  process-docs-embeddings:
    name: Process Documentation Embeddings
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/staging'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: latest

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: latest

      - name: Install dependencies
        run: bun install

      - name: Process docs embeddings
        working-directory: ./apps/sim
        env:
          DATABASE_URL: ${{ github.ref == 'refs/heads/main' && secrets.DATABASE_URL || secrets.STAGING_DATABASE_URL }}
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
        run: bun run scripts/process-docs-embeddings.ts --clear 
