name: Publish TypeScript SDK

on:
  push:
    branches: [main]
    paths:
      - 'packages/ts-sdk/**'

jobs:
  publish-npm:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: latest

      - name: Setup Node.js for npm publishing
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          registry-url: 'https://registry.npmjs.org/'

      - name: Install dependencies
        working-directory: packages/ts-sdk
        run: bun install

      - name: Run tests
        working-directory: packages/ts-sdk
        run: bun run test

      - name: Build package
        working-directory: packages/ts-sdk
        run: bun run build

      - name: Get package version
        id: package_version
        working-directory: packages/ts-sdk
        run: echo "version=$(node -p "require('./package.json').version")" >> $GITHUB_OUTPUT

      - name: Check if version already exists
        id: version_check
        run: |
          if npm view simstudio-ts-sdk@${{ steps.package_version.outputs.version }} version &> /dev/null; then
            echo "exists=true" >> $GITHUB_OUTPUT
          else
            echo "exists=false" >> $GITHUB_OUTPUT
          fi

      - name: Publish to npm
        if: steps.version_check.outputs.exists == 'false'
        working-directory: packages/ts-sdk
        run: npm publish --access=public
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}

      - name: Log skipped publish
        if: steps.version_check.outputs.exists == 'true'
        run: echo "Skipped publishing because version ${{ steps.package_version.outputs.version }} already exists on npm"

      - name: Create GitHub Release
        if: steps.version_check.outputs.exists == 'false'
        uses: softprops/action-gh-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: typescript-sdk-v${{ steps.package_version.outputs.version }}
          name: TypeScript SDK v${{ steps.package_version.outputs.version }}
          body: |
            ## TypeScript SDK v${{ steps.package_version.outputs.version }}
            
            Published simstudio-ts-sdk@${{ steps.package_version.outputs.version }} to npm.
            
            ### Installation
            ```bash
            npm install simstudio-ts-sdk@${{ steps.package_version.outputs.version }}
            ```
            
            ### Documentation
            See the [README](https://github.com/simstudio/sim/tree/main/packages/ts-sdk) for usage instructions.
          draft: false
          prerelease: false 