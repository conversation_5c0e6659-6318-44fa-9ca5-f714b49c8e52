# GitHub Copilot Instructions for Sim

## Project Overview
Sim is an AI agent workflow platform built with Next.js, TypeScript, and modern web technologies. It allows users to build and deploy AI agent workflows through a visual interface.

## Key Technologies
- **Framework**: Next.js 15 with App Router
- **Runtime**: Bun
- **Language**: TypeScript/JavaScript
- **Database**: PostgreSQL with Drizzle ORM and pgvector extension
- **UI**: Tailwind CSS, Shadcn/ui components
- **State Management**: Zustand
- **Flow Editor**: ReactFlow
- **Authentication**: Better Auth
- **Realtime**: Socket.io
- **Monorepo**: Turborepo with Bun workspaces
- **Code Quality**: Biome (linting and formatting)

## Code Style Guidelines

### TypeScript/JavaScript
- Use TypeScript for all new code
- Prefer functional components with hooks
- Use arrow functions for components and utilities
- Implement proper error boundaries and error handling
- Use Zod for runtime type validation when needed

### React/Next.js Patterns
- Use App Router patterns (app directory)
- Implement Server Components where appropriate
- Use Client Components only when necessary (interactivity, browser APIs)
- Follow Next.js 15 best practices for data fetching
- Use proper loading and error states

### Database & API
- Use Drizzle ORM for database operations
- Implement proper database migrations
- Use tRPC or Next.js API routes for backend logic
- Handle database connections properly with connection pooling
- Use pgvector for AI embeddings and vector operations

### UI/UX
- Use Tailwind CSS for styling
- Follow Shadcn/ui component patterns
- Implement responsive design
- Use proper accessibility attributes
- Follow consistent spacing and typography scales

### State Management
- Use Zustand for global state
- Keep state minimal and normalized
- Use React Query/TanStack Query for server state
- Implement proper loading and error states

## File Structure Patterns
```
apps/sim/
├── app/                    # Next.js App Router
├── components/            # Reusable UI components
├── lib/                   # Utilities and configurations
├── stores/               # Zustand stores
├── types/                # TypeScript type definitions
└── public/               # Static assets

packages/
├── ts-sdk/               # TypeScript SDK
├── python-sdk/           # Python SDK
└── cli/                  # CLI package
```

## Naming Conventions
- Use kebab-case for file and directory names
- Use PascalCase for React components
- Use camelCase for functions and variables
- Use SCREAMING_SNAKE_CASE for constants
- Use descriptive names that indicate purpose

## Common Patterns to Follow

### Component Structure
```typescript
interface ComponentProps {
  // Define props with proper types
}

export function Component({ prop1, prop2 }: ComponentProps) {
  // Component logic
  return (
    <div className="proper-tailwind-classes">
      {/* JSX content */}
    </div>
  )
}
```

### API Route Structure
```typescript
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    // API logic
    return NextResponse.json({ data })
  } catch (error) {
    return NextResponse.json({ error: 'Error message' }, { status: 500 })
  }
}
```

### Database Operations
```typescript
import { db } from '@/lib/db'
import { users } from '@/lib/db/schema'

export async function getUser(id: string) {
  return await db.select().from(users).where(eq(users.id, id))
}
```

## AI/Copilot Features
- The project includes built-in Copilot functionality for AI assistance
- Use the COPILOT_API_KEY environment variable for Sim's managed Copilot service
- Implement proper AI model integrations following existing patterns
- Handle AI responses with proper error handling and fallbacks

## Testing Patterns
- Write unit tests for utilities and pure functions
- Write integration tests for API routes
- Write component tests for complex UI logic
- Use proper mocking for external dependencies

## Performance Considerations
- Implement proper code splitting
- Use React.memo() for expensive components
- Optimize database queries with proper indexing
- Use proper caching strategies
- Implement proper image optimization

## Security Best Practices
- Validate all user inputs
- Use proper authentication and authorization
- Sanitize data before database operations
- Implement proper CORS policies
- Use environment variables for sensitive data

## Common Pitfalls to Avoid
- Don't use any type in TypeScript
- Don't forget to handle loading and error states
- Don't perform side effects in render functions
- Don't forget to clean up subscriptions and listeners
- Don't hardcode configuration values

## Helpful Context
When suggesting code, consider:
- The monorepo structure with multiple packages
- The use of Bun as the runtime and package manager
- The integration with various AI services and models
- The real-time nature of the application with Socket.io
- The visual workflow editor built with ReactFlow
- The multi-tenant architecture with workspaces
