@import "tailwindcss";
@import "fumadocs-ui/css/neutral.css";
@import "fumadocs-ui/css/preset.css";

@theme {
  --color-fd-primary: #802fff; /* Purple from control-bar component */
}

/* Custom text highlighting styles */
.text-highlight {
  color: var(--color-fd-primary);
}

/* Override marker color for highlighted lists */
.highlight-markers li::marker {
  color: var(--color-fd-primary);
}

/* Tailwind v4 content sources */
@source '../app/**/*.{js,ts,jsx,tsx,mdx}';
@source '../components/**/*.{js,ts,jsx,tsx,mdx}';
@source '../content/**/*.{js,ts,jsx,tsx,mdx}';
@source '../mdx-components.tsx';
@source '../node_modules/fumadocs-ui/dist/**/*.js';
