---
title: YAML Workflow Examples
description: Examples of complete YAML workflows
---

import { Tab, Tabs } from 'fumadocs-ui/components/tabs'

## Multi-Agent Chain Workflow

A workflow where multiple AI agents process information sequentially:

```yaml
version: '1.0'
blocks:
  start:
    type: starter
    name: Start
    inputs:
      startWorkflow: manual
    connections:
      success: agent-1-initiator

  agent-1-initiator:
    type: agent
    name: Agent 1 Initiator
    inputs:
      systemPrompt: You are the first agent in a chain. Your role is to analyze the input and create an initial response that will be passed to the next agent.
      userPrompt: |-
        Welcome! I'm the first agent in our chain.

        Input to process: <start.input>

        Please create an initial analysis or greeting that the next agent can build upon. Be creative and set a positive tone for the chain!
      model: gpt-4o
      temperature: 0.7
      apiKey: '{{OPENAI_API_KEY}}'
    connections:
      success: agent-2-enhancer

  agent-2-enhancer:
    type: agent
    name: Agent 2 Enhancer
    inputs:
      systemPrompt: You are the second agent in a chain. Take the output from Agent 1 and enhance it with additional insights or improvements.
      userPrompt: |-
        I'm the second agent! Here's what Agent 1 provided:

        <agent1initiator.content>

        Now I'll enhance this with additional details, insights, or improvements. Let me build upon their work!
      model: gpt-4o
      temperature: 0.7
      apiKey: '{{OPENAI_API_KEY}}'
    connections:
      success: agent-3-refiner

  agent-3-refiner:
    type: agent
    name: Agent 3 Refiner
    inputs:
      systemPrompt: You are the third agent in a chain. Take the enhanced output from Agent 2 and refine it further, adding structure or organization.
      userPrompt: |-
        I'm the third agent in our chain! Here's the enhanced work from Agent 2:

        <agent2enhancer.content>

        My job is to refine and organize this content. I'll add structure, clarity, and polish to make it even better!
      model: gpt-4o
      temperature: 0.6
      apiKey: '{{OPENAI_API_KEY}}'
    connections:
      success: agent-4-finalizer

  agent-4-finalizer:
    type: agent
    name: Agent 4 Finalizer
    inputs:
      systemPrompt: You are the final agent in a chain of 4. Create a comprehensive summary and conclusion based on all the previous agents' work.
      userPrompt: |-
        I'm the final agent! Here's the refined work from Agent 3:

        <agent3refiner.content>

        As the last agent in our chain, I'll create a final, polished summary that brings together all the work from our team of 4 agents. Let me conclude this beautifully!
      model: gpt-4o
      temperature: 0.5
      apiKey: '{{OPENAI_API_KEY}}'
```

## Router-Based Conditional Workflow

A workflow that uses routing logic to send data to different agents based on conditions:

```yaml
version: '1.0'
blocks:
  start:
    type: starter
    name: Start
    inputs:
      startWorkflow: manual
    connections:
      success: router-1

  router-1:
    type: router
    name: Router 1
    inputs:
      prompt: go to agent 1 if <start.input> is greater than 5. else agent 2 if greater than 10. else agent 3
      model: gpt-4o
      apiKey: '{{OPENAI_API_KEY}}'
    connections:
      success:
        - agent-1
        - agent-2
        - agent-3

  agent-1:
    type: agent
    name: Agent 1
    inputs:
      systemPrompt: say 1
      model: gpt-4o
      apiKey: '{{OPENAI_API_KEY}}'

  agent-2:
    type: agent
    name: Agent 2
    inputs:
      systemPrompt: say 2
      model: gpt-4o
      apiKey: '{{OPENAI_API_KEY}}'

  agent-3:
    type: agent
    name: Agent 3
    inputs:
      systemPrompt: say 3
      model: gpt-4o
      apiKey: '{{OPENAI_API_KEY}}'
```

## Web Search with Structured Output

A workflow that searches the web using tools and returns structured data:

```yaml
version: '1.0'
blocks:
  59eb07c1-1411-4b28-a274-fa78f55daf72:
    type: starter
    name: Start
    inputs:
      startWorkflow: manual
    connections:
      success: d77c2c98-56c4-432d-9338-9bac54a2d42f
  d77c2c98-56c4-432d-9338-9bac54a2d42f:
    type: agent
    name: Agent 1
    inputs:
      systemPrompt: look up the user input. use structured output
      userPrompt: <start.input>
      model: claude-sonnet-4-0
      apiKey: '{{ANTHROPIC_API_KEY}}'
      tools:
        - type: exa
          title: Exa
          params:
            type: auto
            apiKey: '{{EXA_API_KEY}}'
            numResults: ''
          toolId: exa_search
          operation: exa_search
          isExpanded: true
          usageControl: auto
      responseFormat: |-
        {
            "name": "output_schema",
            "description": "Defines the structure for an output object.",
            "strict": true,
            "schema": {
                "type": "object",
                "properties": {
                    "output": {
                        "type": "string",
                        "description": "The output value"
                    }
                },
                "additionalProperties": false,
                "required": ["output"]
            }
        }
```

## Loop Processing with Collection

A workflow that processes each item in a collection using a loop:

```yaml
version: '1.0'
blocks:
  start:
    type: starter
    name: Start
    inputs:
      startWorkflow: manual
    connections:
      success: food-analysis-loop
  food-analysis-loop:
    type: loop
    name: Food Analysis Loop
    inputs:
      count: 5
      loopType: forEach
      collection: '["apple", "banana", "carrot"]'
    connections:
      loop:
        start: calorie-agent
  calorie-agent:
    type: agent
    name: Calorie Analyzer
    inputs:
      systemPrompt: Return the number of calories in the food
      userPrompt: <loop.currentItem>
      model: claude-sonnet-4-0
      apiKey: '{{ANTHROPIC_API_KEY}}'
    parentId: food-analysis-loop
```

## Email Classification and Response

A workflow that classifies emails and generates appropriate responses:

```yaml
version: '1.0'
blocks:
  start:
    type: starter
    name: Start
    inputs:
      startWorkflow: manual
    connections:
      success: email-classifier

  email-classifier:
    type: agent
    name: Email Classifier
    inputs:
      systemPrompt: Classify emails into categories and extract key information.
      userPrompt: |
        Classify this email: <start.input>
        
        Categories: support, billing, sales, feedback
        Extract: urgency level, customer sentiment, main request
      model: gpt-4o
      apiKey: '{{OPENAI_API_KEY}}'
    connections:
      success: response-generator

  response-generator:
    type: agent
    name: Response Generator
    inputs:
      systemPrompt: Generate appropriate responses based on email classification.
      userPrompt: |
        Email classification: <emailclassifier.content>
        Original email: <start.input>
        
        Generate a professional, helpful response addressing the customer's needs.
      model: gpt-4o
      temperature: 0.7
      apiKey: '{{OPENAI_API_KEY}}'
```
