---
title: Router Block YAML Schema
description: YAML configuration reference for Router blocks
---

## Schema Definition

```yaml
type: object
required:
  - type
  - name
  - inputs
properties:
  type:
    type: string
    enum: [router]
    description: Block type identifier
  name:
    type: string
    description: Display name for this router block
  inputs:
    type: object
    required:
      - prompt
      - model
      - apiKey
    properties:
      prompt:
        type: string
        description: Instructions for routing decisions and criteria
      model:
        type: string
        description: AI model identifier (e.g., gpt-4o, gemini-2.5-pro, deepseek-chat)
      apiKey:
        type: string
        description: API key for the model provider (use {{ENV_VAR}} format)
      temperature:
        type: number
        minimum: 0
        maximum: 2
        description: Model temperature for routing decisions
        default: 0.3
      azureEndpoint:
        type: string
        description: Azure OpenAI endpoint URL (required for Azure models)
      azureApiVersion:
        type: string
        description: Azure API version (required for Azure models)
  connections:
    type: object
    description: Multiple connection paths for different routing outcomes
    properties:
      success:
        type: array
        items:
          type: string
        description: Array of target block IDs for routing destinations
```

## Connection Configuration

Router blocks use a success array containing all possible routing destinations:

```yaml
connections:
  success:
    - <string>                          # Target block ID option 1
    - <string>                          # Target block ID option 2
    - <string>                          # Target block ID option 3
    # Additional target block IDs as needed
```

## Examples

### Content Type Router

```yaml
content-router:
  type: router
  name: "Content Type Router"
  inputs:
    prompt: |
      Route this content based on its type:
      - If it's a question, route to question-handler
      - If it's a complaint, route to complaint-handler  
      - If it's feedback, route to feedback-handler
      - If it's a request, route to request-handler
      
      Content: <start.input>
    model: gpt-4o
    apiKey: '{{OPENAI_API_KEY}}'
  connections:
    success:
      - question-handler
      - complaint-handler
      - feedback-handler
      - request-handler
```

### Priority Router

```yaml
priority-router:
  type: router
  name: "Priority Router" 
  inputs:
    prompt: |
      Analyze the urgency and route accordingly:
      - urgent-queue: High priority, needs immediate attention
      - standard-queue: Normal priority, standard processing
      - low-queue: Low priority, can be delayed
      
      Email content: <email-analyzer.content>
      
      Route based on urgency indicators, deadlines, and tone.
    model: gpt-4o
    temperature: 0.2
    apiKey: '{{OPENAI_API_KEY}}'
  connections:
    success:
      - urgent-queue
      - standard-queue  
      - low-queue
```

### Department Router

```yaml
department-router:
  type: router
  name: "Department Router"
  inputs:
    prompt: |
      Route this customer inquiry to the appropriate department:
      
      - sales-team: Sales questions, pricing, demos
      - support-team: Technical issues, bug reports, how-to questions
      - billing-team: Payment issues, subscription changes, invoices
      - general-team: General inquiries, feedback, other topics
      
      Customer message: <start.input>
      Customer type: <customer-analyzer.type>
    model: claude-3-5-sonnet-20241022
    apiKey: '{{ANTHROPIC_API_KEY}}'
  connections:
    success:
      - sales-team
      - support-team
      - billing-team
      - general-team
```

## Advanced Configuration

### Multiple Models Router

```yaml
model-selector-router:
  type: router
  name: "Model Selection Router"
  inputs:
    prompt: |
      Based on the task complexity, route to the appropriate model:
      - simple-gpt35: Simple questions, basic tasks
      - advanced-gpt4: Complex analysis, detailed reasoning
      - specialized-claude: Creative writing, nuanced analysis
      
      Task: <start.task>
      Complexity indicators: <analyzer.complexity>
    model: gpt-4o-mini
    temperature: 0.1
    apiKey: '{{OPENAI_API_KEY}}'
  connections:
    success:
      - simple-gpt35
      - advanced-gpt4
      - specialized-claude
```

## Output References

Router blocks don't produce direct outputs but control workflow path:

```yaml
# Router decisions affect which subsequent blocks execute
# Access the routed block's outputs normally:
final-step:
  inputs:
    routed-result: <routed-block-name.content>
```

## Best Practices

- Provide clear routing criteria in the prompt
- Use specific, descriptive target block names
- Include examples of content for each routing path
- Use lower temperature values for consistent routing
- Test with diverse input types to ensure accurate routing
- Consider fallback paths for edge cases 