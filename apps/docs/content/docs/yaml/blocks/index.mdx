---
title: Block Schemas
description: Complete YAML schema reference for all Sim blocks
---

import { Card, Cards } from "fumadocs-ui/components/card";

This section contains the complete YAML schema definitions for all available block types in Sim. Each block type has specific configuration requirements and output formats.

## Core Blocks

These are the essential building blocks for creating workflows:

<Cards>
  <Card title="Starter Block" href="/yaml/blocks/starter">
    Workflow entry point supporting manual triggers, webhooks, and schedules
  </Card>
  <Card title="Agent Block" href="/yaml/blocks/agent">
    AI-powered processing with LLM integration and tool support
  </Card>
  <Card title="Function Block" href="/yaml/blocks/function">
    Custom JavaScript/TypeScript code execution environment
  </Card>
  <Card title="Response Block" href="/yaml/blocks/response">
    Format and return final workflow results
  </Card>
</Cards>

## Logic & Control Flow

Blocks for implementing conditional logic and control flow:

<Cards>
  <Card title="Condition Block" href="/yaml/blocks/condition">
    Conditional branching based on boolean expressions
  </Card>
  <Card title="Router Block" href="/yaml/blocks/router">
    AI-powered intelligent routing to multiple paths
  </Card>
  <Card title="Loop Block" href="/yaml/blocks/loop">
    Iterative processing with for and forEach loops
  </Card>
  <Card title="Parallel Block" href="/yaml/blocks/parallel">
    Concurrent execution across multiple instances
  </Card>
</Cards>

## Integration Blocks

Blocks for connecting to external services and systems:

<Cards>
  <Card title="API Block" href="/yaml/blocks/api">
    HTTP requests to external REST APIs
  </Card>
  <Card title="Webhook Block" href="/yaml/blocks/webhook">
    Webhook triggers for external integrations
  </Card>
</Cards>

## Advanced Blocks

Specialized blocks for complex workflow patterns:

<Cards>
  <Card title="Evaluator Block" href="/yaml/blocks/evaluator">
    Validate outputs against defined criteria and metrics
  </Card>
  <Card title="Workflow Block" href="/yaml/blocks/workflow">
    Execute other workflows as reusable components
  </Card>
</Cards>

## Common Schema Elements

All blocks share these common elements:

### Basic Structure

```yaml
block-id:
  type: <block-type>
  name: <display-name>
  inputs:
    # Block-specific configuration
  connections:
    # Connection definitions
```

### Connection Types

- **success**: Target block for successful execution
- **error**: Target block for error handling (optional)
- **conditions**: Multiple paths for conditional blocks

### Environment Variables

Use double curly braces for environment variables:

```yaml
inputs:
  apiKey: '{{API_KEY_NAME}}'
  endpoint: '{{SERVICE_ENDPOINT}}'
```

### Block References

Reference other block outputs using the block name in lowercase:

```yaml
inputs:
  userPrompt: <blockname.content>
  data: <functionblock.output>
  originalInput: <start.input>
```

## Validation Rules

All YAML blocks are validated against their schemas:

1. **Required fields**: Must be present
2. **Type validation**: Values must match expected types
3. **Enum validation**: String values must be from allowed lists
4. **Range validation**: Numbers must be within specified ranges
5. **Pattern validation**: Strings must match regex patterns (where applicable)

## Quick Reference

### Block Types and Properties

| Block Type | Primary Output | Common Use Cases |
|------------|----------------|------------------|
| starter | `.input` | Workflow entry point |
| agent | `.content` | AI processing, text generation |
| function | `.output` | Data transformation, calculations |
| api | `.output` | External service integration |
| condition | N/A (branching) | Conditional logic |
| router | N/A (branching) | Intelligent routing |
| response | N/A (terminal) | Final output formatting |
| loop | `.results` | Iterative processing |
| parallel | `.results` | Concurrent processing |
| webhook | `.payload` | External triggers |
| evaluator | `.score` | Output validation, quality assessment |
| workflow | `.output` | Sub-workflow execution, modularity |

### Required vs Optional

- **Always required**: `type`, `name`
- **Usually required**: `inputs`, `connections`
- **Context dependent**: Specific input fields vary by block type
- **Always optional**: `error` connections, UI-specific fields 