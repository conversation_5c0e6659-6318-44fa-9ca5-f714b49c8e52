---
title: Starter Block YAML Schema
description: YAML configuration reference for Starter blocks
---

## Schema Definition

```yaml
type: object
required:
  - type
  - name
properties:
  type:
    type: string
    enum: [starter]
    description: Block type identifier
  name:
    type: string
    description: Display name for this starter block
  inputs:
    type: object
    properties:
      startWorkflow:
        type: string
        enum: [manual, webhook, schedule]
        description: How the workflow should be triggered
        default: manual
      inputFormat:
        type: array
        description: Expected input structure for API calls (manual workflows)
        items:
          type: object
          properties:
            name:
              type: string
              description: Field name
            type:
              type: string
              enum: [string, number, boolean, object, array]
              description: Field type
      scheduleType:
        type: string
        enum: [hourly, daily, weekly, monthly]
        description: Schedule frequency (schedule workflows only)
      hourlyMinute:
        type: number
        minimum: 0
        maximum: 59
        description: Minute of the hour to run (hourly schedules)
      dailyTime:
        type: string
        pattern: "^([01]?[0-9]|2[0-3]):[0-5][0-9]$"
        description: Time of day to run in HH:MM format (daily schedules)
      weeklyDay:
        type: string
        enum: [MON, TUE, WED, THU, FRI, SAT, SUN]
        description: Day of week to run (weekly schedules)
      weeklyTime:
        type: string
        pattern: "^([01]?[0-9]|2[0-3]):[0-5][0-9]$"
        description: Time of day to run in HH:MM format (weekly schedules)
      monthlyDay:
        type: number
        minimum: 1
        maximum: 28
        description: Day of month to run (monthly schedules)
      monthlyTime:
        type: string
        pattern: "^([01]?[0-9]|2[0-3]):[0-5][0-9]$"
        description: Time of day to run in HH:MM format (monthly schedules)
      timezone:
        type: string
        description: Timezone for scheduled workflows
        default: UTC
      webhookProvider:
        type: string
        enum: [slack, gmail, airtable, telegram, generic, whatsapp, github, discord, stripe]
        description: Provider for webhook integration (webhook workflows only)
      webhookConfig:
        type: object
        description: Provider-specific webhook configuration
  connections:
    type: object
    properties:
      success:
        type: string
        description: Target block ID to execute when workflow starts
```

## Connection Configuration

The starter block only has a success connection since it's the entry point:

```yaml
connections:
  success: <string>                     # Target block ID to execute when workflow starts
```

## Examples

### Manual Start

```yaml
start:
  type: starter
  name: Start
  inputs:
    startWorkflow: manual
  connections:
    success: next-block
```

### Manual Start with Input Format

```yaml
start:
  type: starter
  name: Start
  inputs:
    startWorkflow: manual
    inputFormat:
      - name: query
        type: string
      - name: email
        type: string
      - name: age
        type: number
      - name: isActive
        type: boolean
      - name: preferences
        type: object
      - name: tags
        type: array
  connections:
    success: agent-1
```

### Daily Schedule

```yaml
start:
  type: starter
  name: Start
  inputs:
    startWorkflow: schedule
    scheduleType: daily
    dailyTime: "09:00"
    timezone: "America/New_York"
  connections:
    success: daily-task
```

### Weekly Schedule

```yaml
start:
  type: starter
  name: Start
  inputs:
    startWorkflow: schedule
    scheduleType: weekly
    weeklyDay: MON
    weeklyTime: "08:30"
    timezone: UTC
  connections:
    success: weekly-report
```

### Webhook Trigger

```yaml
start:
  type: starter
  name: Start
  inputs:
    startWorkflow: webhook
    webhookProvider: slack
    webhookConfig:
      # Provider-specific configuration
  connections:
    success: process-webhook
``` 