---
title: Vision
description: Analyze images with vision models
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="vision"
  color="#4D5FFF"
  icon={true}
  iconSvg={`<svg className="block-icon"
      
      fill='currentColor'
      
      
      viewBox='0 0 28 23'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M13.9999 6.51172C12.7047 6.51172 11.4625 7.02625 10.5466 7.94213C9.63074 8.858 9.11621 10.1002 9.11621 11.3954C9.11621 12.6907 9.63074 13.9329 10.5466 14.8488C11.4625 15.7646 12.7047 16.2792 13.9999 16.2792C15.2952 16.2792 16.5374 15.7646 17.4532 14.8488C18.3691 13.9329 18.8837 12.6907 18.8837 11.3954C18.8837 10.1002 18.3691 8.858 17.4532 7.94213C16.5374 7.02625 15.2952 6.51172 13.9999 6.51172ZM11.0697 11.3954C11.0697 10.6183 11.3784 9.87298 11.9279 9.32345C12.4775 8.77393 13.2228 8.46521 13.9999 8.46521C14.7771 8.46521 15.5224 8.77393 16.0719 9.32345C16.6214 9.87298 16.9302 10.6183 16.9302 11.3954C16.9302 12.1726 16.6214 12.9179 16.0719 13.4674C15.5224 14.017 14.7771 14.3257 13.9999 14.3257C13.2228 14.3257 12.4775 14.017 11.9279 13.4674C11.3784 12.9179 11.0697 12.1726 11.0697 11.3954Z'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M14 0C8.1213 0 4.16093 3.52149 1.86233 6.50772L1.82195 6.56112C1.30102 7.23702 0.82307 7.85823 0.498791 8.59274C0.15107 9.38065 0 10.2389 0 11.3953C0 12.5518 0.15107 13.41 0.498791 14.198C0.824372 14.9325 1.30233 15.555 1.82195 16.2296L1.86363 16.283C4.16093 19.2692 8.1213 22.7907 14 22.7907C19.8787 22.7907 23.8391 19.2692 26.1377 16.283L26.178 16.2296C26.699 15.555 27.1769 14.9325 27.5012 14.198C27.8489 13.41 28 12.5518 28 11.3953C28 10.2389 27.8489 9.38065 27.5012 8.59274C27.1756 7.85823 26.6977 7.23702 26.178 6.56112L26.1364 6.50772C23.8391 3.52149 19.8787 0 14 0ZM3.41209 7.69935C5.53228 4.94233 8.98605 1.95349 14 1.95349C19.014 1.95349 22.4664 4.94233 24.5879 7.69935C25.1609 8.44167 25.4943 8.88447 25.7144 9.38195C25.9202 9.84819 26.0465 10.4173 26.0465 11.3953C26.0465 12.3734 25.9202 12.9425 25.7144 13.4087C25.4943 13.9062 25.1596 14.349 24.5892 15.0913C22.4651 17.8484 19.014 20.8372 14 20.8372C8.98605 20.8372 5.53358 17.8484 3.41209 15.0913C2.83907 14.349 2.50567 13.9062 2.28558 13.4087C2.07981 12.9425 1.95349 12.3734 1.95349 11.3953C1.95349 10.4173 2.07981 9.84819 2.28558 9.38195C2.50567 8.88447 2.84167 8.44167 3.41209 7.69935Z'
      />
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
Vision is a tool that allows you to analyze images with vision models.

With Vision, you can:

- **Analyze images**: Analyze images with vision models
- **Extract text**: Extract text from images
- **Identify objects**: Identify objects in images
- **Describe images**: Describe images in detail
- **Generate images**: Generate images from text

In Sim, the Vision integration enables your agents to analyze images with vision models as part of their workflows. This allows for powerful automation scenarios that require analyzing images with vision models. Your agents can analyze images with vision models, extract text from images, identify objects in images, describe images in detail, and generate images from text. This integration bridges the gap between your AI workflows and your image analysis needs, enabling more sophisticated and image-centric automations. By connecting Sim with Vision, you can create agents that stay current with the latest information, provide more accurate responses, and deliver more value to users - all without requiring manual intervention or custom code.
{/* MANUAL-CONTENT-END */}


## Usage Instructions

Process visual content with customizable prompts to extract insights and information from images.



## Tools

### `vision_tool`

Process and analyze images using advanced vision models. Capable of understanding image content, extracting text, identifying objects, and providing detailed visual descriptions.

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `apiKey` | string | Yes | API key for the selected model provider |
| `imageUrl` | string | Yes | Publicly accessible image URL |
| `model` | string | No | Vision model to use \(gpt-4o, claude-3-opus-20240229, etc\) |
| `prompt` | string | No | Custom prompt for image analysis |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `content` | string | The analyzed content and description of the image |
| `model` | string | The vision model that was used for analysis |
| `tokens` | number | Total tokens used for the analysis |
| `usage` | object | Detailed token usage breakdown |



## Notes

- Category: `tools`
- Type: `vision`
