---
title: Thinking
description: Forces model to outline its thought process.
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="thinking"
  color="#181C1E"
  icon={true}
  iconSvg={`<svg className="block-icon"
      
      xmlns='http://www.w3.org/2000/svg'
      
      
      viewBox='0 0 24 24'
      fill='none'
      stroke='currentColor'
      strokeWidth='2'
      strokeLinecap='round'
      strokeLinejoin='round'
    >
      <path d='M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z' />
      <path d='M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z' />
      <path d='M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4' />
      <path d='M17.599 6.5a3 3 0 0 0 .399-1.375' />
      <path d='M6.003 5.125A3 3 0 0 0 6.401 6.5' />
      <path d='M3.477 10.896a4 4 0 0 1 .585-.396' />
      <path d='M19.938 10.5a4 4 0 0 1 .585.396' />
      <path d='M6 18a4 4 0 0 1-1.967-.516' />
      <path d='M19.967 17.484A4 4 0 0 1 18 18' />
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
The Thinking tool encourages AI models to engage in explicit reasoning before responding to complex queries. By providing a dedicated space for step-by-step analysis, this tool helps models break down problems, consider multiple perspectives, and arrive at more thoughtful conclusions.

Research has shown that prompting language models to "think step by step" can significantly improve their reasoning capabilities. According to [Anthropic's research on Claude's Think tool](https://www.anthropic.com/engineering/claude-think-tool), when models are given space to work through their reasoning explicitly, they demonstrate:

- **Improved problem-solving**: Breaking complex problems into manageable steps
- **Enhanced accuracy**: Reducing errors by carefully working through each component of a problem
- **Greater transparency**: Making the model's reasoning process visible and auditable
- **More nuanced responses**: Considering multiple angles before arriving at conclusions

In Sim, the Thinking tool creates a structured opportunity for your agents to engage in this kind of deliberate reasoning. By incorporating thinking steps into your workflows, you can help your agents tackle complex tasks more effectively, avoid common reasoning pitfalls, and produce higher-quality outputs. This is particularly valuable for tasks involving multi-step reasoning, complex decision-making, or situations where accuracy is critical.
{/* MANUAL-CONTENT-END */}


## Usage Instructions

Adds a step where the model explicitly outlines its thought process before proceeding. This can improve reasoning quality by encouraging step-by-step analysis.



## Tools

### `thinking_tool`

Processes a provided thought/instruction, making it available for subsequent steps.

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `thought` | string | Yes | The thought process or instruction provided by the user in the Thinking Step block. |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `acknowledgedThought` | string | The thought that was processed and acknowledged |



## Notes

- Category: `tools`
- Type: `thinking`
