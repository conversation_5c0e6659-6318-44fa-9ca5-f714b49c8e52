---
title: File
description: Read and parse multiple files
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="file"
  color="#40916C"
  icon={true}
  iconSvg={`<svg className="block-icon"
      
      
      
      viewBox='0 0 23 28'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M8 15.2H15.2M8 20H11.6M2 4.4V23.6C2 24.2365 2.25286 24.847 2.70294 25.2971C3.15303 25.7471 3.76348 26 4.4 26H18.8C19.4365 26 20.047 25.7471 20.4971 25.2971C20.9471 24.847 21.2 24.2365 21.2 23.6V9.6104C21.2 9.29067 21.136 8.97417 21.012 8.67949C20.8879 8.38481 20.7062 8.11789 20.4776 7.8944L15.1496 2.684C14.7012 2.24559 14.0991 2.00008 13.472 2H4.4C3.76348 2 3.15303 2.25286 2.70294 2.70294C2.25286 3.15303 2 3.76348 2 4.4Z'
        stroke='currentColor'
        strokeWidth='2.25'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M14 2V6.8C14 7.43652 14.2529 8.04697 14.7029 8.49706C15.153 8.94714 15.7635 9.2 16.4 9.2H21.2'
        stroke='currentColor'
        strokeWidth='2.25'
        strokeLinejoin='round'
      />
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
The File Parser tool provides a powerful way to extract and process content from various file formats, making it easy to incorporate document data into your agent workflows. This tool supports multiple file formats and can handle files up to 200MB in size.

With the File Parser, you can:

- **Process multiple file formats**: Extract text from PDFs, CSVs, Word documents (DOCX), text files, and more
- **Handle large files**: Process documents up to 200MB in size
- **Parse files from URLs**: Directly extract content from files hosted online by providing their URLs
- **Process multiple files at once**: Upload and parse several files in a single operation
- **Extract structured data**: Maintain formatting and structure from the original documents when possible

The File Parser tool is particularly useful for scenarios where your agents need to work with document content, such as analyzing reports, extracting data from spreadsheets, or processing text from various document sources. It simplifies the process of making document content available to your agents, allowing them to work with information stored in files just as easily as with direct text input.
{/* MANUAL-CONTENT-END */}


## Usage Instructions

Upload and extract contents from structured file formats including PDFs, CSV spreadsheets, and Word documents (DOCX). You can either provide a URL to a file or upload files directly. Specialized parsers extract text and metadata from each format. You can upload multiple files at once and access them individually or as a combined document.



## Tools

### `file_parser`

Parse one or more uploaded files or files from URLs (text, PDF, CSV, images, etc.)

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `filePath` | string | Yes | Path to the file\(s\). Can be a single path, URL, or an array of paths. |
| `fileType` | string | No | Type of file to parse \(auto-detected if not specified\) |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `files` | array | Array of parsed files |
| `combinedContent` | string | Combined content of all parsed files |



## Notes

- Category: `tools`
- Type: `file`
