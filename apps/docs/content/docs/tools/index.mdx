---
title: Tools
description: Powerful tools to enhance your agentic workflows
---

import { Card, Cards } from "fumadocs-ui/components/card";
import { Step, Steps } from "fumadocs-ui/components/steps";
import { Tab, Tabs } from "fumadocs-ui/components/tabs";

Tools are powerful components in Sim that allow your workflows to interact with external services, process data, and perform specialized tasks. They extend the capabilities of your agents and workflows by providing access to various APIs and services.

## What is a Tool?

A tool is a specialized component that provides a specific functionality or integration with external services. Tools can be used to search the web, interact with databases, process images, generate text or images, communicate via messaging platforms, and much more.

## Using Tools in Workflows

There are two primary ways to use tools in your Sim workflows:

<Steps>
  <Step>
    <strong>As Standalone Blocks</strong>: Tools can be added as individual
    blocks on the canvas when you need deterministic, direct access to their
    functionality. This gives you precise control over when and how the tool is
    called.
  </Step>
  <Step>
    <strong>As Agent Tools</strong>: Tools can be added to Agent blocks by
    clicking "Add tools" and configuring the required parameters. This allows
    agents to dynamically choose which tools to use based on the context and
    requirements of the task.
  </Step>
</Steps>

## Tool Configuration

Each tool requires specific configuration to function properly. Common configuration elements include:

- **API Keys**: Many tools require authentication through API keys
- **Connection Parameters**: Endpoints, database identifiers, etc.
- **Input Formatting**: How data should be structured for the tool
- **Output Handling**: How to process the results from the tool

## Available Tools

Sim provides a diverse collection of tools for various purposes, including:

- **AI and Language Processing**: OpenAI, ElevenLabs, Translation services
- **Search and Research**: Google Search, Tavily, Exa, Perplexity
- **Document Manipulation**: Google Docs, Google Sheets, Notion, Confluence
- **Media Processing**: Vision, Image Generator
- **Communication**: Slack, WhatsApp, Twilio SMS, Gmail
- **Data Storage**: Pinecone, Supabase, Airtable
- **Development**: GitHub

Each tool has its own dedicated documentation page with detailed instructions on configuration and usage.

## Tool Outputs

Tools typically return structured data that can be processed by subsequent blocks in your workflow. The format of this data varies depending on the tool and operation but generally includes:

- The main content or result
- Metadata about the operation
- Status information

Refer to each tool's specific documentation to understand its exact output format.

## YAML Configuration

For detailed YAML workflow configuration and syntax, see the [YAML Workflow Reference](/yaml) documentation. This includes comprehensive guides for:

- **Block Reference Syntax**: How to connect and reference data between blocks
- **Tool Configuration**: Using tools in both standalone blocks and agent configurations  
- **Environment Variables**: Secure handling of API keys and credentials
- **Complete Examples**: Real-world workflow patterns and configurations

For specific tool parameters and configuration options, refer to each tool's individual documentation page.
