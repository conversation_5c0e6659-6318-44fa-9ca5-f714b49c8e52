---
title: Firecrawl
description: Scrape or search the web
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="firecrawl"
  color="#181C1E"
  icon={true}
  iconSvg={`<svg className="block-icon" viewBox='0 0 642 600' xmlns='http://www.w3.org/2000/svg' >
      <path
        d='M301 63C299 91 303 122 298 149C295 158 289 165 283 169C274 172 266 170 261 167C253 176 248 183 244 191C230 226 226 263 226 301C216 310 203 317 192 310C179 295 175 277 174 259C161 273 153 288 146 304C141 321 138 336 137 352C140 372 145 388 152 402C161 421 174 435 187 449C181 462 165 453 157 450C158 454 161 458 165 461C195 490 231 500 268 509C240 494 211 471 195 442C179 413 172 378 180 344C191 353 200 362 211 364C223 365 232 361 236 353C247 274 299 214 323 143C322 136 327 140 329 142C354 165 367 191 375 218C387 254 381 294 379 329C393 345 413 334 424 329C429 342 432 352 429 362C427 378 417 388 413 400C422 407 433 403 440 400C432 423 419 442 404 460C383 483 358 501 335 512C379 502 420 491 449 459C443 458 427 464 428 452C443 437 464 423 472 403C482 383 485 362 484 339C482 307 472 280 458 254C459 267 452 276 445 284C434 289 426 279 424 272C415 247 424 220 418 198C415 179 405 165 397 150C370 114 336 86 303 64'
        fill='rgb(253,76,31)'
      />
      <path
        d='M324 141C303 214 249 273 244 354C235 359 229 364 223 366C205 367 193 357 182 347C180 350 179 353 180 357C178 374 178 390 182 403C185 421 193 434 200 448C212 465 227 480 243 491C258 500 269 513 285 512C284 508 257 485 252 468C241 450 235 433 233 414C241 415 254 420 263 412C260 387 265 363 273 343C281 323 293 306 310 295C317 289 324 285 330 282C328 307 328 331 329 355C330 368 332 379 338 389C358 394 376 384 388 370C383 386 377 401 371 415C376 414 381 411 385 408C383 421 380 431 376 441C366 467 356 491 334 510C358 499 381 483 400 461C418 442 430 423 440 403C432 404 421 410 413 404C414 386 428 377 427 360C429 349 428 340 424 332C413 336 404 341 392 339C386 338 381 334 379 330C380 292 385 248 371 214C366 195 358 180 349 165C341 155 333 145 323 140'
        fill='rgb(254,156,69)'
      />
      <path
        d='M330 284C309 293 289 311 279 332C267 356 261 383 265 411C256 420 242 418 235 412C237 438 245 459 258 479C269 493 281 507 295 513C288 495 265 472 265 446C272 447 281 454 288 444C296 425 303 407 309 388C317 406 321 427 336 443C346 449 358 446 363 438C355 464 348 489 334 511C344 501 352 491 357 480C370 457 379 435 385 412C380 411 376 416 371 418C376 401 382 386 387 371C379 382 369 388 358 391C348 394 337 392 334 383C324 353 328 316 330 285'
        fill='rgb(254,220,87)'
      />
      <path
        d='M311 389C303 407 297 426 289 445C282 454 273 450 268 445C267 472 285 492 302 512C299 514 297 514 294 514C297 514 299 514 301 514C314 515 325 512 334 513C341 495 355 467 362 443C357 446 351 448 344 447C337 446 334 441 330 438C320 422 316 406 311 391'
        fill='rgb(251,250,202)'
      />
      <path
        d='M187 163C188 181 167 187 164 203C158 215 158 228 159 241C172 233 183 221 188 209C193 194 192 178 188 166'
        fill='rgb(253,76,31)'
      />
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[Firecrawl](https://firecrawl.dev/) is a powerful web scraping and content extraction API that integrates seamlessly into Sim, enabling developers to extract clean, structured content from any website. This integration provides a simple way to transform web pages into usable data formats like Markdown and HTML while preserving the essential content.

With Firecrawl in Sim, you can:

- **Extract clean content**: Remove ads, navigation elements, and other distractions to get just the main content
- **Convert to structured formats**: Transform web pages into Markdown, HTML, or JSON
- **Capture metadata**: Extract SEO metadata, Open Graph tags, and other page information
- **Handle JavaScript-heavy sites**: Process content from modern web applications that rely on JavaScript
- **Filter content**: Focus on specific parts of a page using CSS selectors
- **Process at scale**: Handle high-volume scraping needs with a reliable API
- **Search the web**: Perform intelligent web searches and retrieve structured results
- **Crawl entire sites**: Crawl multiple pages from a website and aggregate their content

In Sim, the Firecrawl integration enables your agents to access and process web content programmatically as part of their workflows. Supported operations include:

- **Scrape**: Extract structured content (Markdown, HTML, metadata) from a single web page.
- **Search**: Search the web for information using Firecrawl's intelligent search capabilities.
- **Crawl**: Crawl multiple pages from a website, returning structured content and metadata for each page.

This allows your agents to gather information from websites, extract structured data, and use that information to make decisions or generate insights—all without having to navigate the complexities of raw HTML parsing or browser automation. Simply configure the Firecrawl block with your API key, select the operation (Scrape, Search, or Crawl), and provide the relevant parameters. Your agents can immediately begin working with web content in a clean, structured format.
{/* MANUAL-CONTENT-END */}


## Usage Instructions

Extract content from any website with advanced web scraping or search the web for information. Retrieve clean, structured data from web pages with options to focus on main content, or intelligently search for information across the web.



## Tools

### `firecrawl_scrape`

Extract structured content from web pages with comprehensive metadata support. Converts content to markdown or HTML while capturing SEO metadata, Open Graph tags, and page information.

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `url` | string | Yes | The URL to scrape content from |
| `scrapeOptions` | json | No | Options for content scraping |
| `apiKey` | string | Yes | Firecrawl API key |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `markdown` | string | Page content in markdown format |
| `html` | string | Raw HTML content of the page |
| `metadata` | object | Page metadata including SEO and Open Graph information |

### `firecrawl_search`

Search for information on the web using Firecrawl

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `query` | string | Yes | The search query to use |
| `apiKey` | string | Yes | Firecrawl API key |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `data` | array | Search results data |

### `firecrawl_crawl`

Crawl entire websites and extract structured content from all accessible pages

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `url` | string | Yes | The website URL to crawl |
| `limit` | number | No | Maximum number of pages to crawl \(default: 100\) |
| `onlyMainContent` | boolean | No | Extract only main content from pages |
| `apiKey` | string | Yes | Firecrawl API Key |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `pages` | array | Array of crawled pages with their content and metadata |



## Notes

- Category: `tools`
- Type: `firecrawl`
