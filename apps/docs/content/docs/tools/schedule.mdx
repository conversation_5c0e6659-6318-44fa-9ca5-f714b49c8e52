---
title: Schedule
description: Trigger workflow execution on a schedule
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="schedule"
  color="#7B68EE"
  icon={true}
  iconSvg={`<svg className="block-icon"
      
      xmlns='http://www.w3.org/2000/svg'
      
      
      viewBox='0 0 24 24'
      fill='none'
      stroke='currentColor'
      strokeWidth='2'
      strokeLinecap='round'
      strokeLinejoin='round'
    >
      <path d='M8 2v4' />
      <path d='M16 2v4' />
      <rect   x='3' y='4' rx='2' />
      <path d='M3 10h18' />
    </svg>`}
/>

## Usage Instructions

Configure automated workflow execution with flexible timing options. Set up recurring workflows that run at specific intervals or times.





## Notes

- Category: `triggers`
- Type: `schedule`
