---
title: Connections
description: Connect your blocks to one another.
---

import { Callout } from 'fumadocs-ui/components/callout'
import { Card, Cards } from 'fumadocs-ui/components/card'
import { ConnectIcon } from '@/components/icons'
import { Video } from '@/components/ui/video'

Connections are the pathways that allow data to flow between blocks in your workflow. They define how information is passed from one block to another, enabling you to create sophisticated, multi-step processes.

<Callout type="info">
  Properly configured connections are essential for creating effective workflows. They determine how
  data moves through your system and how blocks interact with each other.
</Callout>

<div className="mx-auto w-full overflow-hidden rounded-lg">
  <Video src="connections.mp4" />
</div>

## Connection Types

Sim supports different types of connections that enable various workflow patterns:

<Cards>
  <Card title="Connection Basics" href="/connections/basics">
    Learn how connections work and how to create them in your workflows
  </Card>
  <Card title="Connection Tags" href="/connections/tags">
    Understand how to use connection tags to reference data between blocks
  </Card>
  <Card title="Data Structure" href="/connections/data-structure">
    Explore the output data structures of different block types
  </Card>
  <Card title="Accessing Data" href="/connections/accessing-data">
    Learn techniques for accessing and manipulating connected data
  </Card>
  <Card title="Best Practices" href="/connections/best-practices">
    Follow recommended patterns for effective connection management
  </Card>
</Cards>
