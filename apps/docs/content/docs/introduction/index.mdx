---
title: Introduction
description: Build AI workflows visually without code
---

import { Card, Cards } from 'fumadocs-ui/components/card'
import { File, Files, Folder } from 'fumadocs-ui/components/files'
import { Features } from '@/components/ui/features'

Sim is a visual workflow editor that enables you to build AI-powered applications by connecting blocks on a canvas. Drag and drop components to create chatbots, automation workflows, and data processing pipelines without writing code.

## What Makes Sim Powerful

**Multi-Model AI Support** - Connect to OpenAI, Anthropic, Google, Groq, Cerebras, and local models through Ollama. Switch providers without rebuilding workflows.

**60+ Pre-Built Tools** - Gmail, Slack, Notion, Google Sheets, Airtable, Supabase, Pinecone, and more. Extensible architecture allows custom tool integration.

**Flexible Execution** - Run workflows via chat interface, REST API, webhooks, scheduled jobs, or trigger from external systems.

**Production Deployment** - Deploy as APIs, integrate with existing systems using our SDK, or embed as plugins. Built-in monitoring, logging, and error handling.

**Real-time Collaboration** - Work simultaneously with team members on the same workflow, like Google Docs for AI development.

Connect multiple AI models, integrate with 60+ services, and deploy production-ready applications through an intuitive visual interface designed specifically for AI development.

## Core Building Blocks

**Processing Blocks**

- **Agent** - Execute AI model inference with any LLM provider
- **API** - Connect to REST endpoints and external services  
- **Function** - Run custom JavaScript for data processing

**Logic Blocks**

- **Condition** - Create branching logic based on data evaluation
- **Router** - Route execution paths using AI-powered decision making
- **Loop** - Iterate over collections sequentially
- **Parallel** - Execute multiple operations concurrently

**Output Blocks**

- **Response** - Format and return final workflow results
- **Evaluator** - Validate outputs against defined criteria

## Built-in Integrations

**AI Models**: OpenAI, Anthropic, Google, Groq, Cerebras, Ollama

**Communication**: Gmail, Slack, Telegram, WhatsApp, Microsoft Teams

**Data Sources**: Notion, Google Sheets, Airtable, Supabase, Pinecone

**Web Services**: Firecrawl, Google Search, Exa AI, Perplexity

**Development**: GitHub, Jira, Linear, browser automation

## Use Cases

**AI Assistants** - Build chatbots with web search, calendar access, and email capabilities

**Content Generation** - Create blog posts, social media content, and marketing materials

**Data Processing** - Extract insights from documents, analyze datasets, and generate reports

**Process Automation** - Automate business workflows with event-driven triggers

**API Orchestration** - Combine multiple services into unified endpoints

## Key Features

**Multi-Provider AI Support** - Switch between OpenAI, Anthropic, Google, and local models without rebuilding workflows

**Real-time Collaboration** - Work simultaneously with team members on the same workflow

**Production-Ready** - Built-in error handling, logging, and monitoring for production deployments

**Local Development** - Test with Ollama locally, then deploy with cloud providers

## Getting Started

Ready to build your first workflow? Our [Getting Started guide](/getting-started) will walk you through creating a customer support assistant in under 10 minutes.

<Cards>
  <Card title="Getting Started" href="/getting-started">
    Build your first workflow
  </Card>
  <Card title="Blocks" href="/blocks">
    Learn about workflow components
  </Card>
  <Card title="Tools" href="/tools">
    Explore integrations
  </Card>
</Cards>
