---
title: Execution Basics
description: Understanding the fundamental execution flow in Sim
---

import { Callout } from 'fumadocs-ui/components/callout'
import { File, Files, Folder } from 'fumadocs-ui/components/files'
import { Step, Steps } from 'fumadocs-ui/components/steps'
import { Tab, Tabs } from 'fumadocs-ui/components/tabs'
import { ThemeImage } from '@/components/ui/theme-image'
import {
  AgentIcon,
  ApiIcon,
  ChartBarIcon,
  CodeIcon,
  ConditionalIcon,
  ConnectIcon,
  ResponseIcon,
  StarterIcon,
  LoopIcon,
  ParallelIcon,
} from '@/components/icons'
import { Video } from '@/components/ui/video'

When you run a workflow in Sim, the execution engine follows a systematic process to ensure blocks are executed in the correct order with proper data flow.

## Execution Flow

The execution engine runs workflows in layers, processing blocks based on their dependencies:

<Steps>
  <Step>
    <strong>Validation</strong>: Ensures the workflow has a starter block with no incoming connections and all blocks are properly connected.
  </Step>

  <Step>
    <strong>Layer-based Execution</strong>: Identifies which blocks can execute next based on completed dependencies and executes them in parallel.
  </Step>

  <Step>
    <strong>Path Updates</strong>: Router and Condition blocks update the active execution path, determining which blocks execute next.
  </Step>

  <Step>
    <strong>Iteration Processing</strong>: Loop and Parallel blocks manage iterations and create virtual instances for concurrent execution.
  </Step>

  <Step>
    <strong>Result Collection</strong>: Outputs from the final blocks are collected and returned as the workflow result.
  </Step>
</Steps>

## Block Types and Execution

Different block types have different execution behaviors:

<Tabs items={['Orchestration Blocks', 'Processing Blocks', 'Integration Blocks']}>
  <Tab>
    <Card>
      Orchestration blocks control the flow of execution through your workflow.
      <Files>
        <File
          name="Starter Block"
          icon={<StarterIcon className="h-4 w-4" />}
          annotation="Initiates workflow execution and provides initial input values. Every workflow must have exactly one starter block."
        />
        <File
          name="Router Block"
          icon={<ConnectIcon className="h-4 w-4" />}
          annotation="Directs execution along specific paths based on dynamic decisions. Uses an AI model to select one of multiple possible paths."
        />
        <File
          name="Condition Block"
          icon={<ConditionalIcon className="h-4 w-4" />}
          annotation="Executes different paths based on conditional logic. Evaluates JavaScript expressions to determine which path to follow."
        />
        <File
          name="Loop Block"
          icon={<LoopIcon className="h-4 w-4" />}
          annotation="Executes blocks repeatedly for a fixed number of iterations or over a collection. Manages iteration state and provides access to current item."
        />
        <File
          name="Parallel Block"
          icon={<ParallelIcon className="h-4 w-4" />}
          annotation="Executes blocks concurrently across multiple instances. Distributes work based on count or collection for faster processing."
        />
      </Files>
    </Card>
  </Tab>

<Tab>
  <Card>
    Processing blocks transform data and generate new outputs.
    <Files>
      <File
        name="Agent Block"
        icon={<AgentIcon className="h-4 w-4" />}
        annotation="Interacts with AI models to generate content. Executes prompts against various LLM providers."
      />
      <File
        name="Function Block"
        icon={<CodeIcon className="h-4 w-4" />}
        annotation="Executes custom JavaScript/TypeScript code. Runs in a secure sandbox environment with access to connected block outputs."
      />
      <File
        name="Evaluator Block"
        icon={<ChartBarIcon className="h-4 w-4" />}
        annotation="Assesses outputs against defined criteria. Uses AI to evaluate content based on custom metrics."
      />
      <File
        name="Workflow Block"
        icon={<ConnectIcon className="h-4 w-4" />}
        annotation="Execute nested workflows as a single block. Allows modular workflow design by embedding one workflow inside another."
      />
    </Files>
  </Card>
</Tab>

  <Tab>
    <Card>
      Integration blocks connect with external systems.
      <Files>
        <File
          name="API Block"
          icon={<ApiIcon className="h-4 w-4" />}
          annotation="Makes HTTP requests to external services. Configurable with headers, body, and authentication."
        />
        <File
          name="Knowledge Base Block"
          icon={
            <svg className="h-4 w-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2L3 9V20C3 20.55 3.45 21 4 21H9V14H15V21H20C20.55 21 21 20.55 21 20V9L12 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <circle cx="12" cy="10" r="2" stroke="currentColor" strokeWidth="2"/>
            </svg>
          }
          annotation="Search and interact with knowledge bases. Performs semantic search, retrieves documents, and manages knowledge data."
        />
        <File
          name="Response Block"
          icon={<ResponseIcon className="h-4 w-4" />}
          annotation="Format and return responses from workflows. Configure response data, status codes, and headers."
        />
        <File
          name="Tool Blocks"
          icon={<CodeIcon className="h-4 w-4" />}
          annotation="Specialized blocks for specific services (Gmail, Slack, GitHub, etc.). Each has its own execution logic for the specific service."
        />
      </Files>
    </Card>
  </Tab>
</Tabs>

## Execution Methods

Sim offers multiple ways to trigger workflow execution:

### Manual Execution

Run workflows on-demand through the Sim interface by clicking the "Run" button. This is perfect for:

- Testing during development
- One-off tasks
- Workflows that need human supervision

<div className="mx-auto w-full overflow-hidden rounded-lg">
  <Video src="input-format.mp4" />
</div>

### Scheduled Execution

Configure workflows to run automatically on a specified schedule:

- Set up recurring executions using cron expressions
- Define start times and frequency
- Configure timezone settings
- Set minimum and maximum execution intervals

<div className="mx-auto w-full overflow-hidden rounded-lg">
  <Video src="configure-schedule.mp4" />
</div>

### API Endpoints

Each workflow can be exposed as an API endpoint:

- Get a unique URL for your workflow
- Configure authentication requirements
- Send custom inputs via POST requests
- Receive execution results as JSON responses

<div className="mx-auto w-full overflow-hidden rounded-lg">
  <Video src="api-deployment.mp4" />
</div>

#### Viewing Deployed APIs

Monitor your deployed workflow APIs and their current state:

<div className="mx-auto w-full overflow-hidden rounded-lg">
  <Video src="api-redeployment.mp4" />
</div>

This shows how to view the deployed state and compare with the original deployed API configuration.

### Webhooks

Configure workflows to execute in response to external events:

- Set up webhook triggers from third-party services
- Process incoming webhook data as workflow input
- Configure webhook security settings
- Support for specialized webhooks (GitHub, Stripe, etc.)

<div className="mx-auto w-full overflow-hidden rounded-lg">
  <Video src="webhooks.mp4" />
</div>

<Callout type="info">
  The execution method you choose depends on your workflow's purpose. Manual execution is great for
  development, while scheduled execution, API endpoints, and webhooks are better for production use
  cases.
</Callout>

## Execution Context

Each workflow execution maintains a context that tracks:

- **Block States**: Outputs and execution status of each block
- **Execution Path**: Active blocks based on routing decisions
- **Loop/Parallel State**: Current iterations and distribution items
- **Environment Variables**: Configuration values available during execution
- **Execution Logs**: Detailed records of each block's execution

## Real-Time Monitoring

Monitor your workflow execution in real-time:

- **Active Block Highlighting**: Currently executing blocks pulse with animation
- **Live Logs**: Execution logs appear instantly in the logs panel
- **Block States**: Visual indicators show success, error, or pending states
- **Performance Metrics**: Execution time for each block

These monitoring features help you understand workflow behavior and quickly identify any issues.
