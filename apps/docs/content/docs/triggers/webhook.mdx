---
title: Webhooks  
description: Trigger workflow execution from external webhooks
---

import { Callout } from 'fumadocs-ui/components/callout'
import { Tab, Tabs } from 'fumadocs-ui/components/tabs'
import { ThemeImage } from '@/components/ui/theme-image'
import { Video } from '@/components/ui/video'

The Webhook block allows external services to automatically trigger your workflow execution through HTTP webhooks.

<div className="mx-auto w-full overflow-hidden rounded-lg">
  <Video src="webhooks.mp4" />
</div>

## Supported Providers

Choose from the dropdown to configure your webhook source:

<Tabs items={['Popular Services', 'Generic']}>
  <Tab>
    <ul className="grid grid-cols-2 gap-1 text-sm">
      <li>**Slack** - Bot events and messages</li>
      <li>**Gmail** - Email notifications</li>
      <li>**GitHub** - Repository events</li>
      <li>**Discord** - Server events</li>
      <li>**Airtable** - Database changes</li>
      <li>**Telegram** - Bot messages</li>
      <li>**WhatsApp** - Messaging events</li>
      <li>**Stripe** - Payment events</li>
    </ul>
  </Tab>
  <Tab>
    <p>For custom integrations:</p>
    <ul className="list-disc space-y-1 pl-6 text-sm">
      <li><strong>HTTP POST</strong>: Accepts requests from any client</li>
      <li><strong>Authentication</strong>: Bearer token or custom headers</li>
      <li><strong>Security</strong>: IP restrictions and rate limiting</li>
      <li><strong>Deduplication</strong>: Prevents duplicate requests</li>
    </ul>
  </Tab>
</Tabs>

## How It Works

1. **Configure Provider** - Select from dropdown and set up authentication
2. **Get Webhook URL** - Automatically generated unique endpoint  
3. **External Service** - Sends HTTP POST to your webhook URL
4. **Workflow Triggers** - Automatically starts when webhook is received

<Callout>
Webhooks cannot receive incoming connections and serve as pure workflow triggers.
</Callout>