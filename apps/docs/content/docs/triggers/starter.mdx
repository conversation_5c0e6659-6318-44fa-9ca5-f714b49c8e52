---
title: Starter
description: Manually initiate workflow execution with input parameters
---

import { Callout } from 'fumadocs-ui/components/callout'
import { Tab, Tabs } from 'fumadocs-ui/components/tabs'
import { ThemeImage } from '@/components/ui/theme-image'

The Starter block allows manual workflow execution with two input modes: structured parameters or conversational chat.

<ThemeImage
  lightSrc="/static/light/starter-light.png"
  darkSrc="/static/dark/starter-dark.png"
  alt="Starter Block with Manual and Chat Mode Options"
  width={350}
  height={175}
/>

## Execution Modes

Choose your input method from the dropdown:

<Tabs items={['Manual Mode', 'Chat Mode']}>
  <Tab>
    <div className="space-y-4">
      <ul className="list-disc space-y-1 pl-6">
        <li><strong>Structured inputs</strong>: Define specific parameters (text, number, boolean, JSON, file, date)</li>
        <li><strong>Form interface</strong>: Users fill out a form with predefined fields</li>
        <li><strong>API friendly</strong>: Perfect for programmatic execution</li>
      </ul>
      
      <div className="mx-auto w-full overflow-hidden rounded-lg">
        <video autoPlay loop muted playsInline className="w-full -mb-2 rounded-lg" src="/input-format.mp4"></video>
      </div>
      
      <p className="text-sm text-gray-600">Configure input parameters that will be available when deploying as an API endpoint.</p>
    </div>
  </Tab>
  <Tab>
    <div className="space-y-4">
      <ul className="list-disc space-y-1 pl-6">
        <li><strong>Natural language</strong>: Users type questions or requests</li>
        <li><strong>start.input variable</strong>: Captures all user input as `<start.input>`</li>
        <li><strong>start.conversationId</strong>: Access conversation ID as `<start.conversationId>`</li>
        <li><strong>Conversational</strong>: Ideal for AI-powered workflows</li>
      </ul>
      
      <div className="mx-auto w-full overflow-hidden rounded-lg">
        <video autoPlay loop muted playsInline className="w-full -mb-2 rounded-lg" src="/chat-input.mp4"></video>
      </div>
      
      <p className="text-sm text-gray-600">Chat with your workflow and access input text, conversation ID, and uploaded files for context-aware responses.</p>
    </div>
  </Tab>
</Tabs>

## Using Chat Variables

In Chat mode, access user input and conversation context through special variables:

```yaml
# Reference the chat input, conversation ID, and files in your workflow
user_message: "<start.input>"
conversation_id: "<start.conversationId>"
uploaded_files: "<start.files>"
```

- **`<start.input>`** - Contains the user's message text
- **`<start.conversationId>`** - Unique identifier for the conversation thread
- **`<start.files>`** - Array of files uploaded by the user (if any)

## API Execution

<Tabs items={['Manual Mode', 'Chat Mode']}>
  <Tab>
    ```bash
    curl -X POST "https://api.sim.dev/v1/workflows/{id}/start" \
      -H "Authorization: Bearer {api-key}" \
      -d '{"parameters": {"userId": "123", "action": "process"}}'
    ```
  </Tab>
  <Tab>
    ```bash
    curl -X POST "https://api.sim.dev/v1/workflows/{id}/start" \
      -H "Authorization: Bearer {api-key}" \
      -d '{"input": "Analyze Q4 sales data"}'
    ```
  </Tab>
</Tabs>

<Callout>
Starter blocks are ideal for testing workflows and user-initiated tasks. For automated execution, use Schedule or Webhook triggers.
</Callout>