---
title: Workflow
description: Execute other workflows as reusable components within your current workflow
---

import { Callout } from 'fumadocs-ui/components/callout'
import { Step, Steps } from 'fumadocs-ui/components/steps'
import { Tab, Tabs } from 'fumadocs-ui/components/tabs'
import { ThemeImage } from '@/components/ui/theme-image'

The Workflow block allows you to execute other workflows as reusable components within your current workflow. This powerful feature enables modular design, code reuse, and the creation of complex nested workflows that can be composed from smaller, focused workflows.

<ThemeImage
  lightSrc="/static/light/workflow-light.png"
  darkSrc="/static/dark/workflow-dark.png"
  alt="Workflow Block"
  width={300}
  height={175}
/>

<Callout type="info">
  Workflow blocks enable modular design by allowing you to compose complex workflows from smaller, reusable components.
</Callout>

## Overview

The Workflow block serves as a bridge between workflows, enabling you to:

<Steps>
  <Step>
    <strong>Reuse existing workflows</strong>: Execute previously created workflows as components within new workflows
  </Step>
  <Step>
    <strong>Create modular designs</strong>: Break down complex processes into smaller, manageable workflows
  </Step>
  <Step>
    <strong>Maintain separation of concerns</strong>: Keep different business logic isolated in separate workflows
  </Step>
  <Step>
    <strong>Enable team collaboration</strong>: Share and reuse workflows across different projects and team members
  </Step>
</Steps>

## How It Works

The Workflow block:

1. Takes a reference to another workflow in your workspace
2. Passes input data from the current workflow to the child workflow
3. Executes the child workflow in an isolated context
4. Returns the results back to the parent workflow for further processing

## Configuration Options

### Workflow Selection

Choose which workflow to execute from a dropdown list of available workflows in your workspace. The list includes:

- All workflows you have access to in the current workspace
- Workflows shared with you by other team members
- Both enabled and disabled workflows (though only enabled workflows can be executed)

### Input Data

Define the data to pass to the child workflow:

- **Single Variable Input**: Select a variable or block output to pass to the child workflow
- **Variable References**: Use `<variable.name>` to reference workflow variables
- **Block References**: Use `<blockName.field>` to reference outputs from previous blocks
- **Automatic Mapping**: The selected data is automatically available as `start.input` in the child workflow
- **Optional**: The input field is optional - child workflows can run without input data
- **Type Preservation**: Variable types (strings, numbers, objects, etc.) are preserved when passed to the child workflow

### Accessing Results

After a workflow executes, you can access its outputs:

- **`<workflow.response>`**: The complete output from the child workflow
- **`<workflow.name>`**: The name of the executed child workflow
- **`<workflow.success>`**: Boolean indicating successful completion
- **`<workflow.error>`**: Error details if the workflow failed
- **`<workflow.execution_time>`**: Time taken to execute the workflow

### Execution Context

The child workflow executes with:

- Its own isolated execution context
- Access to the same workspace resources (API keys, environment variables)
- Proper workspace membership and permission checks
- Independent logging and monitoring

## Safety and Limitations

To prevent infinite recursion and ensure system stability, the Workflow block includes several safety mechanisms:

<Callout type="warning">
  **Cycle Detection**: The system automatically detects and prevents circular dependencies between workflows to avoid infinite loops.
</Callout>

- **Maximum Depth Limit**: Nested workflows are limited to a maximum depth of 10 levels
- **Cycle Detection**: Automatic detection and prevention of circular workflow dependencies
- **Timeout Protection**: Child workflows inherit timeout settings to prevent indefinite execution
- **Resource Limits**: Memory and execution time limits apply to prevent resource exhaustion

## Advanced Features

### Dynamic Workflow Selection

Select workflows dynamically based on runtime conditions:

```javascript
// In a Function block before the Workflow block
const workflowId = <condition.result> ? 'premium-workflow' : 'standard-workflow';
return { selectedWorkflow: workflowId };
```

### Error Handling and Fallbacks

Implement robust error handling for child workflows:

```javascript
// In a Function block after the Workflow block
if (!<workflow.success>) {
  console.error('Child workflow failed:', <workflow.error>);
  // Implement fallback logic
  return { fallback: true, error: <workflow.error> };
}
return <workflow.response>;
```

### Workflow Chaining

Chain multiple workflows together:

```javascript
// Pass output from one workflow to another
Workflow 1 Input: <start.input>
Workflow 2 Input: <workflow1.response>
Workflow 3 Input: <workflow2.response>
```

## Inputs and Outputs

<Tabs items={['Configuration', 'Variables', 'Results']}>
  <Tab>
    <ul className="list-disc space-y-2 pl-6">
      <li>
        <strong>Workflow Selection</strong>: Choose which workflow to execute
      </li>
      <li>
        <strong>Input Data</strong>: Variable or block reference to pass to child workflow
      </li>
      <li>
        <strong>Execution Context</strong>: Isolated environment with workspace resources
      </li>
    </ul>
  </Tab>
  <Tab>
    <ul className="list-disc space-y-2 pl-6">
      <li>
        <strong>workflow.response</strong>: Complete output from child workflow
      </li>
      <li>
        <strong>workflow.name</strong>: Name of executed child workflow
      </li>
      <li>
        <strong>workflow.success</strong>: Boolean indicating completion status
      </li>
      <li>
        <strong>workflow.error</strong>: Error details if workflow failed
      </li>
      <li>
        <strong>workflow.execution_time</strong>: Time taken to execute
      </li>
    </ul>
  </Tab>
  <Tab>
    <ul className="list-disc space-y-2 pl-6">
      <li>
        <strong>Workflow Response</strong>: Primary output from child workflow
      </li>
      <li>
        <strong>Execution Status</strong>: Success status and error information
      </li>
      <li>
        <strong>Access</strong>: Available in blocks after the workflow
      </li>
    </ul>
  </Tab>
</Tabs>

## Example Use Cases

### Modular Customer Onboarding

<div className="mb-4 rounded-md border p-4">
  <h4 className="font-medium">Scenario: Break down complex onboarding into reusable components</h4>
  <ol className="list-decimal pl-5 text-sm">
    <li>Main workflow receives customer data</li>
    <li>Workflow block executes validation workflow</li>
    <li>Workflow block executes account setup workflow</li>
    <li>Workflow block executes welcome email workflow</li>
  </ol>
</div>

### Microservice Architecture

<div className="mb-4 rounded-md border p-4">
  <h4 className="font-medium">Scenario: Create independent service workflows</h4>
  <ol className="list-decimal pl-5 text-sm">
    <li>Payment processing workflow handles transactions</li>
    <li>Inventory management workflow updates stock</li>
    <li>Notification workflow sends confirmations</li>
    <li>Main workflow orchestrates all services</li>
  </ol>
</div>

### Conditional Processing

<div className="mb-4 rounded-md border p-4">
  <h4 className="font-medium">Scenario: Execute different workflows based on conditions</h4>
  <ol className="list-decimal pl-5 text-sm">
    <li>Condition block evaluates user type</li>
    <li>Enterprise users → Complex approval workflow</li>
    <li>Standard users → Simple approval workflow</li>
    <li>Free users → Basic processing workflow</li>
  </ol>
</div>

### Example: Customer Validation Workflow

```javascript title="validation-workflow.js"
// Main workflow passes customer data to validation workflow
const customerData = <start.input>;

// Validation workflow processes the data
const emailValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(customerData.email);
const phoneValid = /^\+?[1-9]\d{1,14}$/.test(customerData.phone);

return {
  customer: customerData,
  validation: {
    email: emailValid,
    phone: phoneValid,
    overall: emailValid && phoneValid
  }
};
```

## Best Practices

- **Keep workflows focused**: Design child workflows to handle specific, well-defined tasks with clear inputs and outputs
- **Minimize nesting depth**: Avoid deeply nested workflow hierarchies for better maintainability and performance
- **Handle errors gracefully**: Implement proper error handling for child workflow failures and provide fallback mechanisms
- **Document dependencies**: Clearly document which workflows depend on others and maintain dependency maps
- **Test independently**: Ensure child workflows can be tested and validated independently from parent workflows
- **Monitor performance**: Be aware that nested workflows can impact overall execution time and resource usage
- **Use semantic naming**: Give workflows descriptive names that clearly indicate their purpose and functionality
