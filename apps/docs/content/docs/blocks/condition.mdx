---
title: Condition
description: Create conditional logic and branching in your workflows
---

import { Callout } from 'fumadocs-ui/components/callout'
import { Step, Steps } from 'fumadocs-ui/components/steps'
import { Tab, Tabs } from 'fumadocs-ui/components/tabs'
import { Accordion, Accordions } from 'fumadocs-ui/components/accordion'
import { ThemeImage } from '@/components/ui/theme-image'

The Condition block allows you to branch your workflow execution path based on boolean expressions. It evaluates conditions and routes the workflow accordingly, enabling you to create dynamic, responsive workflows with different execution paths.

<ThemeImage
  lightSrc="/static/light/condition-light.png"
  darkSrc="/static/dark/condition-dark.png"
  alt="Condition Block"
  width={350}
  height={175}
/>

<Callout>
  Condition blocks enable deterministic decision-making without requiring an LLM, making them ideal
  for straightforward branching logic.
</Callout>

## Overview

The Condition block enables you to:

<Steps>
  <Step>
    <strong>Create branching logic</strong>: Route workflows based on boolean expressions
  </Step>
  <Step>
    <strong>Make data-driven decisions</strong>: Evaluate conditions using previous block outputs
  </Step>
  <Step>
    <strong>Handle multiple scenarios</strong>: Define multiple conditions with different paths
  </Step>
  <Step>
    <strong>Provide deterministic routing</strong>: Make decisions without requiring an LLM
  </Step>
</Steps>

## How It Works

The Condition block operates through a sequential evaluation process:

1. **Evaluate Expression** - Processes the JavaScript/TypeScript boolean expression using current workflow data
2. **Determine Result** - Returns true or false based on the expression evaluation
3. **Route Workflow** - Directs execution to the appropriate destination block based on the result  
4. **Provide Context** - Generates metadata about the decision for debugging and monitoring

## Configuration Options

### Conditions

Define one or more conditions that will be evaluated. Each condition includes:

- **Expression**: A JavaScript/TypeScript expression that evaluates to true or false
- **Path**: The destination block to route to if the condition is true
- **Description**: Optional explanation of what the condition checks

You can create multiple conditions that are evaluated in order, with the first matching condition determining the execution path.

### Condition Expression Format

Conditions use JavaScript syntax and can reference input values from previous blocks.

<Tabs items={['Score Threshold', 'Text Analysis', 'Multiple Conditions']}>
  <Tab>
    ```javascript
    // Check if a score is above a threshold
    <agent.score> > 75
    ```
  </Tab>
  <Tab>
    ```javascript
    // Check if a text contains specific keywords
    <agent.text>.includes('urgent') || <agent.text>.includes('emergency')
    ```
  </Tab>
  <Tab>
    ```javascript
    // Check multiple conditions
    <agent.age> >= 18 && <agent.country> === 'US'
    ```
  </Tab>
</Tabs>


### Accessing Results

After a condition evaluates, you can access its outputs:

- **`<condition.result>`**: Boolean result of the condition evaluation
- **`<condition.matched_condition>`**: ID of the condition that was matched
- **`<condition.content>`**: Description of the evaluation result
- **`<condition.path>`**: Details of the chosen routing destination

## Advanced Features

### Complex Expressions

Use JavaScript operators and functions in conditions:

```javascript
// String operations
<user.email>.endsWith('@company.com')

// Array operations
<api.tags>.includes('urgent')

// Mathematical operations
<agent.confidence> * 100 > 85

// Date comparisons
new Date(<api.created_at>) > new Date('2024-01-01')
```

### Multiple Condition Evaluation

Conditions are evaluated in order until one matches:

```javascript
// Condition 1: Check for high priority
<ticket.priority> === 'high'

// Condition 2: Check for urgent keywords
<ticket.subject>.toLowerCase().includes('urgent')

// Condition 3: Default fallback
true
```

### Error Handling

Conditions automatically handle:
- Undefined or null values with safe evaluation
- Type mismatches with appropriate fallbacks
- Invalid expressions with error logging
- Missing variables with default values

## Inputs and Outputs

<Tabs items={['Configuration', 'Variables', 'Results']}>
  <Tab>
    <ul className="list-disc space-y-2 pl-6">
      <li>
        <strong>Conditions</strong>: Array of boolean expressions to evaluate
      </li>
      <li>
        <strong>Expressions</strong>: JavaScript/TypeScript conditions using block outputs
      </li>
      <li>
        <strong>Routing Paths</strong>: Destination blocks for each condition result
      </li>
    </ul>
  </Tab>
  <Tab>
    <ul className="list-disc space-y-2 pl-6">
      <li>
        <strong>condition.result</strong>: Boolean result of condition evaluation
      </li>
      <li>
        <strong>condition.matched_condition</strong>: ID of the matched condition
      </li>
      <li>
        <strong>condition.content</strong>: Description of evaluation result
      </li>
      <li>
        <strong>condition.path</strong>: Details of chosen routing destination
      </li>
    </ul>
  </Tab>
  <Tab>
    <ul className="list-disc space-y-2 pl-6">
      <li>
        <strong>Boolean Result</strong>: Primary condition evaluation outcome
      </li>
      <li>
        <strong>Routing Information</strong>: Path selection and condition details
      </li>
      <li>
        <strong>Access</strong>: Available in blocks after the condition
      </li>
    </ul>
  </Tab>
</Tabs>

## Example Use Cases

### Customer Support Routing

<div className="mb-4 rounded-md border p-4">
  <h4 className="font-medium">Scenario: Route support tickets based on priority</h4>
  <ol className="list-decimal pl-5 text-sm">
    <li>API block fetches support ticket data</li>
    <li>Condition checks if `<api.priority>` equals 'high'</li>
    <li>High priority tickets → Agent with escalation tools</li>
    <li>Normal priority tickets → Standard support agent</li>
  </ol>
</div>

### Content Moderation

<div className="mb-4 rounded-md border p-4">
  <h4 className="font-medium">Scenario: Filter content based on analysis results</h4>
  <ol className="list-decimal pl-5 text-sm">
    <li>Agent analyzes user-generated content</li>
    <li>Condition checks if `<agent.toxicity_score>` > 0.7</li>
    <li>Toxic content → Moderation workflow</li>
    <li>Clean content → Publishing workflow</li>
  </ol>
</div>

### User Onboarding Flow

<div className="mb-4 rounded-md border p-4">
  <h4 className="font-medium">Scenario: Personalize onboarding based on user type</h4>
  <ol className="list-decimal pl-5 text-sm">
    <li>Function block processes user registration data</li>
    <li>Condition checks if `<user.account_type>` === 'enterprise'</li>
    <li>Enterprise users → Advanced setup workflow</li>
    <li>Individual users → Simple onboarding workflow</li>
  </ol>
</div>

## Best Practices

- **Order conditions correctly**: Place more specific conditions before general ones to ensure specific logic takes precedence over fallbacks
- **Include a default condition**: Add a catch-all condition (`true`) as the last condition to handle unmatched cases and prevent workflow execution from getting stuck
- **Keep expressions simple**: Use clear, straightforward boolean expressions for better readability and easier debugging
- **Document your conditions**: Add descriptions to explain the purpose of each condition for better team collaboration and maintenance
- **Test edge cases**: Verify conditions handle boundary values correctly by testing with values at the edges of your condition ranges
