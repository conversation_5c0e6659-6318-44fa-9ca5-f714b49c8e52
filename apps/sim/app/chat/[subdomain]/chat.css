/* Force light mode for chat subdomain by overriding dark mode utilities */
/* This file uses CSS variables from globals.css light mode theme */

/* When inside the chat layout, force all light mode CSS variables */
.chat-light-wrapper {
  /* Core Colors - from globals.css light mode */
  --background: 0 0% 100%;
  --foreground: 0 0% 3.9%;

  /* Card Colors */
  --card: 0 0% 99.2%;
  --card-foreground: 0 0% 3.9%;

  /* Popover Colors */
  --popover: 0 0% 100%;
  --popover-foreground: 0 0% 3.9%;

  /* Primary Colors */
  --primary: 0 0% 11.2%;
  --primary-foreground: 0 0% 98%;

  /* Secondary Colors */
  --secondary: 0 0% 96.1%;
  --secondary-foreground: 0 0% 11.2%;

  /* Muted Colors */
  --muted: 0 0% 96.1%;
  --muted-foreground: 0 0% 46.9%;

  /* Accent Colors */
  --accent: 0 0% 92.5%;
  --accent-foreground: 0 0% 11.2%;

  /* Destructive Colors */
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 0 0% 98%;

  /* Border & Input Colors */
  --border: 0 0% 89.8%;
  --input: 0 0% 89.8%;
  --ring: 0 0% 3.9%;

  /* Border Radius */
  --radius: 0.5rem;

  /* Scrollbar Properties */
  --scrollbar-track: 0 0% 85%;
  --scrollbar-thumb: 0 0% 65%;
  --scrollbar-thumb-hover: 0 0% 55%;
  --scrollbar-size: 8px;

  /* Workflow Properties */
  --workflow-background: 0 0% 100%;
  --workflow-dots: 0 0% 94.5%;
  --card-background: 0 0% 99.2%;
  --card-border: 0 0% 89.8%;
  --card-text: 0 0% 3.9%;
  --card-hover: 0 0% 96.1%;

  /* Base Component Properties */
  --base-muted-foreground: #737373;

  /* Gradient Colors */
  --gradient-primary: 263 85% 70%;
  --gradient-secondary: 336 95% 65%;

  /* Brand Colors */
  --brand-primary-hex: #701ffc;
  --brand-primary-hover-hex: #802fff;
  --brand-secondary-hex: #6518e6;
  --brand-accent-hex: #9d54ff;
  --brand-accent-hover-hex: #a66fff;
  --brand-background-hex: #0c0c0c;

  /* UI Surface Colors */
  --surface-elevated: #202020;
}

/* Override dark mode utility classes using CSS variables */
.chat-light-wrapper :is(.dark\:bg-black) {
  background-color: hsl(var(--secondary));
}

.chat-light-wrapper :is(.dark\:bg-gray-900) {
  background-color: hsl(var(--background));
}

.chat-light-wrapper :is(.dark\:bg-gray-800) {
  background-color: hsl(var(--secondary));
}

.chat-light-wrapper :is(.dark\:bg-gray-700) {
  background-color: hsl(var(--accent));
}

.chat-light-wrapper :is(.dark\:bg-gray-600) {
  background-color: hsl(var(--muted));
}

.chat-light-wrapper :is(.dark\:bg-gray-300) {
  background-color: hsl(var(--primary));
}

/* Text color overrides using CSS variables */
.chat-light-wrapper :is(.dark\:text-gray-100) {
  color: hsl(var(--primary));
}

.chat-light-wrapper :is(.dark\:text-gray-200) {
  color: hsl(var(--foreground));
}

.chat-light-wrapper :is(.dark\:text-gray-300) {
  color: hsl(var(--muted-foreground));
}

.chat-light-wrapper :is(.dark\:text-gray-400) {
  color: hsl(var(--muted-foreground));
}

.chat-light-wrapper :is(.dark\:text-neutral-600) {
  color: hsl(var(--muted-foreground));
}

.chat-light-wrapper :is(.dark\:text-blue-400) {
  color: var(--brand-accent-hex);
}

/* Border color overrides using CSS variables */
.chat-light-wrapper :is(.dark\:border-gray-700) {
  border-color: hsl(var(--border));
}

.chat-light-wrapper :is(.dark\:border-gray-800) {
  border-color: hsl(var(--border));
}

.chat-light-wrapper :is(.dark\:border-gray-600) {
  border-color: hsl(var(--border));
}

.chat-light-wrapper :is(.dark\:divide-gray-700) > * + * {
  border-color: hsl(var(--border));
}

/* Hover state overrides */
.chat-light-wrapper :is(.dark\:hover\:bg-gray-800\/60:hover) {
  background-color: hsl(var(--card-hover));
}

/* Code blocks specific overrides using CSS variables */
.chat-light-wrapper pre:is(.dark\:bg-black) {
  background-color: hsl(var(--workflow-dots));
}

.chat-light-wrapper code:is(.dark\:bg-gray-700) {
  background-color: hsl(var(--accent));
}

.chat-light-wrapper code:is(.dark\:text-gray-200) {
  color: hsl(var(--foreground));
}

/* Tooltip overrides - keep tooltips black with white text for consistency */
.chat-light-wrapper [data-radix-tooltip-content] {
  background-color: hsl(0 0% 3.9%) !important;
  color: hsl(0 0% 98%) !important;
}

/* Force color scheme */
.chat-light-wrapper {
  color-scheme: light !important;
}
