import type { SVGProps } from 'react'

export function UsersIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns='http://www.w3.org/2000/svg'
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      stroke='currentColor'
      strokeWidth='2'
      strokeLinecap='round'
      strokeLinejoin='round'
    >
      <path d='M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2' />
      <circle cx='9' cy='7' r='4' />
      <path d='M22 21v-2a4 4 0 0 0-3-3.87' />
      <path d='M16 3.13a4 4 0 0 1 0 7.75' />
    </svg>
  )
}

export function SettingsIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns='http://www.w3.org/2000/svg'
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      stroke='currentColor'
      strokeWidth='2'
      strokeLinecap='round'
      strokeLinejoin='round'
    >
      <path d='M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z' />
      <circle cx='12' cy='12' r='3' />
    </svg>
  )
}

export function SearchIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns='http://www.w3.org/2000/svg'
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      stroke='currentColor'
      strokeWidth='2'
      strokeLinecap='round'
      strokeLinejoin='round'
    >
      <circle cx='11' cy='11' r='8' />
      <path d='m21 21-4.3-4.3' />
    </svg>
  )
}

export function Spinner() {
  return (
    <div className='absolute top-0 right-0 bottom-0 flex items-center justify-center'>
      <svg
        className='-ml-1 mr-3 h-5 w-5 animate-spin text-gray-700'
        xmlns='http://www.w3.org/2000/svg'
        fill='none'
        viewBox='0 0 24 24'
      >
        <circle
          className='opacity-25'
          cx='12'
          cy='12'
          r='10'
          stroke='currentColor'
          strokeWidth='4'
        />
        <path
          className='opacity-75'
          fill='currentColor'
          d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'
        />
      </svg>
    </div>
  )
}

export function AgentIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='21'
      height='24'
      viewBox='0 0 21 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M15.6667 9.25H4.66667C2.64162 9.25 1 10.8916 1 12.9167V18.4167C1 20.4417 2.64162 22.0833 4.66667 22.0833H15.6667C17.6917 22.0833 19.3333 20.4417 19.3333 18.4167V12.9167C19.3333 10.8916 17.6917 9.25 15.6667 9.25Z'
        stroke='currentColor'
        strokeWidth='2'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M10.1663 5.58464C11.1789 5.58464 11.9997 4.76382 11.9997 3.7513C11.9997 2.73878 11.1789 1.91797 10.1663 1.91797C9.15382 1.91797 8.33301 2.73878 8.33301 3.7513C8.33301 4.76382 9.15382 5.58464 10.1663 5.58464Z'
        stroke='currentColor'
        strokeWidth='2'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M10.167 5.58594V9.2526M7.41699 16.5859V14.7526M12.917 14.7526V16.5859'
        stroke='currentColor'
        strokeWidth='2'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export function ApiIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='30'
      height='30'
      viewBox='0 0 30 30'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M5.61111 24.3889C8.5 27.2778 12.4722 25.4722 13.5556 24.3889L15.7222 22.2222L7.77778 14.2778L5.61111 16.4444C4.52778 17.5278 2.72222 21.5 5.61111 24.3889ZM5.61111 24.3889L2 28M24.3889 5.61111C21.5 2.72222 17.5278 4.52778 16.4444 5.61111L14.2778 7.77778L22.2222 15.7222L24.3889 13.5556C25.4722 12.4722 27.2778 8.5 24.3889 5.61111ZM24.3889 5.61111L28 2M15.7222 9.22222L12.8333 12.1111M20.7778 14.2778L17.8889 17.1667'
        stroke='currentColor'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export function ConditionalIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='28'
      height='29'
      viewBox='0 0 28 29'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M23.1015 1.01616C22.1825 1.01442 21.2926 1.33795 20.5894 1.92946C19.8861 2.52098 19.4149 3.34229 19.2592 4.24794C19.1035 5.15358 19.2733 6.08512 19.7386 6.87755C20.2039 7.66998 20.9346 8.27217 21.8014 8.57745V12.7169H6.20035V8.57745C7.06779 8.27077 7.79888 7.6673 8.26441 6.87372C8.72994 6.08013 8.89994 5.14752 8.74435 4.24072C8.58877 3.33391 8.11762 2.5113 7.41419 1.91828C6.71075 1.32526 5.82032 1 4.90027 1C3.98021 1 3.08978 1.32526 2.38634 1.91828C1.68291 2.5113 1.21176 3.33391 1.05618 4.24072C0.900594 5.14752 1.07059 6.08013 1.53612 6.87372C2.00165 7.6673 2.73274 8.27077 3.60018 8.57745V12.7169C3.60018 13.4065 3.87413 14.0679 4.36175 14.5555C4.84938 15.0432 5.51074 15.3171 6.20035 15.3171H12.7008V20.7567C11.8333 21.0633 11.1023 21.6668 10.6367 22.4604C10.1712 23.254 10.0012 24.1866 10.1568 25.0934C10.3124 26.0002 10.7835 26.8228 11.4869 27.4158C12.1904 28.0089 13.0808 28.3341 14.0009 28.3341C14.9209 28.3341 15.8114 28.0089 16.5148 27.4158C17.2182 26.8228 17.6894 26.0002 17.845 25.0934C18.0005 24.1866 17.8305 23.254 17.365 22.4604C16.8995 21.6668 16.1684 21.0633 15.301 20.7567V15.3171H21.8014C22.491 15.3171 23.1524 15.0432 23.64 14.5555C24.1276 14.0679 24.4015 13.4065 24.4015 12.7169V8.57745C25.2683 8.27217 25.999 7.66998 26.4643 6.87755C26.9296 6.08512 27.0994 5.15358 26.9437 4.24794C26.788 3.34229 26.3168 2.52098 25.6135 1.92946C24.9103 1.33795 24.0204 1.01442 23.1015 1.01616ZM4.90027 6.2165C4.64313 6.2165 4.39177 6.14025 4.17798 5.99739C3.96418 5.85454 3.79754 5.65149 3.69914 5.41393C3.60074 5.17637 3.575 4.91497 3.62516 4.66278C3.67532 4.41059 3.79915 4.17893 3.98097 3.99711C4.16279 3.81529 4.39444 3.69147 4.64663 3.64131C4.89882 3.59114 5.16023 3.61689 5.39779 3.71529C5.63535 3.81369 5.83839 3.98033 5.98125 4.19412C6.1241 4.40792 6.20035 4.65928 6.20035 4.91641C6.20035 5.26122 6.06338 5.5919 5.81956 5.83571C5.57575 6.07953 5.24507 6.2165 4.90027 6.2165ZM14.0009 25.7178C13.7437 25.7178 13.4924 25.6415 13.2786 25.4987C13.0648 25.3558 12.8981 25.1528 12.7997 24.9152C12.7013 24.6777 12.6756 24.4163 12.7258 24.1641C12.7759 23.9119 12.8997 23.6802 13.0816 23.4984C13.2634 23.3166 13.495 23.1928 13.7472 23.1426C13.9994 23.0924 14.2608 23.1182 14.4984 23.2166C14.7359 23.315 14.939 23.4816 15.0818 23.6954C15.2247 23.9092 15.301 24.1606 15.301 24.4177C15.301 24.7625 15.164 25.0932 14.9202 25.337C14.6764 25.5808 14.3457 25.7178 14.0009 25.7178ZM23.1015 6.2165C22.8443 6.2165 22.593 6.14025 22.3792 5.99739C22.1654 5.85454 21.9987 5.65149 21.9003 5.41393C21.8019 5.17637 21.7762 4.91497 21.8264 4.66278C21.8765 4.41059 22.0003 4.17893 22.1822 3.99711C22.364 3.81529 22.5956 3.69147 22.8478 3.64131C23.1 3.59114 23.3614 3.61689 23.599 3.71529C23.8365 3.81369 24.0396 3.98033 24.1824 4.19412C24.3253 4.40792 24.4015 4.65928 24.4015 4.91641C24.4015 5.26122 24.2646 5.5919 24.0208 5.83571C23.777 6.07953 23.4463 6.2165 23.1015 6.2165Z'
        fill='currentColor'
        stroke='currentColor'
        strokeWidth='0.25'
      />
    </svg>
  )
}

export function AirplaneIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='30'
      height='30'
      viewBox='0 0 30 30'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M18.0925 7.76479L23.0009 2.85494C23.5535 2.31133 24.2985 2.00809 25.0736 2.01126C25.8488 2.01443 26.5913 2.32375 27.1394 2.87187C27.6875 3.41998 27.9968 4.16247 28 4.93761C28.0031 5.71275 27.6999 6.45774 27.1563 7.01032L22.2464 11.9187L24.903 25.1364C25.2426 26.8275 21.9632 29.1834 21.0902 26.9431L17.2022 16.9644L12.1536 22.013C12.3993 24.963 12.4528 25.9444 10.3845 28.0112L7.24083 22.7704L2 19.6268C4.06685 17.5585 5.04824 17.6105 7.9982 17.8577L13.0468 12.8105L3.06811 8.92107C0.827823 8.04808 3.18374 4.76714 4.8748 5.10824L18.0925 7.76479Z'
        stroke='currentColor'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export function WorkIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='30'
      height='30'
      viewBox='0 0 30 30'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M9.66689 7.20002V6.16002C9.66689 3.85901 9.66689 2 13.9338 2H16.0658C20.3327 2 20.3327 3.85901 20.3327 6.16002V7.20002M9.66689 28.0001H20.3327C25.6916 28.0001 26.6522 25.9056 26.9324 23.3591L27.932 12.959C28.2931 9.78703 27.3585 7.20002 21.6659 7.20002H8.33366C2.64109 7.20002 1.70797 9.78703 2.06764 12.959L3.0672 23.3591C3.34742 25.9056 4.30798 28.0001 9.66689 28.0001Z'
        stroke='currentColor'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M11.8385 16.9227C8.45374 16.5494 5.20151 15.3969 2.33691 13.5557M18.2259 16.9227C21.6106 16.5494 24.8629 15.3969 27.7275 13.5557M17.8879 17.1668C17.8879 17.933 17.5835 18.6678 17.0417 19.2095C16.4999 19.7513 15.7651 20.0557 14.999 20.0557C14.2328 20.0557 13.498 19.7513 12.9562 19.2095C12.4144 18.6678 12.1101 17.933 12.1101 17.1668C12.1101 16.4006 12.4144 15.6658 12.9562 15.124C13.498 14.5823 14.2328 14.2779 14.999 14.2779C15.7651 14.2779 16.4999 14.5823 17.0417 15.124C17.5835 15.6658 17.8879 16.4006 17.8879 17.1668Z'
        stroke='currentColor'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export function WorkflowIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='30'
      height='30'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <circle
        className='a'
        cx='12'
        cy='6'
        r='3'
        stroke='currentColor'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <rect
        className='a'
        height='5'
        rx='2'
        width='8'
        x='2'
        y='16'
        stroke='currentColor'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <rect
        className='a'
        height='5'
        rx='2'
        width='8'
        x='14'
        y='16'
        stroke='currentColor'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        className='a'
        d='M6,16V14a2,2,0,0,1,2-2h8a2,2,0,0,1,2,2v2'
        stroke='currentColor'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <line
        className='a'
        x1='12'
        x2='12'
        y1='9'
        y2='12'
        stroke='currentColor'
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export function WarnIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='30'
      height='30'
      viewBox='0 0 30 30'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M15 9.3152V16.4654M15 20.6853V20.0353M2.91879 12.7837C2.62752 13.0747 2.39645 13.4202 2.2388 13.8005C2.08115 14.1809 2 14.5885 2 15.0002C2 15.412 2.08115 15.8196 2.2388 16.2C2.39645 16.5803 2.62752 16.9258 2.91879 17.2168L12.7834 27.0814C13.0744 27.3727 13.4199 27.6038 13.8003 27.7614C14.1806 27.9191 14.5883 28.0002 15 28.0002C15.4117 28.0002 15.8194 27.9191 16.1997 27.7614C16.58 27.6038 16.9256 27.3727 17.2166 27.0814L27.0812 17.2168C27.3725 16.9258 27.6035 16.5803 27.7612 16.2C27.9189 15.8196 28 15.412 28 15.0002C28 14.5885 27.9189 14.1809 27.7612 13.8005C27.6035 13.4202 27.3725 13.0747 27.0812 12.7837L17.2166 2.91904C16.9256 2.62776 16.58 2.3967 16.1997 2.23904C15.8194 2.08139 15.4117 2.00024 15 2.00024C14.5883 2.00024 14.1806 2.08139 13.8003 2.23904C13.4199 2.3967 13.0744 2.62776 12.7834 2.91904L2.91879 12.7837Z'
        stroke='currentColor'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export function UploadIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='30'
      height='25'
      viewBox='0 0 30 25'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M15.1605 23.1773L15.1552 12.5569M25.5937 19.1946C31.5611 14.9996 26.2243 8.93265 20.6738 8.93265C16.9262 -5.67304 -5.05936 6.40766 4.68352 17.4183'
        stroke='currentColor'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M19.3792 15.6341L15.1549 11.4098L10.9307 15.6341'
        stroke='currentColor'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export function TrashIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='29'
      height='35'
      viewBox='0 0 29 35'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M24.9 12.4L23.444 26.9565C23.2239 29.1631 23.1147 30.2655 22.612 31.0992C22.1711 31.8331 21.5227 32.42 20.7487 32.7857C19.8699 33.2 18.764 33.2 16.5453 33.2H12.4547C10.2377 33.2 9.13013 33.2 8.25133 32.784C7.47662 32.4185 6.8276 31.8316 6.38627 31.0975C5.88707 30.2655 5.77613 29.1631 5.55427 26.9565L4.1 12.4M17.1 23.6667V15M11.9 23.6667V15M1.5 8.06667H9.49933M9.49933 8.06667L10.1684 3.4352C10.3625 2.5928 11.0628 2 11.8671 2H17.1329C17.9372 2 18.6357 2.5928 18.8316 3.4352L19.5007 8.06667M9.49933 8.06667H19.5007M19.5007 8.06667H27.5'
        stroke='currentColor'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export function StudentIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='30'
      height='27'
      viewBox='0 0 30 27'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M28 9.1498V15.6498M17.8821 2.6498C16.9814 2.22228 15.997 2.00049 15 2.00049C14.003 2.00049 13.0186 2.22228 12.1179 2.6498L3.4196 6.7279C1.5268 7.6145 1.5268 10.6851 3.4196 11.5717L12.1166 15.6498C13.0174 16.0775 14.0021 16.2994 14.9993 16.2994C15.9966 16.2994 16.9813 16.0775 17.8821 15.6498L26.5804 11.5717C28.4732 10.6851 28.4732 7.6145 26.5804 6.7279L17.8821 2.6498Z'
        stroke='currentColor'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M5.90039 13.0498V19.7123C5.90039 23.5057 12.0026 25.3998 15.0004 25.3998C17.9982 25.3998 24.1004 23.5057 24.1004 19.7123V13.0498'
        stroke='currentColor'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export function SignalIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='29'
      height='35'
      viewBox='0 0 29 35'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M23.964 33.2C26.252 28.416 27.5 23.216 27.5 17.6C27.5 11.984 26.252 6.576 23.964 2M16.476 29.664C18.14 25.92 19.18 21.76 19.18 17.6C19.18 13.44 18.14 9.072 16.476 5.328M8.988 26.128C10.236 23.424 10.86 20.512 10.86 17.6C10.86 14.688 10.236 11.568 8.988 9.072M1.5 22.384C2.124 20.928 2.54 19.264 2.54 17.6C2.54 15.936 2.124 14.064 1.5 12.608'
        stroke='currentColor'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export function SectionIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='30'
      height='30'
      viewBox='0 0 30 30'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M4.88889 22.2222V7.77778M4.88889 22.2222C4.31752 22.2222 3.75898 22.3917 3.28391 22.7091C2.80883 23.0265 2.43856 23.4777 2.2199 24.0056C2.00125 24.5335 1.94404 25.1143 2.05551 25.6747C2.16698 26.2351 2.44212 26.7498 2.84614 27.1539C3.25016 27.5579 3.76491 27.833 4.3253 27.9445C4.88569 28.056 5.46654 27.9987 5.99442 27.7801C6.5223 27.5614 6.97348 27.1912 7.29091 26.7161C7.60835 26.241 7.77778 25.6825 7.77778 25.1111M4.88889 22.2222C5.65507 22.2222 6.38987 22.5266 6.93164 23.0684C7.47341 23.6101 7.77778 24.3449 7.77778 25.1111M4.88889 7.77778C5.65507 7.77778 6.38987 7.47341 6.93164 6.93164C7.47341 6.38987 7.77778 5.65507 7.77778 4.88889M4.88889 7.77778C4.31752 7.77778 3.75898 7.60835 3.28391 7.29091C2.80883 6.97348 2.43856 6.5223 2.2199 5.99442C2.00125 5.46654 1.94404 4.88569 2.05551 4.3253C2.16698 3.76491 2.44212 3.25016 2.84614 2.84614C3.25016 2.44212 3.76491 2.16698 4.3253 2.05551C4.88569 1.94404 5.46654 2.00125 5.99442 2.2199C6.5223 2.43856 6.97348 2.80883 7.29091 3.28391C7.60835 3.75898 7.77778 4.31752 7.77778 4.88889M7.77778 25.1111H22.2222M7.77778 4.88889H22.2222M22.2222 4.88889C22.2222 5.65507 22.5266 6.38987 23.0684 6.93164C23.6101 7.47341 24.3449 7.77778 25.1111 7.77778M22.2222 4.88889C22.2222 4.31752 22.3917 3.75898 22.7091 3.28391C23.0265 2.80883 23.4777 2.43856 24.0056 2.2199C24.5335 2.00125 25.1143 1.94404 25.6747 2.05551C26.2351 2.16698 26.7498 2.44212 27.1539 2.84614C27.5579 3.25016 27.833 3.76491 27.9445 4.3253C28 4.88569 27.9987 5.46654 27.7801 5.99442C27.5614 6.5223 27.1912 6.97348 26.7161 7.29091C26.241 7.60835 25.6825 7.77778 25.1111 7.77778M25.1111 7.77778V22.2222M25.1111 22.2222C24.3449 22.2222 23.6101 22.5266 23.0684 23.0684C22.5266 23.6101 22.2222 24.3449 22.2222 25.1111M25.1111 22.2222C25.6825 22.2222 26.241 22.3917 26.7161 22.7091C27.1912 23.0265 27.5614 23.4777 27.7801 24.0056C27.9987 24.5335 28 25.1143 27.9445 25.6747C27.833 26.2351 27.5579 26.7498 27.1539 27.1539C26.7498 27.5579 26.2351 27.833 25.6747 27.9445C25.1143 28.056 24.5335 27.9987 24.0056 27.7801C23.4777 27.5614 23.0265 27.1912 22.7091 26.7161C22.3917 26.241 22.2222 25.6825 22.2222 25.1111'
        stroke='currentColor'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export function ReminderIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='30'
      height='30'
      viewBox='0 0 30 30'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M2 5.22256L6.55 2M28 5.22256L23.45 2M26.5556 16.4444C26.5556 19.5092 25.3381 22.4484 23.171 24.6155C21.0039 26.7825 18.0647 28 15 28C11.9353 28 8.99608 26.7825 6.82899 24.6155C4.6619 22.4484 3.44444 19.5092 3.44444 16.4444C3.44444 13.3797 4.6619 10.4405 6.82899 8.27343C8.99608 6.10635 11.9353 4.88889 15 4.88889C18.0647 4.88889 21.0039 6.10635 23.171 8.27343C25.3381 10.4405 26.5556 13.3797 26.5556 16.4444Z'
        stroke='currentColor'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M15 9.94446V17.1667L19.3333 20.0556'
        stroke='currentColor'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export function DatabaseIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='30'
      height='33'
      viewBox='0 0 30 33'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M28 16.2188C28 23.8026 2 23.8026 2 16.2188M15 2C7.82075 2 2 4.09625 2 6.68C2 13.44 28 13.44 28 6.68C28 4.09625 22.1793 2 15 2Z'
        stroke='currentColor'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M2 6.875V26.6431C2 32.7856 28 32.7856 28 26.6431V6.875'
        stroke='currentColor'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export function CrateIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='30'
      height='30'
      viewBox='0 0 30 30'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M15 28L26.7448 21.9218C27.2012 21.6849 27.4294 21.5679 27.5956 21.3931C27.7427 21.2398 27.8541 21.0558 27.922 20.8544C28 20.6247 28 20.369 28 19.8548V8.5477M15 28L3.25522 21.9218C2.79878 21.6849 2.57056 21.5679 2.40444 21.3931C2.25732 21.2398 2.14585 21.0558 2.078 20.8544C2 20.6247 2 20.3676 2 19.8519V8.5477M15 28V14.909M28 8.5477L15 14.909M28 8.5477L16.0544 2.36548C15.6688 2.16614 15.4767 2.06503 15.273 2.02603C15.0927 1.99132 14.9073 1.99132 14.727 2.02603C14.5248 2.06503 14.3312 2.16614 13.9441 2.36548L2 8.5477M2 8.5477L15 14.909'
        stroke='currentColor'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export function CookieIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='30'
      height='30'
      viewBox='0 0 30 30'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M19.3368 21.2951L19.8483 20.7836M12.1124 22.74L12.6239 22.2285M9.22264 9.73606L9.73269 9.22457M7.77776 16.9605L8.28781 16.449M15.0022 15.5156L15.5137 15.0041M15.0065 28.008C18.3701 28.0079 21.6029 26.7048 24.0264 24.3724C26.4499 22.0399 27.8758 18.8594 28.0047 15.4982C28.0133 15.2772 27.7735 15.137 27.5741 15.2367C23.9966 17.0385 21.7859 15.1255 22.1457 12.5579C22.1518 12.5083 22.1467 12.458 22.1306 12.4106C22.1146 12.3633 22.0881 12.3201 22.0531 12.2845C22.0181 12.2488 21.9755 12.2214 21.9285 12.2045C21.8815 12.1875 21.8313 12.1814 21.7815 12.1866C18.6447 12.6244 17.3226 10.7215 17.8168 8.13513C17.8241 8.09108 17.8223 8.04599 17.8115 8.00267C17.8006 7.95935 17.7809 7.91873 17.7537 7.88335C17.7264 7.84796 17.6922 7.81857 17.6531 7.79701C17.614 7.77545 17.5708 7.76218 17.5264 7.75802C14.5745 7.49505 14.3303 4.08802 14.8375 2.42641C14.8996 2.22123 14.7522 1.99294 14.5384 2.00017C11.1323 2.12457 7.91116 3.58078 5.56769 6.05559C3.22422 8.53041 1.94565 11.8261 2.00699 15.2339C2.06833 18.6416 3.46468 21.8892 5.89569 24.2781C8.32671 26.6669 11.5982 28.0063 15.0065 28.008Z'
        stroke='currentColor'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export function ErrorIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='30'
      height='30'
      viewBox='0 0 30 30'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M15 8.68344V16.6279M15 21.3166V20.5943M28 15C28 16.7072 27.6637 18.3977 27.0104 19.9749C26.3571 21.5521 25.3995 22.9852 24.1924 24.1924C22.9852 25.3995 21.5521 26.3571 19.9749 27.0104C18.3977 27.6637 16.7072 28 15 28C13.2928 28 11.6023 27.6637 10.0251 27.0104C8.44788 26.3571 7.01477 25.3995 5.80761 24.1924C4.60045 22.9852 3.64288 21.5521 2.98957 19.9749C2.33625 18.3977 2 16.7072 2 15C2 11.5522 3.36964 8.24558 5.80761 5.80761C8.24558 3.36964 11.5522 2 15 2C18.4478 2 21.7544 3.36964 24.1924 5.80761C26.6304 8.24558 28 11.5522 28 15Z'
        stroke='currentColor'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export function ChromeIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='30'
      height='30'
      viewBox='0 0 30 30'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M13.5442 27.9221L19.4982 17.6001M4.53516 7.27808L10.5022 17.6001M26.9212 9.80008H15.0002M15.0002 9.80008C16.3793 9.80008 17.7019 10.3479 18.6771 11.3231C19.6523 12.2983 20.2002 13.621 20.2002 15.0001C20.2002 16.3792 19.6523 17.7018 18.6771 18.677C17.7019 19.6522 16.3793 20.2001 15.0002 20.2001C13.621 20.2001 12.2984 19.6522 11.3232 18.677C10.348 17.7018 9.80016 16.3792 9.80016 15.0001C9.80016 13.621 10.348 12.2983 11.3232 11.3231C12.2984 10.3479 13.621 9.80008 15.0002 9.80008Z'
        stroke='currentColor'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M15 28C16.7072 28 18.3977 27.6637 19.9749 27.0104C21.5521 26.3571 22.9852 25.3995 24.1924 24.1924C25.3995 22.9852 26.3571 21.5521 27.0104 19.9749C27.6637 18.3977 28 16.7072 28 15C28 13.2928 27.6637 11.6023 27.0104 10.0251C26.3571 8.44788 25.3995 7.01477 24.1924 5.80761C22.9852 4.60045 21.5521 3.64288 19.9749 2.98957C18.3977 2.33625 16.7072 2 15 2C11.5522 2 8.24558 3.36964 5.80761 5.80761C3.36964 8.24558 2 11.5522 2 15C2 18.4478 3.36964 21.7544 5.80761 24.1924C8.24558 26.6304 11.5522 28 15 28Z'
        stroke='currentColor'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export function CalendarIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='30'
      height='30'
      viewBox='0 0 30 30'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M21.5 4.88889V2M8.5 4.88889V2M2.36111 9.22222H27.6389M2 12.1747C2 9.11967 2 7.59144 2.62978 6.42433C3.19924 5.38333 4.08288 4.54873 5.15467 4.03956C6.39111 3.44444 8.00889 3.44444 11.2444 3.44444H18.7556C21.9911 3.44444 23.6089 3.44444 24.8453 4.03956C25.933 4.56244 26.8156 5.39733 27.3702 6.42289C28 7.59289 28 9.12111 28 12.1761V19.2712C28 22.3262 28 23.8544 27.3702 25.0216C26.8008 26.0626 25.9171 26.8972 24.8453 27.4063C23.6089 28 21.9911 28 18.7556 28H11.2444C8.00889 28 6.39111 28 5.15467 27.4049C4.0831 26.8961 3.19948 26.062 2.62978 25.0216C2 23.8516 2 22.3233 2 19.2683V12.1747Z'
        stroke='currentColor'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export function MessagesIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='30'
      height='30'
      viewBox='0 0 30 30'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M4.88867 14.999C6.27936 13.956 7.99961 13.4496 9.7336 13.5728C11.4676 13.6961 13.0989 14.4406 14.3281 15.6698C15.5574 16.899 16.3019 18.5304 16.4252 20.2644C16.5484 21.9984 16.042 23.7186 14.999 25.1093M4.88867 14.999C3.99176 15.6717 3.2638 16.5439 2.76241 17.5467C2.26103 18.5495 2 19.6552 2 20.7763C1.99954 21.5025 2.10861 22.2246 2.32353 22.9183C2.71639 24.1893 2.4333 25.6047 2.18054 26.9393C2.15813 27.0534 2.16551 27.1713 2.20198 27.2817C2.23845 27.392 2.30278 27.4912 2.38874 27.5694C2.4747 27.6477 2.57939 27.7025 2.6927 27.7285C2.80601 27.7544 2.92411 27.7508 3.03559 27.7178C4.26038 27.3827 5.47795 27.0967 6.72875 27.556C7.52721 27.849 8.37115 27.9986 9.22166 27.998C10.343 27.9991 11.4491 27.7386 12.4521 27.2371C13.455 26.7356 14.3271 26.0071 14.999 25.1093M4.88867 14.999C4.88867 8.23229 9.04112 2 16.4433 2C18.2963 1.99968 20.1222 2.44502 21.767 3.29845C23.4118 4.15189 24.8272 5.38838 25.8938 6.90363C26.9605 8.41888 27.647 10.1684 27.8956 12.0047C28.1441 13.841 27.9474 15.7101 27.322 17.4544C26.6345 19.3695 27.3755 21.9347 27.8088 24.0521C27.834 24.1648 27.8288 24.2822 27.7937 24.3923C27.7587 24.5024 27.6949 24.6011 27.6091 24.6785C27.5232 24.7558 27.4184 24.8089 27.3052 24.8323C27.1921 24.8557 27.0748 24.8486 26.9653 24.8118C25.0703 24.2196 22.846 23.3877 21.0652 24.1387C19.1384 24.9504 17.1135 25.1093 14.999 25.1093'
        stroke='currentColor'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export function NotificationsIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='30'
      height='33'
      viewBox='0 0 30 33'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M19.9056 24.7496H10.0908M19.9056 24.7496H25.7783C28.8267 24.7496 28.3116 21.7272 26.7695 20.1932C21.2153 14.6764 29.1046 2 14.9982 2C0.891783 2 8.78265 14.6748 3.22849 20.1932C1.74489 21.6687 1.11278 24.7496 4.21973 24.7496H10.0908M19.9056 24.7496C19.9056 27.8777 18.8526 31.2495 14.9982 31.2495C11.1437 31.2495 10.0908 27.8777 10.0908 24.7496'
        stroke='currentColor'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export function MailIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='30'
      height='24'
      viewBox='0 0 30 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M2.35742 5.83288L11.7674 12.1071C13.0656 12.9712 13.7141 13.404 14.4151 13.5725C15.0352 13.7208 15.681 13.7208 16.2998 13.5725C17.0008 13.404 17.6492 12.9712 18.9475 12.1071L28.3574 5.83288M8.82844 21.7219H21.8864C24.1513 21.7219 25.2837 21.7219 26.1492 21.2811C26.9097 20.8931 27.5278 20.2744 27.9152 19.5137C28.3574 18.6482 28.3574 17.5158 28.3574 15.2509V7.97102C28.3574 5.70616 28.3574 4.57373 27.9166 3.70823C27.5288 2.94727 26.9102 2.32858 26.1492 1.94084C25.2837 1.5 24.1513 1.5 21.8864 1.5H8.82844C6.56358 1.5 5.43115 1.5 4.56566 1.94084C3.80519 2.32881 3.187 2.94747 2.79961 3.70823C2.35742 4.57373 2.35742 5.70616 2.35742 7.97102V15.2509C2.35742 17.5158 2.35742 18.6482 2.79826 19.5137C3.186 20.2747 3.80469 20.8933 4.56566 21.2811C5.43115 21.7219 6.56358 21.7219 8.82844 21.7219Z'
        stroke='currentColor'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export function CodeIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='30'
      height='27'
      viewBox='0 0 30 27'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M23.2639 6.83064L23.6375 7.20422C26.5433 10.1117 27.9971 11.5638 27.9971 13.37C27.9971 15.1762 26.5433 16.63 23.6375 19.5358L23.2639 19.9094M18.0434 2L11.9507 24.7401M6.72863 6.83064L6.35504 7.20422C3.45081 10.1117 1.99707 11.5638 1.99707 13.37C1.99707 15.1762 3.45081 16.63 6.35829 19.5358L6.73187 19.9094'
        stroke='currentColor'
        strokeWidth='2.6'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export function ChartBarIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='24'
      height='22'
      viewBox='0 0 24 22'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M14.4 0C14.9139 0 15.4067 0.2107 15.7678 0.5858C16.1289 0.9609 16.3334 1.4696 16.3334 2V6.5H21.6C22.1139 6.5 22.6067 6.7107 22.9678 7.0858C23.3289 7.4609 23.5334 7.9696 23.5334 8.5V19.2C23.5334 19.7304 23.3289 20.2391 22.9678 20.6142C22.6067 20.9893 22.1139 21.2 21.6 21.2H2.4C1.8861 21.2 1.3933 20.9893 1.0322 20.6142C0.6711 20.2391 0.4666 19.7304 0.4666 19.2V12C0.4666 11.4696 0.6711 10.9609 1.0322 10.5858C1.3933 10.2107 1.8861 10 2.4 10H7.6666V2C7.6666 1.4696 7.8711 0.9609 8.2322 0.5858C8.5933 0.2107 9.0861 0 9.6 0H14.4ZM14.4 2.4H9.6V19.2H14.4V2.4ZM21.6 8.9H16.3334V19.2H21.6V8.9ZM7.6666 12.5H2.4V19.2H7.6666V12.5Z'
        fill='currentColor'
      />
    </svg>
  )
}

export function AtomIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='30'
      height='30'
      viewBox='0 0 30 30'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M19.9206 10.079C26.5651 16.7235 29.7457 24.3111 27.0273 27.0296C24.3117 29.7466 16.7212 26.5645 10.0782 19.9215C3.43516 13.2785 0.253045 5.68936 2.97149 2.97092C5.68705 0.253917 13.2762 3.43603 19.9206 10.079Z'
        stroke='currentColor'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M14.6383 15.0001H15.3605M10.0796 10.0789C3.43514 16.7234 0.253025 24.311 2.97147 27.0295C5.68992 29.7479 13.2776 26.5644 19.9206 19.9214C26.5636 13.2783 29.7471 5.68923 27.0287 2.97079C24.3103 0.252343 16.7226 3.4359 10.0796 10.0789Z'
        stroke='currentColor'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export function ElevatorIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='29'
      height='40'
      viewBox='0 0 29 40'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M14.5 12.588H23.5C24.5609 12.588 25.5783 13.0094 26.3284 13.7596C27.0786 14.5097 27.5 15.5271 27.5 16.588V34C27.5 35.0609 27.0786 36.0783 26.3284 36.8284C25.5783 37.5786 24.5609 38 23.5 38H14.5M14.5 12.588H5.5C4.43913 12.588 3.42172 13.0094 2.67157 13.7596C1.92143 14.5097 1.5 15.5271 1.5 16.588V34C1.5 35.0609 1.92143 36.0783 2.67157 36.8284C3.42172 37.5786 4.43913 38 5.5 38H14.5M14.5 12.588V38M4.75 5.176L8 2L11.25 5.176M17.75 2L21 5.176L24.25 2'
        stroke='currentColor'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export function DollarIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='30'
      height='43'
      viewBox='0 0 30 43'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M25.8333 6.33333H15M15 6.33333H9.58333C7.57211 6.33333 5.64326 7.13229 4.22111 8.55444C2.79896 9.97659 2 11.9054 2 13.9167C2 15.9279 2.79896 17.8567 4.22111 19.2789C5.64326 20.701 7.57211 21.5 9.58333 21.5H15M15 6.33333V2M15 6.33333V21.5M15 21.5H20.4167C21.4125 21.5 22.3986 21.6961 23.3187 22.0772C24.2387 22.4583 25.0747 23.0169 25.7789 23.7211C26.4831 24.4253 27.0417 25.2613 27.4228 26.1813C27.8039 27.1014 28 28.0875 28 29.0833C28 30.0792 27.8039 31.0653 27.4228 31.9853C27.0417 32.9054 26.4831 33.7414 25.7789 34.4456C25.0747 35.1497 24.2387 35.7083 23.3187 36.0894C22.3986 36.4705 21.4125 36.6667 20.4167 36.6667H15M15 21.5V36.6667M15 36.6667H2M15 36.6667V41'
        stroke='currentColor'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export function CreditCardIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='30'
      height='24'
      viewBox='0 0 30 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M2 7.77778V6.62222C2 5.00444 2 4.19556 2.31489 3.57733C2.59222 3.03278 3.03278 2.59222 3.57733 2.31489C4.19556 2 5.00444 2 6.62222 2H23.3778C24.9956 2 25.8044 2 26.4212 2.31489C26.9658 2.59222 27.4078 3.03278 27.6851 3.57733C28 4.19411 28 5.003 28 6.61789V7.77778M2 7.77778H28M2 7.77778V17.6C2 19.2178 2 20.0267 2.31489 20.6449C2.59185 21.1884 3.03378 21.6304 3.57733 21.9073C4.19411 22.2222 5.003 22.2222 6.61789 22.2222H23.3821C24.997 22.2222 25.8044 22.2222 26.4212 21.9073C26.9658 21.63 27.4078 21.188 27.6851 20.6449C28 20.0267 28 19.2207 28 17.6058V7.77778M6.33333 16.4444H12.1111'
        stroke='currentColor'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export function BoatIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='30'
      height='30'
      viewBox='0 0 30 30'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M4.6 22.5024L3.73767 17.6737C3.61597 16.9932 3.74231 16.2917 4.09374 15.6964C4.44517 15.1011 4.99841 14.6516 5.653 14.4294L5.89856 14.3457M5.89856 14.3457L14.0727 11.5723C14.6741 11.3685 15.3259 11.3685 15.9273 11.5723L24.1 14.3457M5.89856 14.3457L5.9 8.85678C5.9 8.0906 6.20436 7.3558 6.74614 6.81402C7.28791 6.27225 8.02271 5.96789 8.78889 5.96789H12.4M24.1 14.3457L24.3456 14.4294C25.0004 14.6513 25.554 15.1007 25.9057 15.696C26.2574 16.2913 26.384 16.993 26.2623 17.6737L25.4 22.501M24.1 14.3457V8.85678C24.1 8.0906 23.7956 7.3558 23.2539 6.81402C22.7121 6.27225 21.9773 5.96789 21.2111 5.96789H17.6M12.4 5.96789H17.6M12.4 5.96789V4.6C12.4 3.91044 12.6739 3.24912 13.1615 2.76152C13.6491 2.27393 14.3104 2 15 2C15.6896 2 16.3509 2.27393 16.8385 2.76152C17.3261 3.24912 17.6 3.91044 17.6 4.6V5.96789M2 27.1319C2.93456 28.0809 5.20956 28.3496 7.38056 27.4439C8.87122 26.8228 10.7548 26.8069 12.2787 27.3413C14.0429 27.9438 15.9571 27.9438 17.7213 27.3413C19.2452 26.8069 21.1288 26.8213 22.618 27.4424C24.7904 28.3496 27.0669 28.0809 28 27.1304'
        stroke='currentColor'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export function CancelIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='30'
      height='30'
      viewBox='0 0 30 30'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M28 15C28 16.7072 27.6637 18.3977 27.0104 19.9749C26.3571 21.5521 25.3995 22.9852 24.1924 24.1924C22.9852 25.3995 21.5521 26.3571 19.9749 27.0104C18.3977 27.6637 16.7072 28 15 28C13.2928 28 11.6023 27.6637 10.0251 27.0104C8.44788 26.3571 7.01477 25.3995 5.80761 24.1924C4.60045 22.9852 3.64288 21.5521 2.98957 19.9749C2.33625 18.3977 2 16.7072 2 15C2 11.5522 3.36964 8.24558 5.80761 5.80761C8.24558 3.36964 11.5522 2 15 2C18.4478 2 21.7544 3.36964 24.1924 5.80761C26.6304 8.24558 28 11.5522 28 15Z'
        stroke='currentColor'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M23.6667 6.33333L6.33333 23.6667'
        stroke='currentColor'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export function BankIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='30'
      height='30'
      viewBox='0 0 30 30'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M15 10.5797V23.6664M22.2222 10.5797V23.6664M7.77778 10.5797V23.6664M15.6457 2.15283L26.8603 7.79772C28.2239 8.48383 27.7386 10.5523 26.2147 10.5523H3.78533C2.26144 10.5523 1.77611 8.48383 3.13967 7.79772L14.3543 2.15283C14.5548 2.05264 14.7759 2.00049 15 2.00049C15.2241 2.00049 15.4452 2.05264 15.6457 2.15283ZM25.8333 27.9997H4.16667C3.59203 27.9997 3.04093 27.7714 2.6346 27.3651C2.22827 26.9588 2 26.4077 2 25.8331C2 25.2584 2.22827 24.7073 2.6346 24.301C3.04093 23.8947 3.59203 23.6664 4.16667 23.6664H25.8333C26.408 23.6664 26.9591 23.8947 27.3654 24.301C27.7717 24.7073 28 25.2584 28 25.8331C28 26.4077 27.7717 26.9588 27.3654 27.3651C26.9591 27.7714 26.408 27.9997 25.8333 27.9997Z'
        stroke='currentColor'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export function AmbulanceIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='30'
      height='24'
      viewBox='0 0 30 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M7.63333 9.22222H13.4111M10.5222 6.33333V12.1111M10.9657 18.5461C10.9657 20.5756 9.35944 22.2222 7.37911 22.2222C5.39878 22.2222 3.79256 20.5756 3.79256 18.5447M10.9657 18.5461C10.9657 16.5167 9.35944 14.8686 7.37911 14.8686C5.39878 14.8686 3.79256 16.5152 3.79256 18.5447M10.9657 18.5461L19.0343 18.5447M3.79256 18.5447H2V3.44444C2 3.06135 2.15218 2.69395 2.42307 2.42307C2.69395 2.15218 3.06135 2 3.44444 2H17.5899C17.973 2 18.3404 2.15218 18.6113 2.42307C18.8822 2.69395 19.0343 3.06135 19.0343 3.44444V6.59622M19.0343 18.5447H19.9313M19.0343 18.5447V6.59622M19.0343 6.59622H22.3002C22.6854 6.59626 23.0667 6.67334 23.4217 6.82292C23.7767 6.9725 24.0982 7.19157 24.3672 7.46722L28 11.1924V18.5447H27.103M27.103 18.5447C27.103 20.5756 25.4982 22.2222 23.5179 22.2222C21.5376 22.2222 19.9313 20.5756 19.9313 18.5447M27.103 18.5447C27.103 16.5152 25.4982 14.8686 23.5179 14.8686C21.5376 14.8686 19.9313 16.5152 19.9313 18.5447'
        stroke='currentColor'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export function ComponentIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='30'
      height='30'
      viewBox='0 0 30 30'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M10.45 6.55L15 2L19.55 6.55L15 11.1L10.45 6.55ZM18.9 15.65L23.45 11.1L28 15.65L23.45 20.2L18.9 15.65ZM10.45 23.45L15 18.9L19.55 23.45L15 28L10.45 23.45ZM2 15L6.55 10.45L11.1 15L6.55 19.55L2 15Z'
        stroke='currentColor'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export function BrightIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='30'
      height='30'
      viewBox='0 0 30 30'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M2 15H6.33333M4.88889 4.88889L7.95256 7.95256M25.1111 4.88889L22.0474 7.95256M4.88889 25.1111L7.95256 22.0474M25.1111 25.1111L22.0474 22.0474M15 2V6.33333M15 28V23.6667M23.6667 15H28M19.3333 15C19.3333 16.1493 18.8768 17.2515 18.0641 18.0641C17.2515 18.8768 16.1493 19.3333 15 19.3333C13.8507 19.3333 12.7485 18.8768 11.9359 18.0641C11.1232 17.2515 10.6667 16.1493 10.6667 15C10.6667 13.8507 11.1232 12.7485 11.9359 11.9359C12.7485 11.1232 13.8507 10.6667 15 10.6667C16.1493 10.6667 17.2515 11.1232 18.0641 11.9359C18.8768 12.7485 19.3333 13.8507 19.3333 15Z'
        stroke='currentColor'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export function CrewAIIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='24'
      height='26'
      viewBox='0 0 24 26'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M12.0566 1.18005C6.73561 2.31905 1.05261 10.0691 1.00061 16.3721C0.94961 21.8991 4.10061 25.0521 9.68061 24.9991C16.3966 24.9491 23.9396 18.1291 21.7706 14.0991C21.0986 12.8071 20.3746 12.7551 19.0836 13.8921C17.4816 15.2871 17.4296 14.2021 18.8766 10.9991C20.6336 7.01905 20.5816 5.67705 18.5666 3.45505C16.6046 1.38805 14.5366 0.61105 12.0566 1.18005ZM17.3786 3.24805C19.6516 5.26305 19.7546 7.48405 17.8436 11.6681C16.4486 14.7681 15.6736 15.1831 14.0196 13.5281C12.7796 12.2881 12.6766 10.0681 13.7616 7.48405C14.8986 4.84905 14.7436 4.38405 13.1936 5.83105C9.47361 9.18905 6.73561 15.596 7.76961 18.334C8.23361 19.523 8.59461 19.729 10.5066 19.729C13.2966 19.729 16.8096 18.0241 18.4636 15.8031C20.2196 13.5291 21.2536 13.5291 21.2536 15.7511C21.2536 19.6261 14.7946 24.378 9.62861 24.378C3.37761 24.378 0.27761 19.6261 2.13761 12.9081C3.01561 9.91305 6.58061 5.00405 9.21461 3.24805C12.4696 1.07805 14.8986 1.07805 17.3786 3.24805Z'
        stroke='currentColor'
        fill='currentColor'
        strokeWidth='1.25'
      />
    </svg>
  )
}

export function HubspotIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      role='img'
      viewBox='0 0 24 24'
      xmlns='http://www.w3.org/2000/svg'
      fill='currentColor'
    >
      <path d='M18.164 7.93V5.084a2.198 2.198 0 001.267-1.978v-.067A2.2 2.2 0 0017.238.845h-.067a2.2 2.2 0 00-2.193 2.193v.067a2.196 2.196 0 001.252 1.973l.013.006v2.852a6.22 6.22 0 00-2.969 1.31l.012-.01-7.828-6.095A2.497 2.497 0 104.3 4.656l-.012.006 7.697 5.991a6.176 6.176 0 00-1.038 3.446c0 1.343.425 2.588 1.147 3.607l-.013-.02-2.342 2.343a1.968 1.968 0 00-.58-.095h-.002a2.033 2.033 0 102.033 2.033 1.978 1.978 0 00-.1-.595l.005.014 2.317-2.317a6.247 6.247 0 104.782-11.134l-.036-.005zm-.964 9.378a3.206 3.206 0 113.215-3.207v.002a3.206 3.206 0 01-3.207 3.207z' />
    </svg>
  )
}

export function SalesforceIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      role='img'
      viewBox='0 0 24 24'
      xmlns='http://www.w3.org/2000/svg'
      fill='currentColor'
    >
      <path d='M10.006 5.415a4.195 4.195 0 013.045-1.306c1.56 0 2.954.9 3.69 2.205.63-.3 1.35-.45 2.1-.45 2.85 0 5.159 2.34 5.159 5.22s-2.31 5.22-5.176 5.22c-.345 0-.69-.044-1.02-.104a3.75 3.75 0 01-3.3 1.95c-.6 0-1.155-.15-1.65-.375A4.314 4.314 0 018.88 20.4a4.302 4.302 0 01-4.05-2.82c-.27.062-.54.076-.825.076-2.204 0-4.005-1.8-4.005-4.05 0-1.5.811-2.805 2.01-3.51-.255-.57-.39-1.2-.39-1.846 0-2.58 2.1-4.65 4.65-4.65 1.53 0 2.85.705 3.72 1.8' />
    </svg>
  )
}

export function FirecrawlIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg viewBox='0 0 642 600' xmlns='http://www.w3.org/2000/svg' {...props}>
      <path
        d='M301 63C299 91 303 122 298 149C295 158 289 165 283 169C274 172 266 170 261 167C253 176 248 183 244 191C230 226 226 263 226 301C216 310 203 317 192 310C179 295 175 277 174 259C161 273 153 288 146 304C141 321 138 336 137 352C140 372 145 388 152 402C161 421 174 435 187 449C181 462 165 453 157 450C158 454 161 458 165 461C195 490 231 500 268 509C240 494 211 471 195 442C179 413 172 378 180 344C191 353 200 362 211 364C223 365 232 361 236 353C247 274 299 214 323 143C322 136 327 140 329 142C354 165 367 191 375 218C387 254 381 294 379 329C393 345 413 334 424 329C429 342 432 352 429 362C427 378 417 388 413 400C422 407 433 403 440 400C432 423 419 442 404 460C383 483 358 501 335 512C379 502 420 491 449 459C443 458 427 464 428 452C443 437 464 423 472 403C482 383 485 362 484 339C482 307 472 280 458 254C459 267 452 276 445 284C434 289 426 279 424 272C415 247 424 220 418 198C415 179 405 165 397 150C370 114 336 86 303 64'
        fill='rgb(253,76,31)'
      />
      <path
        d='M324 141C303 214 249 273 244 354C235 359 229 364 223 366C205 367 193 357 182 347C180 350 179 353 180 357C178 374 178 390 182 403C185 421 193 434 200 448C212 465 227 480 243 491C258 500 269 513 285 512C284 508 257 485 252 468C241 450 235 433 233 414C241 415 254 420 263 412C260 387 265 363 273 343C281 323 293 306 310 295C317 289 324 285 330 282C328 307 328 331 329 355C330 368 332 379 338 389C358 394 376 384 388 370C383 386 377 401 371 415C376 414 381 411 385 408C383 421 380 431 376 441C366 467 356 491 334 510C358 499 381 483 400 461C418 442 430 423 440 403C432 404 421 410 413 404C414 386 428 377 427 360C429 349 428 340 424 332C413 336 404 341 392 339C386 338 381 334 379 330C380 292 385 248 371 214C366 195 358 180 349 165C341 155 333 145 323 140'
        fill='rgb(254,156,69)'
      />
      <path
        d='M330 284C309 293 289 311 279 332C267 356 261 383 265 411C256 420 242 418 235 412C237 438 245 459 258 479C269 493 281 507 295 513C288 495 265 472 265 446C272 447 281 454 288 444C296 425 303 407 309 388C317 406 321 427 336 443C346 449 358 446 363 438C355 464 348 489 334 511C344 501 352 491 357 480C370 457 379 435 385 412C380 411 376 416 371 418C376 401 382 386 387 371C379 382 369 388 358 391C348 394 337 392 334 383C324 353 328 316 330 285'
        fill='rgb(254,220,87)'
      />
      <path
        d='M311 389C303 407 297 426 289 445C282 454 273 450 268 445C267 472 285 492 302 512C299 514 297 514 294 514C297 514 299 514 301 514C314 515 325 512 334 513C341 495 355 467 362 443C357 446 351 448 344 447C337 446 334 441 330 438C320 422 316 406 311 391'
        fill='rgb(251,250,202)'
      />
      <path
        d='M187 163C188 181 167 187 164 203C158 215 158 228 159 241C172 233 183 221 188 209C193 194 192 178 188 166'
        fill='rgb(253,76,31)'
      />
    </svg>
  )
}

export function JinaAIIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='30'
      height='14'
      viewBox='0 0 30 14'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M17.1516 5.25628C17.1724 5.25598 17.1932 5.25598 17.2146 5.25569C17.2831 5.2551 17.3514 5.2554 17.4197 5.25598C17.468 5.25569 17.5161 5.25569 17.5644 5.2554C17.6655 5.2554 17.7665 5.25569 17.8679 5.25628C17.9956 5.25686 18.1237 5.25657 18.2514 5.25569C19.3043 5.251 20.25 5.39426 21.0642 6.12112C21.0958 6.14632 21.1275 6.17122 21.1594 6.19612C21.8277 6.7885 22.2088 7.68733 22.2704 8.56624C22.278 8.73762 22.2777 8.90901 22.2768 9.08069C22.2771 9.1346 22.2771 9.1885 22.2771 9.24241C22.2774 9.35432 22.2771 9.46594 22.2768 9.57786C22.2762 9.72083 22.2765 9.8638 22.2771 10.0068C22.2774 10.1178 22.2774 10.2285 22.2771 10.3393C22.2771 10.3923 22.2771 10.445 22.2774 10.4978C22.2774 10.571 22.2771 10.6446 22.2765 10.7181C22.2768 10.7395 22.2771 10.7612 22.2771 10.7831C22.2753 10.9405 22.2408 11.0615 22.1335 11.1789C21.9882 11.292 21.8977 11.3102 21.7163 11.3108C21.6862 11.3108 21.6862 11.3108 21.6551 11.3111C21.5874 11.3114 21.5198 11.3114 21.4521 11.3114C21.4035 11.3114 21.3551 11.3116 21.3065 11.3116C21.1746 11.3122 21.0428 11.3122 20.9107 11.3125C20.8284 11.3125 20.746 11.3125 20.6637 11.3128C20.4059 11.3131 20.1481 11.3134 19.8903 11.3137C19.5926 11.3137 19.2953 11.3143 18.9976 11.3152C18.7676 11.3157 18.5376 11.316 18.3077 11.316C18.1703 11.316 18.0329 11.3163 17.8954 11.3169C17.7663 11.3172 17.6368 11.3172 17.5076 11.3172C17.4601 11.3169 17.4129 11.3172 17.3655 11.3175C17.3007 11.3178 17.2357 11.3178 17.1709 11.3175C17.1349 11.3175 17.0986 11.3175 17.0614 11.3175C16.933 11.3034 16.8343 11.2621 16.7385 11.1748C16.6243 11.0278 16.6067 10.9246 16.6067 10.7436C16.6064 10.7111 16.6064 10.7111 16.6064 10.678C16.6061 10.605 16.6058 10.5321 16.6058 10.4594C16.6055 10.4073 16.6055 10.3551 16.6052 10.303C16.6046 10.1313 16.6043 9.95989 16.604 9.78821C16.6037 9.72932 16.6037 9.67014 16.6037 9.61126C16.6032 9.33382 16.6026 9.05637 16.6023 8.77893C16.602 8.45872 16.6014 8.1385 16.6002 7.81858C16.5994 7.57102 16.5988 7.32346 16.5988 7.07591C16.5988 6.92825 16.5985 6.7803 16.5976 6.63264C16.597 6.49348 16.597 6.35432 16.5973 6.21516C16.5973 6.16419 16.597 6.11321 16.5967 6.06223C16.5929 5.56565 16.5929 5.56565 16.7283 5.3887C16.8583 5.27737 16.9811 5.25657 17.1516 5.25628Z'
        fill='currentColor'
      />
      <path
        d='M28.4893 5.83187C28.5341 5.86966 28.5786 5.90745 28.6229 5.94554C28.6407 5.95931 28.6586 5.97337 28.6771 5.98773C29.2217 6.42161 29.5281 7.12093 29.6483 7.79124C29.6509 7.83665 29.6524 7.88206 29.6527 7.92777C29.6529 7.96761 29.6529 7.96761 29.6532 8.00804C29.6532 8.03704 29.6535 8.06575 29.6535 8.09534C29.6538 8.12611 29.6538 8.15657 29.6541 8.18821C29.6547 8.28929 29.655 8.39036 29.6553 8.49144C29.6553 8.52601 29.6556 8.56058 29.6556 8.59603C29.6562 8.77884 29.6568 8.96165 29.6571 9.14446C29.6573 9.33314 29.6582 9.52181 29.6594 9.71077C29.6603 9.85579 29.6606 10.0011 29.6606 10.1461C29.6609 10.2159 29.6612 10.2856 29.6617 10.355C29.6623 10.4523 29.6623 10.5498 29.662 10.6471C29.6626 10.6902 29.6626 10.6902 29.6632 10.7341C29.662 10.9002 29.6474 11.0025 29.5311 11.1311C29.3805 11.2661 29.2481 11.265 29.0556 11.2632C29.0257 11.2635 28.9958 11.2638 28.9654 11.2638C28.8669 11.2644 28.7685 11.2641 28.67 11.2638C28.6012 11.2638 28.5323 11.2638 28.4635 11.2641C28.3191 11.2641 28.1746 11.2641 28.0302 11.2635C27.8462 11.2626 27.6625 11.2632 27.4785 11.2638C27.3362 11.2644 27.1938 11.2641 27.0517 11.2638C26.9837 11.2638 26.916 11.2638 26.8484 11.2641C25.9759 11.2667 25.1834 11.0508 24.5488 10.4268C24.5201 10.3981 24.4914 10.3691 24.4627 10.3401C24.4384 10.3155 24.4138 10.2909 24.3889 10.2657C23.8404 9.68851 23.5985 8.90687 23.6087 8.12435C23.6301 7.32191 23.9899 6.59681 24.5506 6.03343C25.6158 5.02562 27.3318 4.91839 28.4893 5.83187Z'
        fill='currentColor'
      />
      <path
        d='M8.6422 5.41793C8.7591 5.5858 8.7424 5.76246 8.74093 5.95904C8.74122 5.99566 8.74123 6.03228 8.74123 6.07037C8.74152 6.17086 8.74122 6.27164 8.74093 6.37213C8.74064 6.47818 8.74064 6.58424 8.74064 6.69C8.74093 6.86842 8.74035 7.04713 8.74005 7.22554C8.73947 7.43004 8.73917 7.63482 8.73947 7.83961C8.73947 8.03765 8.73947 8.23599 8.73917 8.43404C8.73888 8.51783 8.73888 8.60133 8.73888 8.68511C8.73947 9.82125 8.63869 10.9436 7.85119 11.8339C7.82951 11.8588 7.80753 11.8837 7.78527 11.9095C7.72023 11.9831 7.65402 12.0551 7.58751 12.1269C7.57199 12.1442 7.55675 12.1618 7.54064 12.1796C6.93712 12.8277 5.99757 13.1886 5.12746 13.2276C5.10197 13.2291 5.07619 13.2302 5.04982 13.2314C4.98771 13.2346 4.92531 13.2373 4.8629 13.2402C4.86085 12.0302 4.86085 10.8203 4.86261 9.61031C4.86291 9.44918 4.8629 9.28804 4.8632 9.12691C4.86349 8.63941 4.86466 8.15191 4.86671 7.6647C4.86789 7.45552 4.86847 7.24634 4.86876 7.03717C4.86876 6.84058 4.86964 6.64371 4.87082 6.44713C4.8714 6.37506 4.8714 6.30299 4.8714 6.23092C4.87111 6.13248 4.87199 6.03404 4.87287 5.9356C4.87257 5.90718 4.87228 5.87877 4.87199 5.84947C4.87521 5.66051 4.91417 5.53306 5.03869 5.38863C5.1673 5.27232 5.31642 5.28756 5.48107 5.28756C5.51066 5.28726 5.54025 5.28697 5.57101 5.28668C5.66886 5.28551 5.76701 5.28521 5.86515 5.28463C5.93341 5.28404 6.00167 5.28345 6.06994 5.28287C6.2132 5.2817 6.35617 5.28082 6.49914 5.27994C6.68253 5.27906 6.86564 5.27759 7.04874 5.27584C7.18996 5.27467 7.33087 5.27349 7.47179 5.27261C7.53947 5.27203 7.60685 5.27174 7.67453 5.27115C7.76886 5.27027 7.86349 5.26998 7.95812 5.26939C7.98566 5.2691 8.01349 5.26881 8.0422 5.26881C8.4632 5.26734 8.4632 5.26734 8.6422 5.41793Z'
        fill='currentColor'
      />
      <path
        d='M11.2636 5.26714C11.2897 5.26685 11.3155 5.26685 11.3421 5.26655C11.3705 5.26655 11.3987 5.26626 11.428 5.26626C11.4578 5.26597 11.4877 5.26597 11.5185 5.26567C11.6175 5.26509 11.7165 5.26479 11.8158 5.2645C11.8665 5.26421 11.8665 5.26421 11.9184 5.26421C12.0974 5.26362 12.2767 5.26304 12.456 5.26274C12.6408 5.26245 12.8257 5.26157 13.0109 5.2604C13.1532 5.25952 13.2953 5.25923 13.4377 5.25923C13.506 5.25894 13.5742 5.25864 13.6422 5.25806C13.7377 5.25747 13.8332 5.25747 13.9287 5.25776C13.9569 5.25718 13.985 5.25688 14.0137 5.25659C14.1895 5.25776 14.3278 5.28501 14.4731 5.38872C14.6096 5.55659 14.6231 5.68052 14.6234 5.89233C14.6234 5.91343 14.6237 5.93481 14.6237 5.95679C14.6239 6.02798 14.6237 6.09917 14.6237 6.17036C14.6239 6.22134 14.6239 6.27261 14.6239 6.32358C14.6242 6.46216 14.6245 6.60103 14.6245 6.73989C14.6245 6.85562 14.6245 6.97134 14.6245 7.08735C14.6248 7.36069 14.6248 7.63403 14.6248 7.90708C14.6248 8.18921 14.6251 8.47105 14.6257 8.75288C14.626 8.99487 14.6263 9.23687 14.6263 9.47886C14.6263 9.62358 14.6263 9.76802 14.6266 9.91245C14.6269 10.0487 14.6269 10.1846 14.6266 10.3206C14.6266 10.3704 14.6266 10.4202 14.6269 10.47C14.6272 10.5382 14.6269 10.6065 14.6269 10.6745C14.6269 10.7128 14.6269 10.7509 14.6269 10.7902C14.616 10.9469 14.5935 11.0638 14.4895 11.1839C14.2952 11.3498 14.1092 11.3404 13.8655 11.3401C13.8233 11.3404 13.8233 11.3404 13.7802 11.3404C13.687 11.3407 13.5939 11.3407 13.501 11.341C13.436 11.341 13.3712 11.341 13.3065 11.3413C13.1705 11.3413 13.0346 11.3413 12.8987 11.3413C12.7249 11.3413 12.5509 11.3418 12.3772 11.3421C12.2433 11.3424 12.1091 11.3427 11.9752 11.3427C11.9114 11.3427 11.8472 11.3427 11.783 11.343C11.6934 11.3433 11.6034 11.3433 11.5138 11.343C11.4745 11.3433 11.4745 11.3433 11.4341 11.3436C11.2425 11.3424 11.0609 11.3348 10.9044 11.2132C10.7761 11.0486 10.7412 10.9103 10.7412 10.7035C10.7409 10.6821 10.7409 10.6607 10.7406 10.6387C10.7404 10.5672 10.7406 10.496 10.7409 10.4249C10.7409 10.3736 10.7406 10.322 10.7404 10.2708C10.7401 10.1319 10.7404 9.99272 10.7406 9.85386C10.7406 9.70796 10.7406 9.56235 10.7404 9.41675C10.7404 9.17183 10.7404 8.9272 10.7409 8.68257C10.7412 8.40015 10.7412 8.11743 10.7406 7.83472C10.7404 7.59185 10.7404 7.34897 10.7404 7.1061C10.7406 6.96108 10.7406 6.81636 10.7404 6.67134C10.7401 6.53511 10.7404 6.39858 10.7406 6.26235C10.7409 6.21255 10.7409 6.16245 10.7406 6.11265C10.7404 6.04409 10.7406 5.97583 10.7412 5.90757C10.7412 5.86919 10.7412 5.8311 10.7412 5.79185C10.7582 5.62397 10.7963 5.47515 10.9264 5.36118C11.0421 5.28325 11.1262 5.26802 11.2636 5.26714Z'
        fill='currentColor'
      />
      <path
        d='M3.58833 9.8857C3.97827 10.2715 4.18628 10.7596 4.20093 11.3066C4.18452 11.8662 3.96245 12.3628 3.56226 12.7527C3.194 13.0776 2.70064 13.2692 2.20523 13.2466C2.18326 13.2446 2.16158 13.2425 2.13902 13.2402C2.11031 13.2376 2.08159 13.2349 2.05201 13.232C1.51617 13.1658 1.08199 12.9168 0.732767 12.5078C0.370071 12.0132 0.294193 11.4762 0.364505 10.8783C0.476126 10.3738 0.798392 9.944 1.23081 9.66598C1.99693 9.22125 2.9148 9.30006 3.58833 9.8857Z'
        fill='currentColor'
      />
      <path
        d='M12.6714 0.74873C12.6995 0.748437 12.7273 0.748145 12.756 0.747559C13.2318 0.749609 13.693 0.952637 14.0349 1.28105C14.3838 1.6335 14.5947 2.0791 14.6 2.57627C14.5965 3.14111 14.4632 3.62246 14.063 4.04111C13.6912 4.40293 13.2008 4.59365 12.686 4.60859C12.1434 4.59453 11.6864 4.40234 11.2941 4.02969C10.9086 3.62305 10.7357 3.13232 10.7466 2.57598C10.7659 2.08145 10.9883 1.60625 11.3384 1.25791C11.7245 0.919824 12.1578 0.742578 12.6714 0.74873Z'
        fill='currentColor'
      />
    </svg>
  )
}

export function TranslateIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      stroke='currentColor'
      strokeWidth='2'
      strokeLinecap='round'
      strokeLinejoin='round'
      {...props}
    >
      <path d='m5 8 6 6' />
      <path d='m4 14 6-6 2-3' />
      <path d='M2 5h12' />
      <path d='M7 2h1' />
      <path d='m22 22-5-10-5 10' />
      <path d='M14 18h6' />
    </svg>
  )
}

export function SlackIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg' {...props}>
      <g>
        <path
          d='M53.8412698,161.320635 C53.8412698,176.152381 41.8539683,188.139683 27.0222222,188.139683 C12.1904762,188.139683 0.203174603,176.152381 0.203174603,161.320635 C0.203174603,146.488889 12.1904762,134.501587 27.0222222,134.501587 L53.8412698,134.501587 L53.8412698,161.320635 Z M67.2507937,161.320635 C67.2507937,146.488889 79.2380952,134.501587 94.0698413,134.501587 C108.901587,134.501587 120.888889,146.488889 120.888889,161.320635 L120.888889,228.368254 C120.888889,243.2 108.901587,255.187302 94.0698413,255.187302 C79.2380952,255.187302 67.2507937,243.2 67.2507937,228.368254 L67.2507937,161.320635 Z'
          fill='#E01E5A'
        />
        <path
          d='M94.0698413,53.6380952 C79.2380952,53.6380952 67.2507937,41.6507937 67.2507937,26.8190476 C67.2507937,11.9873016 79.2380952,-7.10542736e-15 94.0698413,-7.10542736e-15 C108.901587,-7.10542736e-15 120.888889,11.9873016 120.888889,26.8190476 L120.888889,53.6380952 L94.0698413,53.6380952 Z M94.0698413,67.2507937 C108.901587,67.2507937 120.888889,79.2380952 120.888889,94.0698413 C120.888889,108.901587 108.901587,120.888889 94.0698413,120.888889 L26.8190476,120.888889 C11.9873016,120.888889 0,108.901587 0,94.0698413 C0,79.2380952 11.9873016,67.2507937 26.8190476,67.2507937 L94.0698413,67.2507937 Z'
          fill='#36C5F0'
        />
        <path
          d='M201.549206,94.0698413 C201.549206,79.2380952 213.536508,67.2507937 228.368254,67.2507937 C243.2,67.2507937 255.187302,79.2380952 255.187302,94.0698413 C255.187302,108.901587 243.2,120.888889 228.368254,120.888889 L201.549206,120.888889 L201.549206,94.0698413 Z M188.139683,94.0698413 C188.139683,108.901587 176.152381,120.888889 161.320635,120.888889 C146.488889,120.888889 134.501587,108.901587 134.501587,94.0698413 L134.501587,26.8190476 C134.501587,11.9873016 146.488889,-1.42108547e-14 161.320635,-1.42108547e-14 C176.152381,-1.42108547e-14 188.139683,11.9873016 188.139683,26.8190476 L188.139683,94.0698413 Z'
          fill='#2EB67D'
        />
        <path
          d='M161.320635,201.549206 C176.152381,201.549206 188.139683,213.536508 188.139683,228.368254 C188.139683,243.2 176.152381,255.187302 161.320635,255.187302 C146.488889,255.187302 134.501587,243.2 134.501587,228.368254 L134.501587,201.549206 L161.320635,201.549206 Z M161.320635,188.139683 C146.488889,188.139683 134.501587,176.152381 134.501587,161.320635 C134.501587,146.488889 146.488889,134.501587 161.320635,134.501587 L228.571429,134.501587 C243.403175,134.501587 255.390476,146.488889 255.390476,161.320635 C255.390476,176.152381 243.403175,188.139683 228.571429,188.139683 L161.320635,188.139683 Z'
          fill='#ECB22E'
        />
      </g>
    </svg>
  )
}

export function GithubIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg {...props} width='26' height='26' viewBox='0 0 26 26' xmlns='http://www.w3.org/2000/svg'>
      <path
        d='M13 0C11.2928 0 9.60235 0.336255 8.02511 0.989566C6.44788 1.64288 5.01477 2.60045 3.80761 3.80761C1.36964 6.24558 0 9.55219 0 13C0 18.746 3.731 23.621 8.892 25.35C9.542 25.454 9.75 25.051 9.75 24.7V22.503C6.149 23.283 5.382 20.761 5.382 20.761C4.784 19.253 3.939 18.85 3.939 18.85C2.756 18.044 4.03 18.07 4.03 18.07C5.33 18.161 6.019 19.409 6.019 19.409C7.15 21.385 9.061 20.8 9.802 20.488C9.919 19.643 10.257 19.071 10.621 18.746C7.735 18.421 4.706 17.303 4.706 12.35C4.706 10.907 5.2 9.75 6.045 8.827C5.915 8.502 5.46 7.15 6.175 5.395C6.175 5.395 7.267 5.044 9.75 6.721C10.777 6.435 11.895 6.292 13 6.292C14.105 6.292 15.223 6.435 16.25 6.721C18.733 5.044 19.825 5.395 19.825 5.395C20.54 7.15 20.085 8.502 19.955 8.827C20.8 9.75 21.294 10.907 21.294 12.35C21.294 17.316 18.252 18.408 15.353 18.733C15.821 19.136 16.25 19.929 16.25 21.138V24.7C16.25 25.051 16.458 25.467 17.121 25.35C22.282 23.608 26 18.746 26 13C26 11.2928 25.6637 9.60235 25.0104 8.02511C24.3571 6.44788 23.3995 5.01477 22.1924 3.80761C20.9852 2.60045 19.5521 1.64288 17.9749 0.989566C16.3977 0.336255 14.7072 0 13 0Z'
        fill='currentColor'
      />
    </svg>
  )
}

export function SerperIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg viewBox='0 0 654 600' xmlns='http://www.w3.org/2000/svg' {...props}>
      <path
        d='M324 38C356 37 389 36 417 47C452 56 484 72 509 94C539 118 561 145 577 176C593 205 601 238 606 271C610 343 590 403 552 452C528 482 499 507 467 523C438 539 404 547 372 552C297 556 235 534 184 492C133 449 103 392 93 330C93 292 89 255 102 224C112 189 128 158 149 132C194 78 255 46 322 38'
        fill='rgb(71,97,118)'
      />
      <path
        d='M326 39C286 43 250 55 217 75C185 94 156 120 137 150C100 204 87 266 95 336C107 402 142 462 198 502C249 538 309 556 378 551C415 545 449 533 477 516C511 497 535 472 557 445C592 393 611 333 605 265C595 196 563 140 511 95C484 73 452 57 419 48C390 38 359 38 327 39'
        fill='rgb(71,97,119)'
      />
      <path
        d='M342 40C407 42 465 61 513 103C541 126 562 155 576 184C592 217 600 251 600 288C602 357 579 416 535 465C510 493 478 515 445 528C416 541 385 546 352 546C284 548 225 523 178 481C130 436 103 379 96 313C94 244 113 186 151 138C179 103 209 80 245 64C276 50 307 44 340 41'
        fill='rgb(71,97,119)'
      />
      <path
        d='M344 42C309 44 277 51 247 64C209 81 180 103 153 136C114 186 95 244 96 312C104 379 131 435 177 480C225 522 284 547 351 546C385 545 416 540 443 528C478 514 509 492 533 466C578 416 601 357 600 289C599 251 591 217 576 187C561 156 541 127 515 105C466 63 409 44 346 41'
        fill='rgb(71,97,118)'
      />
      <path
        d='M327 81C378 78 423 89 462 114C511 144 546 196 557 248C567 306 559 363 530 406C498 457 448 492 395 503C338 513 282 506 239 477C192 450 156 402 143 351C126 296 137 235 163 190C198 130 258 89 325 82'
        fill='rgb(44,56,71)'
      />
      <path
        d='M329 83C260 89 199 129 165 189C138 235 127 296 144 349C157 401 193 449 237 475C282 505 338 512 393 503C448 491 497 457 529 408C558 363 566 306 557 250C545 196 511 145 464 116C424 91 380 79 330 82'
        fill='rgb(43,55,70)'
      />
      <path
        d='M334 87C381 83 423 94 458 117C510 148 544 201 554 258C562 317 551 370 521 412C487 460 440 491 385 500C331 507 281 499 241 473C191 444 157 394 145 339C136 284 143 227 171 186C207 129 265 91 332 87'
        fill='rgb(41,53,67)'
      />
      <path
        d='M335 88C267 90 208 129 173 184C144 227 137 284 145 338C158 393 191 443 240 471C281 498 331 506 384 500C439 490 487 459 519 413C550 370 561 317 554 259C543 201 509 149 460 119C424 96 383 85 337 88'
        fill='rgb(41,53,67)'
      />
      <path
        d='M347 166C361 164 373 169 387 168C412 180 437 193 447 221C449 232 443 243 434 248C403 245 398 204 365 207C338 206 315 210 297 228C294 238 289 257 303 260C337 280 382 276 417 292C436 300 448 314 455 330C457 349 462 373 449 385C435 408 413 418 391 427C361 429 328 436 304 421C280 413 260 392 250 370C246 356 255 343 268 343C293 360 316 398 356 389C382 390 409 380 416 357C389 295 298 335 260 276C246 256 248 233 258 214C279 184 309 167 346 167'
        fill='rgb(121,172,205)'
      />
      <path
        d='M349 168C312 167 280 183 259 212C249 233 247 256 260 274C299 334 390 294 422 354C409 381 382 391 357 389C316 399 293 361 272 342C255 344 247 356 251 368C260 391 280 412 302 420C328 435 361 428 389 428C412 417 434 407 447 386C461 373 456 349 456 332C428 270 351 289 304 262C288 258 293 239 295 229C314 209 338 204 363 204C398 203 403 244 431 249C443 242 448 232 449 222C436 193 412 181 388 172C374 170 363 166 350 167'
        fill='rgb(125,177,211)'
      />
      <path
        d='M349 169C386 169 425 185 441 220C444 231 441 240 432 243C409 237 402 209 380 206C347 200 314 201 293 226C290 238 286 256 297 262C332 283 375 281 411 295C431 304 446 317 452 337C455 360 452 383 434 396C415 415 391 421 366 426C338 430 316 422 295 413C276 402 261 385 254 366C254 353 261 343 275 348C290 381 325 398 360 394C388 395 411 382 420 360C425 342 413 334 404 327C359 304 298 318 265 276C253 254 255 235 261 214C280 187 314 173 346 170'
        fill='rgb(137,195,233)'
      />
      <path
        d='M349 171C316 173 281 187 263 214C256 235 254 254 266 273C300 316 359 304 401 325C413 333 426 342 422 358C412 382 388 396 363 395C326 399 290 382 278 348C262 345 254 353 253 365C261 384 277 401 292 411C316 421 338 429 365 426C390 420 415 414 432 398C451 383 454 360 453 338C445 317 430 305 413 296C375 282 332 284 299 264C285 257 288 239 291 228C304 212 319 205 336 202C378 193 403 213 423 244C438 244 443 232 441 222C425 186 388 171 352 170'
        fill='rgb(139,198,236)'
      />
    </svg>
  )
}

export function TavilyIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg viewBox='0 0 600 600' xmlns='http://www.w3.org/2000/svg' {...props}>
      <path
        d='M432 291C415 294 418 313 417 326C380 328 342 327 306 328C316 344 312 368 301 381C339 384 377 383 414 384C419 393 415 404 419 412C424 419 431 422 437 421C554 393 539 314 425 290'
        fill='rgb(248,202,81)'
      />
      <path
        d='M263 87C260 88 257 89 255 93C237 121 219 147 204 174C203 184 206 191 212 195C222 198 231 196 239 197C241 238 240 277 241 316C257 307 276 309 294 308C296 273 295 234 296 199C309 196 328 200 333 183C314 149 299 103 267 83'
        fill='rgb(109,164,249)'
      />
      <path
        d='M314 356L316 354C386 355 457 354 527 355C504 385 469 400 440 421C431 421 424 418 421 411C415 402 420 389 416 383C384 371 284 406 312 358'
        fill='rgb(250,188,28)'
      />
      <path
        d='M314 356C281 405 384 369 410 384C422 388 415 402 421 409C425 417 431 420 437 420C469 400 504 384 529 360C456 355 386 356 317 355'
        fill='rgb(251,186,23)'
      />
      <path
        d='M264 325C271 325 290 329 283 339C236 384 186 436 139 482C133 481 133 477 131 474C133 477 133 481 135 482C174 490 213 472 250 466C261 447 246 435 235 426C254 406 271 389 289 372C303 352 287 324 266 326'
        fill='rgb(251,156,158)'
      />
      <path
        d='M263 327C260 328 256 328 253 330C233 348 216 367 197 384C188 381 183 371 175 368C166 367 161 369 156 372C148 409 133 447 133 482C173 430 281 366 277 323'
        fill='rgb(248,56,63)'
      />
      <path
        d='M258 326C235 341 218 365 198 382C186 376 176 360 161 368L160 369L157 369C149 378 150 391 146 401C150 391 149 379 157 370C174 359 185 376 195 385C219 365 238 337 262 325'
        fill='rgb(242,165,165)'
      />
    </svg>
  )
}

export function ConnectIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='24'
      height='24'
      viewBox='-2 -2 28 28'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M24 16C24 17.4667 23.4778 18.7222 22.4333 19.7667C21.3889 20.8111 20.1333 21.3333 18.6667 21.3333H7.76667C7.47778 22.1111 6.99467 22.7498 6.31733 23.2493C5.64 23.7489 4.86756 23.9991 4 24C2.88889 24 1.94444 23.6111 1.16667 22.8333C0.388889 22.0556 0 21.1111 0 20C0 18.8889 0.388889 17.9444 1.16667 17.1667C1.94444 16.3889 2.88889 16 4 16C4.86667 16 5.63911 16.2498 6.31733 16.7493C6.99556 17.2489 7.47867 17.888 7.76667 18.6667H18.6667C19.4 18.6667 20.028 18.4053 20.5507 17.8827C21.0733 17.36 21.3342 16.7324 21.3333 16C21.3324 15.2676 21.0716 14.6396 20.5507 14.116C20.0298 13.5924 19.4018 13.3316 18.6667 13.3333L5.33333 13.3333C3.86667 13.3333 2.61111 12.8111 1.56667 11.7667C0.522222 10.7222 0 9.46667 0 8C0 6.53334 0.522222 5.27778 1.56667 4.23334C2.61111 3.18889 3.86667 2.66667 5.33333 2.66667H16.2333C16.5222 1.88889 17.0058 1.24978 17.684 0.749337C18.3622 0.248893 19.1342 -0.000885312 20 3.57628e-06C21.1111 3.57628e-06 22.0556 0.388892 22.8333 1.16667C23.6111 1.94445 24 2.88889 24 4C24 5.11111 23.6111 6.05556 22.8333 6.83334C22.0556 7.61111 21.1111 8 20 8C19.1333 8 18.3556 7.74978 17.6667 7.24934C16.9778 6.74889 16.5 6.11022 16.2333 5.33334H5.33333C4.6 5.33334 3.97244 5.59422 3.45067 6.116C2.92889 6.63778 2.66756 7.26578 2.66667 8C2.66578 8.73422 2.92711 9.36178 3.45067 9.88267C3.97422 10.4036 4.60178 10.6649 5.33333 10.6667L18.6667 10.6667C20.1333 10.6667 21.3889 11.1889 22.4333 12.2333C23.4778 13.2778 24 14.5333 24 16ZM5.33333 20C5.33333 19.6222 5.20533 19.3053 4.94933 19.0493C4.69333 18.7933 4.37689 18.6658 4 18.6667C3.62311 18.6676 3.30667 18.7956 3.05067 19.0507C2.79467 19.3058 2.66667 19.6222 2.66667 20C2.66667 20.3778 2.79467 20.6942 3.05067 20.9493C3.30667 21.2044 3.62311 21.3324 4 21.3333C4.37689 21.3342 4.69378 21.2062 4.95067 20.9493C5.20756 20.6924 5.33511 20.376 5.33333 20ZM21.3333 4C21.3333 3.62223 21.2053 3.30534 20.9493 3.04934C20.6933 2.79334 20.3769 2.66578 20 2.66667C19.6231 2.66756 19.3067 2.79556 19.0507 3.05067C18.7947 3.30578 18.6667 3.62223 18.6667 4C18.6667 4.37778 18.7947 4.69422 19.0507 4.94934C19.3067 5.20445 19.6231 5.33245 20 5.33334C20.3769 5.33423 20.6938 5.20622 20.9507 4.94934C21.2076 4.69245 21.3351 4.376 21.3333 4Z'
        fill='currentColor'
      />
    </svg>
  )
}

export function YouTubeIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      width='28'
      height='20'
      viewBox='0 0 28 20'
      fill='currentColor'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M11.2 14L18.466 9.8L11.2 5.6V14ZM27.384 3.038C27.566 3.696 27.692 4.578 27.776 5.698C27.874 6.818 27.916 7.784 27.916 8.624L28 9.8C28 12.866 27.776 15.12 27.384 16.562C27.034 17.822 26.222 18.634 24.962 18.984C24.304 19.166 23.1 19.292 21.252 19.376C19.432 19.474 17.766 19.516 16.226 19.516L14 19.6C8.134 19.6 4.48 19.376 3.038 18.984C1.778 18.634 0.966 17.822 0.616 16.562C0.434 15.904 0.308 15.022 0.224 13.902C0.126 12.782 0.0839999 11.816 0.0839999 10.976L0 9.8C0 6.734 0.224 4.48 0.616 3.038C0.966 1.778 1.778 0.966 3.038 0.616C3.696 0.434 4.9 0.308 6.748 0.224C8.568 0.126 10.234 0.0839999 11.774 0.0839999L14 0C19.866 0 23.52 0.224 24.962 0.616C26.222 0.966 27.034 1.778 27.384 3.038Z'
        fill='currentColor'
      />
    </svg>
  )
}

export function PerplexityIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg width='1em' height='1em' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg' {...props}>
      <path
        d='M19.785 0v7.272H22.5V17.62h-2.935V24l-7.037-6.194v6.145h-1.091v-6.152L4.392 24v-6.465H1.5V7.188h2.884V0l7.053 6.494V.19h1.09v6.49L19.786 0zm-7.257 9.044v7.319l5.946 5.234V14.44l-5.946-5.397zm-1.099-.08l-5.946 5.398v7.235l5.946-5.234V8.965zm8.136 7.58h1.844V8.349H13.46l6.105 5.54v2.655zm-8.982-8.28H2.59v8.195h1.8v-2.576l6.192-5.62zM5.475 2.476v4.71h5.115l-5.115-4.71zm13.219 0l-5.115 4.71h5.115v-4.71z'
        fill='currentColor'
        fillRule='nonzero'
      />
    </svg>
  )
}

export function NotionIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 50 50' width='1em' height='1em' {...props}>
      <path
        d='M31.494141 5.1503906L5.9277344 7.0019531A1.0001 1.0001 0 005.9042969 7.0039062A1.0001 1.0001 0 005.8652344 7.0097656A1.0001 1.0001 0 005.7929688 7.0214844A1.0001 1.0001 0 005.7636719 7.0292969A1.0001 1.0001 0 005.7304688 7.0371094A1.0001 1.0001 0 005.6582031 7.0605469A1.0001 1.0001 0 005.6113281 7.0800781A1.0001 1.0001 0 005.5839844 7.0917969A1.0001 1.0001 0 005.4335938 7.1777344A1.0001 1.0001 0 005.4082031 7.1933594A1.0001 1.0001 0 005.3476562 7.2421875A1.0001 1.0001 0 005.3359375 7.2539062A1.0001 1.0001 0 005.2871094 7.2988281A1.0001 1.0001 0 005.2578125 7.3320312A1.0001 1.0001 0 005.2148438 7.3828125A1.0001 1.0001 0 005.1992188 7.4023438A1.0001 1.0001 0 005.15625 7.4648438A1.0001 1.0001 0 005.1445312 7.484375A1.0001 1.0001 0 005.1074219 7.5488281A1.0001 1.0001 0 005.09375 7.5761719A1.0001 1.0001 0 005.0644531 7.6484375A1.0001 1.0001 0 005.0605469 7.65625A1.0001 1.0001 0 005.015625 7.8300781A1.0001 1.0001 0 005.0097656 7.8613281A1.0001 1.0001 0 005.0019531 7.9414062A1.0001 1.0001 0 005.0019531 7.9453125A1.0001 1.0001 0 005 8L5 33.738281C5 34.76391 5.3151542 35.766862 5.9042969 36.607422A1.0001 1.0001 0 005.953125 36.671875L12.126953 44.101562A1.0001 1.0001 0 0012.359375 44.382812L12.75 44.851562A1.0006635 1.0006635 0 0012.917969 45.011719C13.50508 45.581386 14.317167 45.917563 15.193359 45.861328L42.193359 44.119141C43.762433 44.017718 45 42.697027 45 41.125L45 15.132812C45 14.209354 44.565523 13.390672 43.904297 12.839844A1.0008168 1.0008168 0 0043.748047 12.695312L43.263672 12.337891A1.0001 1.0001 0 0043.0625 12.189453L34.824219 6.1132812C33.865071 5.4054876 32.682705 5.0641541 31.494141 5.1503906zM31.638672 7.1445312C32.352108 7.0927682 33.061867 7.29845 33.636719 7.7226562L39.767578 12.246094L14.742188 13.884766C13.880567 13.941006 13.037689 13.622196 12.425781 13.011719L12.423828 13.011719L8.2539062 8.8398438L31.638672 7.1445312zM7 10.414062L11.011719 14.425781L12 15.414062L12 40.818359L7.5390625 35.449219C7.1899317 34.947488 7 34.351269 7 33.738281L7 10.414062zM41.935547 14.134766C42.526748 14.096822 43 14.54116 43 15.132812L43 41.125C43 41.660973 42.59938 42.08847 42.064453 42.123047L15.064453 43.865234C14.770856 43.884078 14.506356 43.783483 14.314453 43.605469A1.0006635 1.0006635 0 0014.3125 43.603516C14.3125 43.603516 14.310547 43.601562 14.310547 43.601562C14.306465 43.597733 14.304796 43.59179 14.300781 43.587891A1.0006635 1.0006635 0 0014.289062 43.572266C14.112238 43.393435 14 43.149431 14 42.867188L14 16.875C14 16.337536 14.39999 15.911571 14.935547 15.876953L41.935547 14.134766zM38.496094 19L33.421875 19.28125C32.647875 19.36125 31.746094 19.938 31.746094 20.875L33.996094 21.0625L33.996094 31.753906L26.214844 19.751953L20.382812 20.080078C19.291812 20.160078 18.994141 20.970953 18.994141 22.001953L21.244141 22.001953L21.244141 37.566406C21.244141 37.566406 20.191844 37.850406 19.839844 37.941406C19.091844 38.134406 18.994141 38.784906 18.994141 39.253906C18.994141 39.253906 22.746656 39.065547 24.472656 38.935547C26.431656 38.785547 26.496094 37.472656 26.496094 37.472656L24.246094 37.003906L24.246094 25.470703C24.246094 25.470703 29.965844 34.660328 31.714844 37.361328C32.537844 38.630328 33.152375 38.878906 34.234375 38.878906C35.122375 38.878906 35.962141 38.616594 36.994141 38.058594L36.994141 20.697266C36.994141 20.697266 37.184203 20.687141 37.783203 20.494141C38.466203 20.273141 38.496094 19.656 38.496094 19z'
        fill='currentColor'
      />
    </svg>
  )
}

export function GmailIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      viewBox='0 0 48 48'
      width='96px'
      height='96px'
      {...props}
    >
      <path fill='#4caf50' d='M45,16.2l-5,2.75l-5,4.75L35,40h7c1.657,0,3-1.343,3-3V16.2z' />
      <path fill='#1e88e5' d='M3,16.2l3.614,1.71L13,23.7V40H6c-1.657,0-3-1.343-3-3V16.2z' />
      <polygon
        fill='#e53935'
        points='35,11.2 24,19.45 13,11.2 12,17 13,23.7 24,31.95 35,23.7 36,17'
      />
      <path
        fill='#c62828'
        d='M3,12.298V16.2l10,7.5V11.2L9.876,8.859C9.132,8.301,8.228,8,7.298,8h0C4.924,8,3,9.924,3,12.298z'
      />
      <path
        fill='#fbc02d'
        d='M45,12.298V16.2l-10,7.5V11.2l3.124-2.341C38.868,8.301,39.772,8,40.702,8h0 C43.076,8,45,9.924,45,12.298z'
      />
    </svg>
  )
}

export function GoogleDriveIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      viewBox='0 0 87.3 78'
      width='1em'
      height='1em'
      {...props}
    >
      <path
        d='m6.6 66.85 3.85 6.65c.8 1.4 1.95 2.5 3.3 3.3l13.75-23.8h-27.5c0 1.55.4 3.1 1.2 4.5z'
        fill='#0066da'
      />
      <path
        d='m43.65 25-13.75-23.8c-1.35.8-2.5 1.9-3.3 3.3l-25.4 44a9.06 9.06 0 0 0 -1.2 4.5h27.5z'
        fill='#00ac47'
      />
      <path
        d='m73.55 76.8c1.35-.8 2.5-1.9 3.3-3.3l1.6-2.75 7.65-13.25c.8-1.4 1.2-2.95 1.2-4.5h-27.502l5.852 11.5z'
        fill='#ea4335'
      />
      <path
        d='m43.65 25 13.75-23.8c-1.35-.8-2.9-1.2-4.5-1.2h-18.5c-1.6 0-3.15.45-4.5 1.2z'
        fill='#00832d'
      />
      <path
        d='m59.8 53h-32.3l-13.75 23.8c1.35.8 2.9 1.2 4.5 1.2h50.8c1.6 0 3.15-.45 4.5-1.2z'
        fill='#2684fc'
      />
      <path
        d='m73.4 26.5-12.7-22c-.8-1.4-1.95-2.5-3.3-3.3l-13.75 23.8 16.15 28h27.45c0-1.55-.4-3.1-1.2-4.5z'
        fill='#ffba00'
      />
    </svg>
  )
}

export function xAIIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      viewBox='0 0 841.89 595.28'
      width='1em'
      height='1em'
      {...props}
    >
      <g>
        <polygon
          points='557.09,211.99 565.4,538.36 631.96,538.36 640.28,93.18'
          fill='currentColor'
        />
        <polygon
          points='640.28,56.91 538.72,56.91 379.35,284.53 430.13,357.05'
          fill='currentColor'
        />
        <polygon
          points='201.61,538.36 303.17,538.36 353.96,465.84 303.17,393.31'
          fill='currentColor'
        />
        <polygon
          points='201.61,211.99 430.13,538.36 531.69,538.36 303.17,211.99'
          fill='currentColor'
        />
      </g>
    </svg>
  )
}

export function xIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 50 50' width='1em' height='1em' {...props}>
      <path
        d='M 5.9199219 6 L 20.582031 27.375 L 6.2304688 44 L 9.4101562 44 L 21.986328 29.421875 L 31.986328 44 L 44 44 L 28.681641 21.669922 L 42.199219 6 L 39.029297 6 L 27.275391 19.617188 L 17.933594 6 L 5.9199219 6 z M 9.7167969 8 L 16.880859 8 L 40.203125 42 L 33.039062 42 L 9.7167969 8 z'
        fill='currentColor'
      />
    </svg>
  )
}

export function GoogleSheetsIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns='http://www.w3.org/2000/svg'
      viewBox='0 0 48 48'
      width='96px'
      height='96px'
    >
      <path
        fill='#43a047'
        d='M37,45H11c-1.657,0-3-1.343-3-3V6c0-1.657,1.343-3,3-3h19l10,10v29C40,43.657,38.657,45,37,45z'
      />
      <path fill='#c8e6c9' d='M40 13L30 13 30 3z' />
      <path fill='#2e7d32' d='M30 13L40 23 40 13z' />
      <path
        fill='#e8f5e9'
        d='M31,23H17h-2v2v2v2v2v2v2v2h18v-2v-2v-2v-2v-2v-2v-2H31z M17,25h4v2h-4V25z M17,29h4v2h-4V29z M17,33h4v2h-4V33z M31,35h-8v-2h8V35z M31,31h-8v-2h8V31z M31,27h-8v-2h8V27z'
      />
    </svg>
  )
}

export const S3Icon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    height='310'
    preserveAspectRatio='xMidYMid'
    viewBox='0 0 256 310'
    width='256'
    xmlns='http://www.w3.org/2000/svg'
  >
    <path d='m20.624 53.686-20.624 10.314v181.02l20.624 10.254.124-.149v-201.297z' fill='#8c3123' />
    <path d='m131 229-110.376 26.274v-201.588l110.376 25.701z' fill='#e05243' />
    <path d='m81.178 187.866 46.818 5.96.294-.678.263-76.77-.557-.6-46.818 5.874z' fill='#8c3123' />
    <path
      d='m127.996 229.295 107.371 26.035.169-.269-.003-201.195-.17-.18-107.367 25.996z'
      fill='#8c3123'
    />
    <path d='m174.827 187.866-46.831 5.96v-78.048l46.831 5.874z' fill='#e05243' />
    <path d='m174.827 89.631-46.831 8.535-46.818-8.535 46.759-12.256z' fill='#5e1f18' />
    <path d='m174.827 219.801-46.831-8.591-46.818 8.591 46.761 13.053z' fill='#f2b0a9' />
    <path
      d='m81.178 89.631 46.818-11.586.379-.117v-77.615l-.379-.313-46.818 23.413z'
      fill='#8c3123'
    />
    <path d='m174.827 89.631-46.831-11.586v-78.045l46.831 23.413z' fill='#e05243' />
    <path
      d='m127.996 309.428-46.823-23.405v-66.217l46.823 11.582.689.783-.187 75.906z'
      fill='#8c3123'
    />
    <g fill='#e05243'>
      <path d='m127.996 309.428 46.827-23.405v-66.217l-46.827 11.582z' />
      <path d='m235.367 53.686 20.633 10.314v181.02l-20.633 10.31z' />
    </g>
  </svg>
)

export function GoogleIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg {...props} xmlns='http://www.w3.org/2000/svg' viewBox='0 0 48 48' width='24' height='24'>
      <path
        fill='#fbc02d'
        d='M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12	s5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24s8.955,20,20,20	s20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z'
      />
      <path
        fill='#e53935'
        d='M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039	l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z'
      />
      <path
        fill='#4caf50'
        d='M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36	c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z'
      />
      <path
        fill='#1565c0'
        d='M43.611,20.083L43.595,20L42,20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571	c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z'
      />
    </svg>
  )
}

export function DiscordIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='800px'
      height='800px'
      viewBox='0 -28.5 256 256'
      version='1.1'
      xmlns='http://www.w3.org/2000/svg'
      xmlnsXlink='http://www.w3.org/1999/xlink'
      preserveAspectRatio='xMidYMid'
    >
      <g>
        <path
          d='M216.856339,16.5966031 C200.285002,8.84328665 182.566144,3.2084988 164.041564,0 C161.766523,4.11318106 159.108624,9.64549908 157.276099,14.0464379 C137.583995,11.0849896 118.072967,11.0849896 98.7430163,14.0464379 C96.9108417,9.64549908 94.1925838,4.11318106 91.8971895,0 C73.3526068,3.2084988 55.6133949,8.86399117 39.0420583,16.6376612 C5.61752293,67.146514 -3.4433191,116.400813 1.08711069,164.955721 C23.2560196,181.510915 44.7403634,191.567697 65.8621325,198.148576 C71.0772151,190.971126 75.7283628,183.341335 79.7352139,175.300261 C72.104019,172.400575 64.7949724,168.822202 57.8887866,164.667963 C59.7209612,163.310589 61.5131304,161.891452 63.2445898,160.431257 C105.36741,180.133187 151.134928,180.133187 192.754523,160.431257 C194.506336,161.891452 196.298154,163.310589 198.110326,164.667963 C191.183787,168.842556 183.854737,172.420929 176.223542,175.320965 C180.230393,183.341335 184.861538,190.991831 190.096624,198.16893 C211.238746,191.588051 232.743023,181.531619 254.911949,164.955721 C260.227747,108.668201 245.831087,59.8662432 216.856339,16.5966031 Z M85.4738752,135.09489 C72.8290281,135.09489 62.4592217,123.290155 62.4592217,108.914901 C62.4592217,94.5396472 72.607595,82.7145587 85.4738752,82.7145587 C98.3405064,82.7145587 108.709962,94.5189427 108.488529,108.914901 C108.508531,123.290155 98.3405064,135.09489 85.4738752,135.09489 Z M170.525237,135.09489 C157.88039,135.09489 147.510584,123.290155 147.510584,108.914901 C147.510584,94.5396472 157.658606,82.7145587 170.525237,82.7145587 C183.391518,82.7145587 193.761324,94.5189427 193.539891,108.914901 C193.539891,123.290155 183.391518,135.09489 170.525237,135.09489 Z'
          fill='#5865F2'
          fillRule='nonzero'
        />
      </g>
    </svg>
  )
}

export function CrunchbaseIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      fill='currentColor'
      width='24'
      height='24'
      viewBox='0 0 24 24'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path d='M21.6 0H2.4A2.41 2.41 0 0 0 0 2.4v19.2A2.41 2.41 0 0 0 2.4 24h19.2a2.41 2.41 0 0 0 2.4-2.4V2.4A2.41 2.41 0 0 0 21.6 0zM7.045 14.465A2.11 2.11 0 0 0 9.84 13.42h1.66a3.69 3.69 0 1 1 0-1.75H9.84a2.11 2.11 0 1 0-2.795 2.795zm11.345.845a3.55 3.55 0 0 1-1.06.63 3.68 3.68 0 0 1-3.39-.38v.38h-1.51V5.37h1.5v4.11a3.74 3.74 0 0 1 1.8-.63H16a3.67 3.67 0 0 1 2.39 6.46zm-.223-2.766a2.104 2.104 0 1 1-4.207 0 2.104 2.104 0 0 1 4.207 0z' />
    </svg>
  )
}

export function InputIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='26'
      height='21'
      viewBox='0 0 26 21'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M2.6 20.8C1.885 20.8 1.27314 20.5456 0.764404 20.0369C0.255671 19.5282 0.000871149 18.9159 4.48275e-06 18.2V15.6C4.48275e-06 15.2317 0.124805 14.9231 0.374405 14.6744C0.624004 14.4257 0.932538 14.3009 1.3 14.3C1.66747 14.2991 1.97644 14.4239 2.2269 14.6744C2.47737 14.9249 2.60174 15.2334 2.6 15.6V18.2H23.4V2.6H2.6V5.2C2.6 5.56833 2.4752 5.8773 2.2256 6.1269C1.976 6.3765 1.66747 6.50087 1.3 6.5C0.932538 6.49913 0.624004 6.37433 0.374405 6.1256C0.124805 5.87687 4.48275e-06 5.56833 4.48275e-06 5.2V2.6C4.48275e-06 1.885 0.254805 1.27313 0.764404 0.7644C1.274 0.255666 1.88587 0.000866666 2.6 0H23.4C24.115 0 24.7273 0.2548 25.2369 0.7644C25.7465 1.274 26.0009 1.88587 26 2.6V18.2C26 18.915 25.7456 19.5273 25.2369 20.0369C24.7282 20.5465 24.1159 20.8009 23.4 20.8H2.6ZM13.2275 11.7H1.3C0.931671 11.7 0.623138 11.5752 0.374405 11.3256C0.125671 11.076 0.000871149 10.7675 4.48275e-06 10.4C-0.000862184 10.0325 0.123938 9.724 0.374405 9.4744C0.624871 9.2248 0.933404 9.1 1.3 9.1H13.2275L11.44 7.345C11.18 7.10666 11.0552 6.80897 11.0656 6.4519C11.076 6.09483 11.2008 5.78587 11.44 5.525C11.7 5.265 12.009 5.1298 12.3669 5.1194C12.7248 5.109 13.0334 5.23337 13.2925 5.4925L17.29 9.49C17.55 9.75 17.68 10.0533 17.68 10.4C17.68 10.7467 17.55 11.05 17.29 11.31L13.2925 15.3075C13.0325 15.5675 12.724 15.6975 12.3669 15.6975C12.0098 15.6975 11.7009 15.5675 11.44 15.3075C11.2017 15.0475 11.0769 14.739 11.0656 14.3819C11.0543 14.0248 11.1791 13.7159 11.44 13.455L13.2275 11.7Z'
        fill='currentColor'
      />
    </svg>
  )
}

export function StartIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='26'
      height='16'
      viewBox='0 0 26 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M7.8 13C9.23 13 10.4542 12.4908 11.4725 11.4725C12.4908 10.4542 13 9.23 13 7.8C13 6.37 12.4908 5.14583 11.4725 4.1275C10.4542 3.10917 9.23 2.6 7.8 2.6C6.37 2.6 5.14583 3.10917 4.1275 4.1275C3.10917 5.14583 2.6 6.37 2.6 7.8C2.6 9.23 3.10917 10.4542 4.1275 11.4725C5.14583 12.4908 6.37 13 7.8 13ZM7.8 15.6C5.63333 15.6 3.79167 14.8417 2.275 13.325C0.758333 11.8083 0 9.96666 0 7.8C0 5.63333 0.758333 3.79167 2.275 2.275C3.79167 0.758333 5.63333 0 7.8 0C9.75 0 11.4456 0.6175 12.8869 1.8525C14.3282 3.0875 15.2 4.63667 15.5025 6.5H24.7C25.0683 6.5 25.3773 6.6248 25.6269 6.8744C25.8765 7.124 26.0009 7.43253 26 7.8C25.9991 8.16746 25.8743 8.47643 25.6256 8.7269C25.3769 8.97737 25.0683 9.10173 24.7 9.1H15.5025C15.1992 10.9633 14.3269 12.5125 12.8856 13.7475C11.4443 14.9825 9.74913 15.6 7.8 15.6Z'
        fill='currentColor'
      />
    </svg>
  )
}

export function PineconeIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='256px'
      height='288px'
      viewBox='0 0 256 288'
      version='1.1'
      xmlns='http://www.w3.org/2000/svg'
      xmlnsXlink='http://www.w3.org/1999/xlink'
      preserveAspectRatio='xMidYMid'
    >
      <path
        d='M108.633615,254.43629 C117.713862,254.43629 125.074857,261.797284 125.074857,270.877532 C125.074857,279.957779 117.713862,287.318774 108.633615,287.318774 C99.5533677,287.318774 92.1923728,279.957779 92.1923728,270.877532 C92.1923728,261.797284 99.5533677,254.43629 108.633615,254.43629 Z M199.849665,224.438339 L216.09705,229.252379 L203.199913,272.780219 C202.072982,276.58361 198.458049,279.095992 194.500389,278.826397 L190.516677,278.552973 L190.419263,278.633409 L149.02918,275.728903 L150.180842,258.822508 L177.989056,260.709686 L159.783784,234.447622 L173.709616,224.792379 L191.938895,251.08702 L199.849665,224.438339 Z M23.0126771,194.347476 L39.9158866,195.544979 L37.935897,223.348728 L64.1501315,205.120082 L73.8271476,219.030793 L47.578736,237.278394 L74.3707554,245.173037 L69.5818063,261.427835 L25.8485266,248.543243 C22.0304448,247.418369 19.5101155,243.787479 19.7913963,239.817092 L23.0126771,194.347476 Z M132.151306,170.671396 L162.658679,207.503468 L148.909247,218.891886 L130.753266,196.972134 L124.866941,230.673893 L107.280249,227.599613 L113.172232,193.845272 L88.7296311,208.256891 L79.6674587,192.874434 L120.745504,168.674377 C124.522104,166.449492 129.355297,167.295726 132.151306,170.671396 Z M217.504528,145.960198 L232.744017,137.668804 L254.94482,178.473633 C256.889641,182.048192 256.088221,186.494171 253.017682,189.164674 L249.876622,191.878375 L217.826246,219.77131 L206.441034,206.680621 L227.988588,187.934494 L195.893546,182.152609 L198.972402,165.078949 L231.044844,170.857793 L217.504528,145.960198 Z M37.7821805,103.299272 L49.2622123,116.306888 L28.0106317,135.050179 L60.1668233,140.664193 L57.1863573,157.755303 L24.9947229,152.136967 L38.822104,177.134576 L23.6411026,185.532577 L1.08439616,144.756992 C-0.885025494,141.196884 -0.115545265,136.746375 2.93488097,134.054184 L37.7821805,103.299272 Z M146.476311,89.8796828 L176.88045,126.612847 L163.1271,137.996532 L144.975445,116.067101 L139.08912,149.778947 L121.502428,146.704666 L127.374238,113.081452 L103.025237,127.354817 L93.9976317,111.952048 L131.398812,90.0233663 L131.435631,89.880899 L131.600545,89.9023265 L135.085833,87.870141 C138.861877,85.6569913 143.68556,86.5079996 146.476311,89.8796828 Z M185.655786,71.8143168 L192.305535,55.7902703 L235.318239,73.6399229 C239.072486,75.1978811 241.2415,79.1537636 240.536356,83.1568091 L239.820231,87.1385839 L232.47517,128.919545 L215.389188,125.909819 L220.312646,97.9413879 L191.776157,113.7129 L183.390302,98.5251862 L211.981072,82.7408038 L185.655786,71.8143168 Z M103.71696,40.2373824 L104.456513,57.5706533 L76.0432671,58.785006 L97.4730368,83.2749086 L84.4165529,94.6993319 L62.9507932,70.1728358 L57.949673,98.1737132 L40.8716575,95.1191088 L49.0561498,49.3603563 C49.771444,45.3612115 53.1664633,42.3942036 57.2253811,42.2210231 L61.246149,42.0411642 L61.3363168,41.9758 L103.71696,40.2373824 Z M161.838155,3.27194826 L192.104824,40.2369789 L178.291207,51.5474574 L160.327329,29.6043227 L154.268381,63.2715157 L136.697231,60.1096121 L142.766468,26.3665075 L118.24002,40.7062765 L109.232678,25.2916494 L150.427675,1.21987397 C154.218286,-0.995121237 159.056796,-0.124957814 161.838155,3.27194826 Z'
        fill='currentColor'
      />
    </svg>
  )
}

export function OpenAIIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='800px'
      height='800px'
      viewBox='0 0 24 24'
      role='img'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M22.2819 9.8211a5.9847 5.9847 0 0 0-.5157-4.9108 6.0462 6.0462 0 0 0-6.5098-2.9A6.0651 6.0651 0 0 0 4.9807 4.1818a5.9847 5.9847 0 0 0-3.9977 2.9 6.0462 6.0462 0 0 0 .7427 7.0966 5.98 5.98 0 0 0 .511 4.9107 6.051 6.051 0 0 0 6.5146 2.9001A5.9847 5.9847 0 0 0 13.2599 24a6.0557 6.0557 0 0 0 5.7718-4.2058 5.9894 5.9894 0 0 0 3.9977-2.9001 6.0557 6.0557 0 0 0-.7475-7.0729zm-9.022 12.6081a4.4755 4.4755 0 0 1-2.8764-1.0408l.1419-.0804 4.7783-2.7582a.7948.7948 0 0 0 .3927-.6813v-6.7369l2.02 1.1686a.071.071 0 0 1 .038.052v5.5826a4.504 4.504 0 0 1-4.4945 4.4944zm-9.6607-4.1254a4.4708 4.4708 0 0 1-.5346-3.0137l.142.0852 4.783 2.7582a.7712.7712 0 0 0 .7806 0l5.8428-3.3685v2.3324a.0804.0804 0 0 1-.0332.0615L9.74 19.9502a4.4992 4.4992 0 0 1-6.1408-1.6464zM2.3408 7.8956a4.485 4.485 0 0 1 2.3655-1.9728V11.6a.7664.7664 0 0 0 .3879.6765l5.8144 3.3543-2.0201 1.1685a.0757.0757 0 0 1-.071 0l-4.8303-2.7865A4.504 4.504 0 0 1 2.3408 7.872zm16.5963 3.8558L13.1038 8.364 15.1192 7.2a.0757.0757 0 0 1 .071 0l4.8303 2.7913a4.4944 4.4944 0 0 1-.6765 8.1042v-5.6772a.79.79 0 0 0-.407-.667zm2.0107-3.0231l-.142-.0852-4.7735-2.7818a.7759.7759 0 0 0-.7854 0L9.409 9.2297V6.8974a.0662.0662 0 0 1 .0284-.0615l4.8303-2.7866a4.4992 4.4992 0 0 1 6.6802 4.66zM8.3065 12.863l-2.02-1.1638a.0804.0804 0 0 1-.038-.0567V6.0742a4.4992 4.4992 0 0 1 7.3757-3.4537l-.142.0805L8.704 5.459a.7948.7948 0 0 0-.3927.6813zm1.0976-2.3654l2.602-1.4998 2.6069 1.4998v2.9994l-2.5974 1.4997-2.6067-1.4997Z'
        fill='currentColor'
      />
    </svg>
  )
}

export function ExaAIIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='252'
      height='304'
      viewBox='0 0 252 304'
      fill='none'
      role='img'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M4.82461 0.75046C5.94359 0.750786 5.94359 0.750786 7.08518 0.751118C7.94546 0.74652 8.80574 0.741921 9.69209 0.737183C10.6428 0.742326 11.5934 0.74747 12.5729 0.752769C13.5785 0.750355 14.584 0.74794 15.62 0.745453C19.0112 0.739788 22.4021 0.7485 25.7932 0.757087C28.2155 0.756015 30.6378 0.754077 33.0601 0.751335C38.9691 0.746979 44.878 0.751943 50.787 0.761185C57.6619 0.771553 64.5368 0.771499 71.4118 0.771013C83.674 0.770512 95.9362 0.779683 108.198 0.794197C120.11 0.808288 132.021 0.815367 143.932 0.814705C144.666 0.814665 145.399 0.814625 146.155 0.814584C146.888 0.814543 147.62 0.814503 148.375 0.814461C161.918 0.813772 175.461 0.819657 189.005 0.828614C193.802 0.831636 198.6 0.832695 203.397 0.833147C209.848 0.833898 216.298 0.840223 222.749 0.850313C225.124 0.853246 227.498 0.854462 229.873 0.853885C233.1 0.853398 236.327 0.859013 239.554 0.866295C240.507 0.864746 241.46 0.863197 242.442 0.861602C248.886 0.885829 248.886 0.885829 250 2.00001C252.1 17.5148 252.1 17.5148 247.828 23.7344C245.548 26.539 243.019 29.0818 240.419 31.5889C238.376 33.6204 236.637 35.8493 234.875 38.125C231.086 42.8997 227.239 47.6241 223.369 52.3335C222.383 53.5339 221.397 54.7348 220.412 55.936C214.429 63.2301 208.439 70.5132 202.313 77.6875C198.113 82.6097 194.1 87.6563 190.129 92.7637C186.095 97.9301 181.879 102.925 177.625 107.91C173.674 112.544 169.843 117.276 166 122C164.501 123.834 163.001 125.667 161.5 127.5C159.202 130.308 156.905 133.116 154.609 135.926C153.831 136.878 153.052 137.831 152.25 138.812C151.515 139.719 150.78 140.625 150.023 141.559C148.814 143.018 147.577 144.457 146.289 145.848C143.623 148.63 143.623 148.63 143.078 152.293C144.286 155.841 146.405 158.206 148.875 160.938C149.881 162.085 150.885 163.235 151.887 164.387C152.661 165.275 152.661 165.275 153.451 166.181C155.961 169.128 158.321 172.187 160.688 175.25C164.273 179.87 167.986 184.285 171.922 188.613C174.481 191.552 176.863 194.608 179.25 197.688C183.104 202.657 187.072 207.495 191.188 212.25C195.142 216.819 198.998 221.438 202.746 226.18C206.376 230.722 210.089 235.197 213.79 239.682C217.101 243.695 220.403 247.715 223.688 251.75C226.934 255.727 230.237 259.651 233.563 263.562C238.148 268.958 242.642 274.418 247 280C247.845 280.841 247.845 280.841 248.707 281.699C250.778 285.385 250.314 289.106 250.188 293.25C250.174 294.09 250.16 294.931 250.146 295.797C250.111 297.865 250.057 299.933 250 302C247.719 303.141 246.468 303.126 243.933 303.129C243.077 303.132 242.221 303.135 241.339 303.138C240.393 303.137 239.447 303.135 238.472 303.134C237.471 303.136 236.47 303.138 235.439 303.141C232.064 303.147 228.689 303.146 225.314 303.145C222.904 303.148 220.493 303.152 218.082 303.155C212.201 303.163 206.32 303.166 200.438 303.167C195.66 303.168 190.882 303.17 186.104 303.173C172.568 303.182 159.032 303.186 145.496 303.185C144.766 303.185 144.036 303.185 143.284 303.185C142.553 303.185 141.822 303.185 141.068 303.185C129.214 303.185 117.359 303.194 105.504 303.208C93.3424 303.223 81.1812 303.23 69.0199 303.229C62.1877 303.229 55.3555 303.231 48.5233 303.242C42.1015 303.252 35.6798 303.252 29.258 303.245C26.8948 303.243 24.5316 303.246 22.1685 303.252C18.955 303.26 15.7417 303.255 12.5282 303.247C11.5821 303.252 10.636 303.258 9.66125 303.263C8.80507 303.258 7.94889 303.254 7.06677 303.249C6.32436 303.249 5.58195 303.249 4.81705 303.25C3 303 3 303 1 301C0.749304 298.843 0.749304 298.843 0.74832 296.106C0.743105 295.065 0.737889 294.024 0.732515 292.952C0.736977 291.799 0.741439 290.647 0.746035 289.46C0.742915 288.243 0.739795 287.025 0.73658 285.771C0.729961 282.382 0.732374 278.993 0.738553 275.603C0.743062 271.952 0.736102 268.301 0.730593 264.65C0.721681 257.494 0.723629 250.337 0.729418 243.18C0.733928 237.366 0.734551 231.552 0.732371 225.738C0.732064 224.912 0.731758 224.085 0.731442 223.234C0.730803 221.556 0.730154 219.878 0.729497 218.2C0.723788 202.448 0.73036 186.696 0.741098 170.944C0.750034 157.416 0.748482 143.888 0.73926 130.36C0.728567 114.666 0.724337 98.9726 0.730485 83.2787C0.731122 81.6065 0.731751 79.9343 0.732371 78.2621C0.732834 77.0279 0.732834 77.0279 0.733306 75.7687C0.735036 69.9587 0.732112 64.1487 0.727412 58.3387C0.721814 51.2623 0.723297 44.1858 0.733973 37.1094C0.739244 33.496 0.741262 29.8827 0.734675 26.2694C0.727655 22.3596 0.736014 18.45 0.746035 14.5403C0.741574 13.388 0.737112 12.2357 0.732515 11.0484C0.737731 10.0076 0.742947 8.96683 0.74832 7.89449C0.748645 6.99108 0.748969 6.08767 0.749304 5.15689C1.07777 2.33093 1.9319 1.14609 4.82461 0.75046ZM39 25C40.5046 28.0092 41.6553 29.9923 43.7227 32.5039C44.2563 33.1562 44.79 33.8084 45.3398 34.4805C45.9083 35.1675 46.4768 35.8546 47.0625 36.5625C47.6503 37.2792 48.2381 37.9959 48.8438 38.7344C50.5594 40.8255 52.2795 42.9128 54 45C55.5008 46.8327 57.0009 48.6659 58.5 50.5C59.2038 51.3598 59.9077 52.2196 60.6328 53.1055C74.8482 70.4877 74.8482 70.4877 81.4375 78.9375C86.6547 85.6225 92.0434 92.1594 97.4453 98.6953C106.496 109.647 115.413 120.68 124 132C126.944 131.669 127.951 131.051 130.043 128.883C130.751 127.973 131.458 127.063 132.188 126.125C132.978 125.125 133.768 124.124 134.582 123.094C135.38 122.073 136.178 121.052 137 120C138.453 118.204 139.911 116.412 141.375 114.625C142.114 113.721 142.852 112.818 143.613 111.887C146.237 108.714 148.896 105.574 151.563 102.438C155.359 97.9705 159.069 93.4497 162.711 88.8555C167.531 82.8433 172.464 76.9234 177.368 70.98C181.651 65.7834 185.911 60.5683 190.16 55.3438C193.828 50.8413 197.525 46.3703 201.296 41.9539C209.025 34.2599 209.025 34.2599 213 25C155.58 25 98.16 25 39 25ZM25 48C25 78.36 25 108.72 25 140C49.42 140 73.84 140 99 140C97.1724 135.431 95.234 132.634 92.0625 129.062C88.1461 124.547 84.3496 119.974 80.6875 115.25C75.8962 109.087 70.9584 103.054 65.9834 97.0383C62.3144 92.6007 58.6856 88.1356 55.1016 83.6289C51.8102 79.5116 48.4818 75.4244 45.1528 71.3374C42.2047 67.7174 39.266 64.0903 36.3438 60.4492C35.7523 59.7146 35.1609 58.98 34.5515 58.2231C33.4108 56.8059 32.2728 55.3863 31.1379 53.9644C28.4954 50.4896 28.4954 50.4896 25 48ZM25 164C25 194.36 25 224.72 25 256C29.0465 252.763 31.8897 249.854 34.9375 245.812C39.1544 240.334 43.4654 234.95 47.875 229.625C49.0665 228.184 50.2579 226.742 51.4492 225.301C52.0388 224.587 52.6284 223.874 53.2358 223.139C60.5987 214.211 67.8623 205.212 74.9729 196.082C78.0953 192.094 81.3286 188.219 84.625 184.375C93.89 174.945 93.89 174.945 99 164C74.58 164 50.16 164 25 164ZM124 172C122.296 173.677 122.296 173.677 120.625 176C119.922 176.913 119.22 177.825 118.496 178.766C118.103 179.279 117.709 179.793 117.304 180.322C114.824 183.512 112.246 186.623 109.688 189.75C105.526 194.853 101.42 199.992 97.375 205.188C93.4114 210.274 89.3101 215.216 85.1179 220.115C81.4019 224.477 77.8597 228.957 74.3425 233.479C69.9929 239.054 65.4983 244.503 60.9922 249.951C57.2454 254.482 53.5376 259.039 49.8867 263.648C46.879 267.397 43.7807 271.072 40.6875 274.75C38.8 276.798 38.8 276.798 39 279C96.42 279 153.84 279 213 279C211.09 274.226 208.908 271.29 205.563 267.562C201.392 262.814 197.362 258.003 193.5 253C189.795 248.214 185.968 243.57 182 239C177.546 233.871 173.304 228.62 169.145 223.25C165.88 219.06 162.482 215.01 159 211C154.552 205.878 150.315 200.635 146.162 195.272C142.252 190.252 138.147 185.417 133.971 180.617C131.987 178.334 130.064 176.093 128.344 173.602C127.9 173.073 127.457 172.545 127 172C126.01 172 125.02 172 124 172Z'
        fill='currentColor'
      />
    </svg>
  )
}

export function RedditIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='100px'
      height='100px'
      viewBox='0 0 50 50'
      fill='#FFFFFF'
      role='img'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M 29 3 C 26.894531 3 24.433594 4.652344 24.0625 12.03125 C 24.375 12.023438 24.683594 12 25 12 C 25.351563 12 25.714844 12.019531 26.0625 12.03125 C 26.300781 7.597656 27.355469 5 29 5 C 29.703125 5 30.101563 5.382813 30.84375 6.1875 C 31.710938 7.128906 32.84375 8.351563 35.0625 8.8125 C 35.027344 8.550781 35 8.269531 35 8 C 35 7.578125 35.042969 7.179688 35.125 6.78125 C 33.75 6.40625 33.023438 5.613281 32.3125 4.84375 C 31.519531 3.984375 30.609375 3 29 3 Z M 41 4 C 38.792969 4 37 5.796875 37 8 C 37 10.203125 38.792969 12 41 12 C 43.207031 12 45 10.203125 45 8 C 45 5.796875 43.207031 4 41 4 Z M 25 14 C 12.867188 14 3 20.179688 3 29 C 3 37.820313 12.867188 45 25 45 C 37.132813 45 47 37.820313 47 29 C 47 20.179688 37.132813 14 25 14 Z M 7.5 14.9375 C 6.039063 14.9375 4.652344 15.535156 3.59375 16.59375 C 1.871094 18.316406 1.515625 20.792969 2.5 22.84375 C 4.011719 19.917969 6.613281 17.421875 9.96875 15.5625 C 9.207031 15.175781 8.363281 14.9375 7.5 14.9375 Z M 42.5 14.9375 C 41.636719 14.9375 40.792969 15.175781 40.03125 15.5625 C 43.386719 17.421875 45.988281 19.917969 47.5 22.84375 C 48.484375 20.792969 48.128906 18.316406 46.40625 16.59375 C 45.347656 15.535156 43.960938 14.9375 42.5 14.9375 Z M 17 23 C 18.65625 23 20 24.34375 20 26 C 20 27.65625 18.65625 29 17 29 C 15.34375 29 14 27.65625 14 26 C 14 24.34375 15.34375 23 17 23 Z M 33 23 C 34.65625 23 36 24.34375 36 26 C 36 27.65625 34.65625 29 33 29 C 31.34375 29 30 27.65625 30 26 C 30 24.34375 31.34375 23 33 23 Z M 16.0625 34 C 16.3125 34.042969 16.558594 34.183594 16.71875 34.40625 C 16.824219 34.554688 19.167969 37.6875 25 37.6875 C 30.910156 37.6875 33.257813 34.46875 33.28125 34.4375 C 33.597656 33.988281 34.234375 33.867188 34.6875 34.1875 C 35.136719 34.503906 35.222656 35.109375 34.90625 35.5625 C 34.789063 35.730469 31.9375 39.6875 25 39.6875 C 18.058594 39.6875 15.210938 35.730469 15.09375 35.5625 C 14.777344 35.109375 14.859375 34.503906 15.3125 34.1875 C 15.539063 34.027344 15.8125 33.957031 16.0625 34 Z'
        fill='currentColor'
      />
    </svg>
  )
}

export function AirtableIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='800px'
      height='800px'
      viewBox='0 -20.5 256 256'
      version='1.1'
      xmlns='http://www.w3.org/2000/svg'
      xmlnsXlink='http://www.w3.org/1999/xlink'
      preserveAspectRatio='xMidYMid'
    >
      <g>
        <path
          d='M114.25873,2.70101695 L18.8604023,42.1756384 C13.5552723,44.3711638 13.6102328,51.9065311 18.9486282,54.0225085 L114.746142,92.0117514 C123.163769,95.3498757 132.537419,95.3498757 140.9536,92.0117514 L236.75256,54.0225085 C242.08951,51.9065311 242.145916,44.3711638 236.83934,42.1756384 L141.442459,2.70101695 C132.738459,-0.900338983 122.961284,-0.900338983 114.25873,2.70101695'
          fill='#FFBF00'
        />
        <path
          d='M136.349071,112.756863 L136.349071,207.659101 C136.349071,212.173089 140.900664,215.263892 145.096461,213.600615 L251.844122,172.166219 C254.281184,171.200072 255.879376,168.845451 255.879376,166.224705 L255.879376,71.3224678 C255.879376,66.8084791 251.327783,63.7176768 247.131986,65.3809537 L140.384325,106.815349 C137.94871,107.781496 136.349071,110.136118 136.349071,112.756863'
          fill='#26B5F8'
        />
        <path
          d='M111.422771,117.65355 L79.742409,132.949912 L76.5257763,134.504714 L9.65047684,166.548104 C5.4112904,168.593211 0.000578531073,165.503855 0.000578531073,160.794612 L0.000578531073,71.7210757 C0.000578531073,70.0173017 0.874160452,68.5463864 2.04568588,67.4384994 C2.53454463,66.9481944 3.08848814,66.5446689 3.66412655,66.2250305 C5.26231864,65.2661153 7.54173107,65.0101153 9.47981017,65.7766689 L110.890522,105.957098 C116.045234,108.002206 116.450206,115.225166 111.422771,117.65355'
          fill='#ED3049'
        />
        <path
          d='M111.422771,117.65355 L79.742409,132.949912 L2.04568588,67.4384994 C2.53454463,66.9481944 3.08848814,66.5446689 3.66412655,66.2250305 C5.26231864,65.2661153 7.54173107,65.0101153 9.47981017,65.7766689 L110.890522,105.957098 C116.045234,108.002206 116.450206,115.225166 111.422771,117.65355'
          fillOpacity='0.25'
          fill='#000000'
        />
      </g>
    </svg>
  )
}

export function GoogleDocsIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns='http://www.w3.org/2000/svg'
      viewBox='0 0 48 48'
      width='96px'
      height='96px'
    >
      <path
        fill='#2196f3'
        d='M37,45H11c-1.657,0-3-1.343-3-3V6c0-1.657,1.343-3,3-3h19l10,10v29C40,43.657,38.657,45,37,45z'
      />
      <path fill='#bbdefb' d='M40 13L30 13 30 3z' />
      <path fill='#1565c0' d='M30 13L40 23 40 13z' />
      <path fill='#e3f2fd' d='M15 23H33V25H15zM15 27H33V29H15zM15 31H33V33H15zM15 35H25V37H15z' />
    </svg>
  )
}

export function GoogleCalendarIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      version='1.1'
      xmlns='http://www.w3.org/2000/svg'
      xmlnsXlink='http://www.w3.org/1999/xlink'
      x='0px'
      y='0px'
      viewBox='0 0 200 200'
      enableBackground='new 0 0 200 200'
      xmlSpace='preserve'
    >
      <g>
        <g transform='translate(3.75 3.75)'>
          <path
            fill='#FFFFFF'
            d='M148.882,43.618l-47.368-5.263l-57.895,5.263L38.355,96.25l5.263,52.632l52.632,6.579l52.632-6.579
			l5.263-53.947L148.882,43.618z'
          />
          <path
            fill='#1A73E8'
            d='M65.211,125.276c-3.934-2.658-6.658-6.539-8.145-11.671l9.132-3.763c0.829,3.158,2.276,5.605,4.342,7.342
			c2.053,1.737,4.553,2.592,7.474,2.592c2.987,0,5.553-0.908,7.697-2.724s3.224-4.132,3.224-6.934c0-2.868-1.132-5.211-3.395-7.026
			s-5.105-2.724-8.5-2.724h-5.276v-9.039H76.5c2.921,0,5.382-0.789,7.382-2.368c2-1.579,3-3.737,3-6.487
			c0-2.447-0.895-4.395-2.684-5.855s-4.053-2.197-6.803-2.197c-2.684,0-4.816,0.711-6.395,2.145s-2.724,3.197-3.447,5.276
			l-9.039-3.763c1.197-3.395,3.395-6.395,6.618-8.987c3.224-2.592,7.342-3.895,12.342-3.895c3.697,0,7.026,0.711,9.974,2.145
			c2.947,1.434,5.263,3.421,6.934,5.947c1.671,2.539,2.5,5.382,2.5,8.539c0,3.224-0.776,5.947-2.329,8.184
			c-1.553,2.237-3.461,3.947-5.724,5.145v0.539c2.987,1.25,5.421,3.158,7.342,5.724c1.908,2.566,2.868,5.632,2.868,9.211
			s-0.908,6.776-2.724,9.579c-1.816,2.803-4.329,5.013-7.513,6.618c-3.197,1.605-6.789,2.421-10.776,2.421
			C73.408,129.263,69.145,127.934,65.211,125.276z'
          />
          <path
            fill='#1A73E8'
            d='M121.25,79.961l-9.974,7.25l-5.013-7.605l17.987-12.974h6.895v61.197h-9.895L121.25,79.961z'
          />
          <path
            fill='#EA4335'
            d='M148.882,196.25l47.368-47.368l-23.684-10.526l-23.684,10.526l-10.526,23.684L148.882,196.25z'
          />
          <path
            fill='#34A853'
            d='M33.092,172.566l10.526,23.684h105.263v-47.368H43.618L33.092,172.566z'
          />
          <path
            fill='#4285F4'
            d='M12.039-3.75C3.316-3.75-3.75,3.316-3.75,12.039v136.842l23.684,10.526l23.684-10.526V43.618h105.263
			l10.526-23.684L148.882-3.75H12.039z'
          />
          <path
            fill='#188038'
            d='M-3.75,148.882v31.579c0,8.724,7.066,15.789,15.789,15.789h31.579v-47.368H-3.75z'
          />
          <path
            fill='#FBBC04'
            d='M148.882,43.618v105.263h47.368V43.618l-23.684-10.526L148.882,43.618z'
          />
          <path
            fill='#1967D2'
            d='M196.25,43.618V12.039c0-8.724-7.066-15.789-15.789-15.789h-31.579v47.368H196.25z'
          />
        </g>
      </g>
    </svg>
  )
}

export function SupabaseIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg {...props} width='24' height='24' viewBox='0 0 27 27' xmlns='http://www.w3.org/2000/svg'>
      <path
        d='M15.4057 26.2606C14.7241 27.1195 13.3394 26.649 13.3242 25.5519L13.083 9.50684H23.8724C25.8262 9.50684 26.9157 11.7636 25.7006 13.2933L15.4057 26.2606Z'
        fill='url(#paint0_linear)'
      />
      <path
        d='M15.4057 26.2606C14.7241 27.1195 13.3394 26.649 13.3242 25.5519L13.083 9.50684H23.8724C25.8262 9.50684 26.9157 11.7636 25.7006 13.2933L15.4057 26.2606Z'
        fill='url(#paint1_linear)'
        fillOpacity='0.2'
      />
      <path
        d='M11.0167 0.443853C11.6983 -0.415083 13.0832 0.0553814 13.0982 1.15237L13.2042 17.1976H2.55005C0.596215 17.1976 -0.493259 14.9408 0.721603 13.4111L11.0167 0.443853Z'
        fill='#3ECF8E'
      />
      <defs>
        <linearGradient
          id='paint0_linear'
          x1='13.084'
          y1='13.0655'
          x2='22.6727'
          y2='17.087'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#249361' />
          <stop offset='1' stopColor='#3ECF8E' />
        </linearGradient>
        <linearGradient
          id='paint1_linear'
          x1='8.83277'
          y1='7.24485'
          x2='13.2057'
          y2='15.477'
          gradientUnits='userSpaceOnUse'
        >
          <stop />
          <stop offset='1' stopOpacity='0' />
        </linearGradient>
      </defs>
    </svg>
  )
}

export function WhatsAppIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns='http://www.w3.org/2000/svg'
      width='16'
      height='16'
      fill='currentColor'
      viewBox='0 0 16 16'
    >
      <path d='M13.601 2.326A7.85 7.85 0 0 0 7.994 0C3.627 0 .068 3.558.064 7.926c0 1.399.366 2.76 1.057 3.965L0 16l4.204-1.102a7.9 7.9 0 0 0 3.79.965h.004c4.368 0 7.926-3.558 7.93-7.93A7.9 7.9 0 0 0 13.6 2.326zM7.994 14.521a6.6 6.6 0 0 1-3.356-.92l-.24-.144-2.494.654.666-2.433-.156-.251a6.56 6.56 0 0 1-1.007-3.505c0-3.626 2.957-6.584 6.591-6.584a6.56 6.56 0 0 1 4.66 1.931 6.56 6.56 0 0 1 1.928 4.66c-.004 3.639-2.961 6.592-6.592 6.592m3.615-4.934c-.197-.099-1.17-.578-1.353-.646-.182-.065-.315-.099-.445.099-.133.197-.513.646-.627.775-.114.133-.232.148-.43.05-.197-.1-.836-.308-1.592-.985-.59-.525-.985-1.175-1.103-1.372-.114-.198-.011-.304.088-.403.087-.088.197-.232.296-.346.1-.114.133-.198.198-.33.065-.134.034-.248-.015-.347-.05-.099-.445-1.076-.612-1.47-.16-.389-.323-.335-.445-.34-.114-.007-.247-.007-.38-.007a.73.73 0 0 0-.529.247c-.182.198-.691.677-.691 1.654s.71 1.916.81 2.049c.098.133 1.394 2.132 3.383 2.992.47.205.84.326 1.129.418.475.152.904.129 ***********-.058 1.171-.48 1.338-.943.164-.464.164-.86.114-.943-.049-.084-.182-.133-.38-.232' />
    </svg>
  )
}

export function StripeIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='2500'
      height='1045'
      viewBox='0 0 512 214'
      xmlns='http://www.w3.org/2000/svg'
      preserveAspectRatio='xMidYMid'
    >
      <path
        d='M35.982 83.484c0-5.546 4.551-7.68 12.09-7.68 10.808 0 24.461 3.272 35.27 9.103V51.484c-11.804-4.693-23.466-6.542-35.27-6.542C19.2 44.942 0 60.018 0 85.192c0 39.252 54.044 32.995 54.044 49.92 0 6.541-5.688 8.675-13.653 8.675-11.804 0-26.88-4.836-38.827-11.378v33.849c13.227 5.689 26.596 8.106 38.827 8.106 29.582 0 49.92-14.648 49.92-40.106-.142-42.382-54.329-34.845-54.329-50.774zm96.142-66.986l-34.702 7.395-.142 113.92c0 21.05 15.787 36.551 36.836 36.551 11.662 0 20.195-2.133 24.888-4.693V140.8c-4.55 1.849-27.022 8.391-27.022-12.658V77.653h27.022V47.36h-27.022l.142-30.862zm71.112 41.386L200.96 47.36h-30.72v124.444h35.556V87.467c8.39-10.951 22.613-8.96 27.022-7.396V47.36c-4.551-1.707-21.191-4.836-29.582 10.524zm38.257-10.524h35.698v124.444h-35.698V47.36zm0-10.809l35.698-7.68V0l-35.698 7.538V36.55zm109.938 8.391c-13.938 0-22.898 6.542-27.875 11.094l-1.85-8.818h-31.288v165.83l35.555-7.537.143-40.249c5.12 3.698 12.657 8.96 25.173 8.96 25.458 0 48.64-20.48 48.64-65.564-.142-41.245-23.609-63.716-48.498-63.716zm-8.533 97.991c-8.391 0-13.37-2.986-16.782-6.684l-.143-52.765c3.698-4.124 8.818-6.968 16.925-6.968 12.942 0 21.902 14.506 21.902 33.137 0 19.058-8.818 33.28-21.902 33.28zM512 110.08c0-36.409-17.636-65.138-51.342-65.138-33.85 0-54.33 28.73-54.33 64.854 0 42.808 24.179 64.426 58.88 64.426 16.925 0 29.725-3.84 39.396-9.244v-28.445c-9.67 4.836-20.764 7.823-34.844 7.823-13.796 0-26.027-4.836-27.591-21.618h69.547c0-1.85.284-9.245.284-12.658zm-70.258-13.511c0-16.071 9.814-22.756 18.774-22.756 8.675 0 17.92 6.685 17.92 22.756h-36.694z'
        fill='#6772E5'
      />
    </svg>
  )
}

export function EyeIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      fill='currentColor'
      width='24'
      height='24'
      viewBox='0 0 28 23'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M13.9999 6.51172C12.7047 6.51172 11.4625 7.02625 10.5466 7.94213C9.63074 8.858 9.11621 10.1002 9.11621 11.3954C9.11621 12.6907 9.63074 13.9329 10.5466 14.8488C11.4625 15.7646 12.7047 16.2792 13.9999 16.2792C15.2952 16.2792 16.5374 15.7646 17.4532 14.8488C18.3691 13.9329 18.8837 12.6907 18.8837 11.3954C18.8837 10.1002 18.3691 8.858 17.4532 7.94213C16.5374 7.02625 15.2952 6.51172 13.9999 6.51172ZM11.0697 11.3954C11.0697 10.6183 11.3784 9.87298 11.9279 9.32345C12.4775 8.77393 13.2228 8.46521 13.9999 8.46521C14.7771 8.46521 15.5224 8.77393 16.0719 9.32345C16.6214 9.87298 16.9302 10.6183 16.9302 11.3954C16.9302 12.1726 16.6214 12.9179 16.0719 13.4674C15.5224 14.017 14.7771 14.3257 13.9999 14.3257C13.2228 14.3257 12.4775 14.017 11.9279 13.4674C11.3784 12.9179 11.0697 12.1726 11.0697 11.3954Z'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M14 0C8.1213 0 4.16093 3.52149 1.86233 6.50772L1.82195 6.56112C1.30102 7.23702 0.82307 7.85823 0.498791 8.59274C0.15107 9.38065 0 10.2389 0 11.3953C0 12.5518 0.15107 13.41 0.498791 14.198C0.824372 14.9325 1.30233 15.555 1.82195 16.2296L1.86363 16.283C4.16093 19.2692 8.1213 22.7907 14 22.7907C19.8787 22.7907 23.8391 19.2692 26.1377 16.283L26.178 16.2296C26.699 15.555 27.1769 14.9325 27.5012 14.198C27.8489 13.41 28 12.5518 28 11.3953C28 10.2389 27.8489 9.38065 27.5012 8.59274C27.1756 7.85823 26.6977 7.23702 26.178 6.56112L26.1364 6.50772C23.8391 3.52149 19.8787 0 14 0ZM3.41209 7.69935C5.53228 4.94233 8.98605 1.95349 14 1.95349C19.014 1.95349 22.4664 4.94233 24.5879 7.69935C25.1609 8.44167 25.4943 8.88447 25.7144 9.38195C25.9202 9.84819 26.0465 10.4173 26.0465 11.3953C26.0465 12.3734 25.9202 12.9425 25.7144 13.4087C25.4943 13.9062 25.1596 14.349 24.5892 15.0913C22.4651 17.8484 19.014 20.8372 14 20.8372C8.98605 20.8372 5.53358 17.8484 3.41209 15.0913C2.83907 14.349 2.50567 13.9062 2.28558 13.4087C2.07981 12.9425 1.95349 12.3734 1.95349 11.3953C1.95349 10.4173 2.07981 9.84819 2.28558 9.38195C2.50567 8.88447 2.84167 8.44167 3.41209 7.69935Z'
      />
    </svg>
  )
}

export function ConfluenceIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='24'
      height='24'
      viewBox='0 3 21 24'
      focusable='false'
      fill='none'
      aria-hidden='true'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fill='#1868DB'
        d='M20.602 20.234c-6.584-3.183-8.507-3.66-11.281-3.66-3.255 0-6.03 1.355-8.507 5.16l-.407.622c-.333.513-.407.696-.407.915s.111.403.518.659l4.18 2.598c.221.146.406.22.591.22.222 0 .37-.11.592-.44l.666-1.024c1.035-1.574 1.96-2.086 3.144-2.086 1.035 0 2.256.293 3.772 1.025l4.365 2.049c.444.22.925.11 1.146-.403l2.072-4.537c.222-.512.074-.842-.444-1.098M1.406 12.22c6.583 3.184 8.507 3.66 11.28 3.66 3.256 0 6.03-1.354 8.508-5.16l.407-.622c.332-.512.406-.695.406-.915s-.11-.402-.518-.658L17.31 5.927c-.222-.147-.407-.22-.592-.22-.222 0-.37.11-.592.44l-.665 1.024c-1.036 1.573-1.96 2.086-3.144 2.086-1.036 0-2.257-.293-3.773-1.025L4.18 6.183c-.444-.22-.925-.11-1.147.402L.962 11.123c-.222.512-.074.841.444 1.098'
      />
    </svg>
  )
}

export function TwilioIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg {...props} xmlns='http://www.w3.org/2000/svg' viewBox='0 0 256 256'>
      <path
        fill='currentColor'
        d='M128 0c70.656 0 128 57.344 128 128s-57.344 128-128 128S0 198.656 0 128 57.344 0 128 0zm0 33.792c-52.224 0-94.208 41.984-94.208 94.208S75.776 222.208 128 222.208s94.208-41.984 94.208-94.208S180.224 33.792 128 33.792zm31.744 99.328c14.704 0 26.624 11.92 26.624 26.624 0 14.704-11.92 26.624-26.624 26.624-14.704 0-26.624-11.92-26.624-26.624 0-14.704 11.92-26.624 26.624-26.624zm-63.488 0c14.704 0 26.624 11.92 26.624 26.624 0 14.704-11.92 26.624-26.624 26.624-14.704 0-26.624-11.92-26.624-26.624 0-14.704 11.92-26.624 26.624-26.624zm63.488-63.488c14.704 0 26.624 11.92 26.624 26.624 0 14.704-11.92 26.624-26.624 26.624-14.704 0-26.624-11.92-26.624-26.624 0-14.704 11.92-26.624 26.624-26.624zm-63.488 0c14.704 0 26.624 11.92 26.624 26.624 0 14.704-11.92 26.624-26.624 26.624-14.704 0-26.624-11.92-26.624-26.624 0-14.704 11.92-26.624 26.624-26.624z'
      />
    </svg>
  )
}

export function ImageIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='26'
      height='26'
      viewBox='0 0 26 26'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      stroke='currentColor'
      strokeWidth='2'
      strokeLinecap='round'
      strokeLinejoin='round'
    >
      <path d='M24.903 10.32C16.0897 9.10933 8.48966 15.6533 9.00033 24.3333M5.66699 7.66667C5.66699 8.37391 5.94794 9.05219 6.44804 9.55228C6.94814 10.0524 7.62641 10.3333 8.33366 10.3333C9.0409 10.3333 9.71918 10.0524 10.2193 9.55228C10.7194 9.05219 11.0003 8.37391 11.0003 7.66667C11.0003 6.95942 10.7194 6.28115 10.2193 5.78105C9.71918 5.28095 9.0409 5 8.33366 5C7.62641 5 6.94814 5.28095 6.44804 5.78105C5.94794 6.28115 5.66699 6.95942 5.66699 7.66667Z' />
      <path d='M1 14.4213C4.70667 13.908 8.03333 15.6986 9.832 18.5546' />
      <path d='M1 9.53333C1 6.54667 1 5.05333 1.58133 3.912C2.09265 2.90851 2.90851 2.09265 3.912 1.58133C5.05333 1 6.54667 1 9.53333 1H16.4667C19.4533 1 20.9467 1 22.088 1.58133C23.0915 2.09265 23.9073 2.90851 24.4187 3.912C25 5.05333 25 6.54667 25 9.53333V16.4667C25 19.4533 25 20.9467 24.4187 22.088C23.9073 23.0915 23.0915 23.9073 22.088 24.4187C20.9467 25 19.4533 25 16.4667 25H9.53333C6.54667 25 5.05333 25 3.912 24.4187C2.90851 23.9073 2.09265 23.0915 1.58133 22.088C1 20.9467 1 19.4533 1 16.4667V9.53333Z' />
    </svg>
  )
}

export function TypeformIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      version='1.1'
      id='Layer_1'
      xmlns='http://www.w3.org/2000/svg'
      xmlnsXlink='http://www.w3.org/1999/xlink'
      x='0px'
      y='0px'
      viewBox='0 0 122.3 80.3'
      xmlSpace='preserve'
    >
      <g>
        <path
          fill='currentColor'
          d='M94.3,0H65.4c-26,0-28,11.2-28,26.2l0,27.9c0,15.6,2,26.2,28.1,26.2h28.8c26,0,28-11.2,28-26.1V26.2
		C122.3,11.2,120.3,0,94.3,0z M0,20.1C0,6.9,5.2,0,14,0c8.8,0,14,6.9,14,20.1v40.1c0,13.2-5.2,20.1-14,20.1c-8.8,0-14-6.9-14-20.1
		V20.1z'
        />
      </g>
    </svg>
  )
}

export function DocumentIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='23'
      height='28'
      viewBox='0 0 23 28'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M8 15.2H15.2M8 20H11.6M2 4.4V23.6C2 24.2365 2.25286 24.847 2.70294 25.2971C3.15303 25.7471 3.76348 26 4.4 26H18.8C19.4365 26 20.047 25.7471 20.4971 25.2971C20.9471 24.847 21.2 24.2365 21.2 23.6V9.6104C21.2 9.29067 21.136 8.97417 21.012 8.67949C20.8879 8.38481 20.7062 8.11789 20.4776 7.8944L15.1496 2.684C14.7012 2.24559 14.0991 2.00008 13.472 2H4.4C3.76348 2 3.15303 2.25286 2.70294 2.70294C2.25286 3.15303 2 3.76348 2 4.4Z'
        stroke='currentColor'
        strokeWidth='2.25'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M14 2V6.8C14 7.43652 14.2529 8.04697 14.7029 8.49706C15.153 8.94714 15.7635 9.2 16.4 9.2H21.2'
        stroke='currentColor'
        strokeWidth='2.25'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export function MistralIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='22'
      height='22'
      viewBox='1 0.5 24 22'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      preserveAspectRatio='xMidYMid meet'
    >
      <g clipPath='url(#clip0_1621_58)'>
        <path d='M17.4541 0H21.8177V4.39481H17.4541V0Z' fill='black' />
        <path d='M19.6367 0H24.0003V4.39481H19.6367V0Z' fill='#F7D046' />
        <path
          d='M0 0H4.36359V4.39481H0V0ZM0 4.39481H4.36359V8.78961H0V4.39481ZM0 8.78971H4.36359V13.1845H0V8.78971ZM0 13.1845H4.36359V17.5793H0V13.1845ZM0 17.5794H4.36359V21.9742H0V17.5794Z'
          fill='black'
        />
        <path d='M2.18164 0H6.54523V4.39481H2.18164V0Z' fill='#F7D046' />
        <path
          d='M19.6362 4.39478H23.9998V8.78958H19.6362V4.39478ZM2.18164 4.39478H6.54523V8.78958H2.18164V4.39478Z'
          fill='#F2A73B'
        />
        <path d='M13.0908 4.39478H17.4544V8.78958H13.0908V4.39478Z' fill='black' />
        <path
          d='M15.2732 4.39478H19.6368V8.78958H15.2732V4.39478ZM6.5459 4.39478H10.9095V8.78958H6.5459V4.39478Z'
          fill='#F2A73B'
        />
        <path
          d='M10.9096 8.78979H15.2732V13.1846H10.9096V8.78979ZM15.2732 8.78979H19.6368V13.1846H15.2732V8.78979ZM6.5459 8.78979H10.9096V13.1846H6.5459V8.78979Z'
          fill='#EE792F'
        />
        <path d='M8.72754 13.1846H13.0911V17.5794H8.72754V13.1846Z' fill='black' />
        <path d='M10.9092 13.1846H15.2728V17.5794H10.9092V13.1846Z' fill='#EB5829' />
        <path
          d='M19.6362 8.78979H23.9998V13.1846H19.6362V8.78979ZM2.18164 8.78979H6.54523V13.1846H2.18164V8.78979Z'
          fill='#EE792F'
        />
        <path d='M17.4541 13.1846H21.8177V17.5794H17.4541V13.1846Z' fill='black' />
        <path d='M19.6367 13.1846H24.0003V17.5794H19.6367V13.1846Z' fill='#EB5829' />
        <path d='M17.4541 17.5793H21.8177V21.9742H17.4541V17.5793Z' fill='black' />
        <path d='M2.18164 13.1846H6.54523V17.5794H2.18164V13.1846Z' fill='#EB5829' />
        <path
          d='M19.6362 17.5793H23.9998V21.9742H19.6362V17.5793ZM2.18164 17.5793H6.54523V21.9742H2.18164V17.5793Z'
          fill='#EA3326'
        />
      </g>
      <defs>
        <clipPath id='clip0_1621_58'>
          <rect width='24' height='22' fill='white' />
        </clipPath>
      </defs>
    </svg>
  )
}

export function BrainIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns='http://www.w3.org/2000/svg'
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      stroke='currentColor'
      strokeWidth='2'
      strokeLinecap='round'
      strokeLinejoin='round'
    >
      <path d='M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z' />
      <path d='M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z' />
      <path d='M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4' />
      <path d='M17.599 6.5a3 3 0 0 0 .399-1.375' />
      <path d='M6.003 5.125A3 3 0 0 0 6.401 6.5' />
      <path d='M3.477 10.896a4 4 0 0 1 .585-.396' />
      <path d='M19.938 10.5a4 4 0 0 1 .585.396' />
      <path d='M6 18a4 4 0 0 1-1.967-.516' />
      <path d='M19.967 17.484A4 4 0 0 1 18 18' />
    </svg>
  )
}

export function StagehandIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='108'
      height='159'
      viewBox='0 0 108 159'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M15 26C22.8234 31.822 23.619 41.405 25.3125 50.3867C25.8461 53.1914 26.4211 55.9689 27.0625 58.75C27.7987 61.9868 28.4177 65.2319 29 68.5C29.332 70.3336 29.6653 72.1669 30 74C30.1418 74.7863 30.2836 75.5727 30.4297 76.3828C31.8011 83.2882 33.3851 90.5397 39.4375 94.75C40.3405 95.3069 40.3405 95.3069 41.2617 95.875C43.8517 97.5512 45.826 99.826 48 102C50.6705 102.89 52.3407 103.143 55.0898 103.211C55.8742 103.239 56.6586 103.268 57.4668 103.297C59.1098 103.349 60.7531 103.393 62.3965 103.43C65.8896 103.567 68.4123 103.705 71.5664 105.289C73 107 73 107 73 111C73.66 111 74.32 111 75 111C74.0759 106.912 74.0759 106.912 71.4766 103.828C67.0509 102.348 62.3634 102.64 57.7305 102.609C52.3632 102.449 49.2783 101.537 45 98C41.8212 94.0795 41.5303 90.9791 42 86C44.9846 83.0154 48.2994 83.6556 52.3047 83.6289C53.139 83.6199 53.9734 83.6108 54.833 83.6015C56.6067 83.587 58.3805 83.5782 60.1543 83.5745C62.8304 83.5627 65.5041 83.5137 68.1797 83.4629C81.1788 83.34 91.8042 85.3227 102 94C106.37 100.042 105.483 106.273 104.754 113.406C103.821 119.026 101.968 124.375 100.125 129.75C99.8806 130.471 99.6361 131.193 99.3843 131.936C97.7783 136.447 95.9466 140.206 93 144C92.34 144 91.68 144 91 144C91 144.66 91 145.32 91 146C79.0816 156.115 63.9798 156.979 49 156C36.6394 154.226 26.7567 148.879 19 139C11.0548 125.712 11.6846 105.465 11.3782 90.4719C11.0579 77.4745 8.03411 64.8142 5.4536 52.1135C5.04373 50.0912 4.64233 48.0673 4.24218 46.043C4.00354 44.8573 3.7649 43.6716 3.51903 42.45C2.14425 33.3121 2.14425 33.3121 4.87499 29.125C8.18297 25.817 10.3605 25.4542 15 26Z'
        fill='#FDFDFD'
      />
      <path
        d='M91 0.999996C94.8466 2.96604 96.2332 5.08365 97.6091 9.03564C99.203 14.0664 99.4412 18.7459 99.4414 23.9922C99.4538 24.9285 99.4663 25.8647 99.4791 26.8294C99.5049 28.8198 99.5247 30.8103 99.539 32.8008C99.5785 37.9693 99.6682 43.1369 99.7578 48.3047C99.7747 49.3188 99.7917 50.3328 99.8091 51.3776C99.9603 59.6066 100.323 67.7921 100.937 76C101.012 77.0582 101.087 78.1163 101.164 79.2065C101.646 85.1097 102.203 90.3442 105.602 95.3672C107.492 98.9262 107.45 102.194 107.375 106.125C107.366 106.881 107.356 107.638 107.346 108.417C107.18 114.639 106.185 120.152 104 126C103.636 126.996 103.273 127.993 102.898 129.02C98.2182 141.022 92.6784 149.349 80.7891 155.062C67.479 160.366 49.4234 159.559 36 155C32.4272 153.286 29.2162 151.308 26 149C25.0719 148.361 24.1437 147.721 23.1875 147.062C8.32968 133.054 9.60387 109.231 8.73413 90.3208C8.32766 81.776 7.51814 73.4295 5.99999 65C5.82831 64.0338 5.65662 63.0675 5.47973 62.072C4.98196 59.3363 4.46395 56.6053 3.93749 53.875C3.76412 52.9572 3.59074 52.0394 3.4121 51.0938C2.75101 47.6388 2.11387 44.3416 0.999995 41C0.505898 36.899 0.0476353 32.7768 2.04687 29.0469C4.91881 25.5668 6.78357 24.117 11.25 23.6875C15.8364 24.0697 17.5999 24.9021 21 28C24.7763 34.3881 26.047 41.2626 27.1875 48.5C27.5111 50.4693 27.8377 52.4381 28.168 54.4062C28.3733 55.695 28.3733 55.695 28.5828 57.0098C28.8087 58.991 28.8087 58.991 30 60C30.3171 59.4947 30.6342 58.9894 30.9609 58.4688C33.1122 55.4736 34.7097 53.3284 38.3789 52.3945C44.352 52.203 48.1389 53.6183 53 57C53.0928 56.1338 53.0928 56.1338 53.1875 55.25C54.4089 51.8676 55.9015 50.8075 59 49C63.8651 48.104 66.9348 48.3122 71.1487 51.0332C72.0896 51.6822 73.0305 52.3313 74 53C73.9686 51.2986 73.9686 51.2986 73.9365 49.5627C73.8636 45.3192 73.818 41.0758 73.7803 36.8318C73.7603 35.0016 73.733 33.1715 73.6982 31.3415C73.6492 28.6976 73.6269 26.0545 73.6094 23.4102C73.5887 22.6035 73.5681 21.7969 73.5468 20.9658C73.5441 13.8444 75.5121 7.83341 80.25 2.4375C83.9645 0.495841 86.8954 0.209055 91 0.999996ZM3.99999 30C1.56925 34.8615 3.215 40.9393 4.24218 46.043C4.37061 46.6927 4.49905 47.3424 4.63137 48.0118C5.03968 50.0717 5.45687 52.1296 5.87499 54.1875C11.1768 80.6177 11.1768 80.6177 11.4375 93.375C11.7542 120.78 11.7542 120.78 23.5625 144.375C28.5565 149.002 33.5798 151.815 40 154C40.6922 154.244 41.3844 154.487 42.0977 154.738C55.6463 158.576 72.4909 156.79 84.8086 150.316C87.0103 148.994 89.0458 147.669 91 146C91 145.34 91 144.68 91 144C91.66 144 92.32 144 93 144C97.1202 138.98 99.3206 133.053 101.25 126.937C101.505 126.174 101.76 125.41 102.023 124.623C104.94 115.65 107.293 104.629 103.625 95.625C96.3369 88.3369 86.5231 83.6919 76.1988 83.6088C74.9905 83.6226 74.9905 83.6226 73.7578 83.6367C72.9082 83.6362 72.0586 83.6357 71.1833 83.6352C69.4034 83.6375 67.6235 83.6472 65.8437 83.6638C63.1117 83.6876 60.3806 83.6843 57.6484 83.6777C55.9141 83.6833 54.1797 83.6904 52.4453 83.6992C51.6277 83.6983 50.81 83.6974 49.9676 83.6964C45.5122 83.571 45.5122 83.571 42 86C41.517 90.1855 41.733 92.4858 43.6875 96.25C46.4096 99.4871 48.6807 101.674 53.0105 102.282C55.3425 102.411 57.6645 102.473 60 102.5C69.8847 102.612 69.8847 102.612 74 106C74.8125 108.687 74.8125 108.688 75 111C74.34 111 73.68 111 73 111C72.8969 110.216 72.7937 109.432 72.6875 108.625C72.224 105.67 72.224 105.67 69 104C65.2788 103.745 61.5953 103.634 57.8672 103.609C51.1596 103.409 46.859 101.691 41.875 97C41.2562 96.34 40.6375 95.68 40 95C39.175 94.4637 38.35 93.9275 37.5 93.375C30.9449 87.1477 30.3616 77.9789 29.4922 69.418C29.1557 66.1103 29.1557 66.1103 28.0352 63.625C26.5234 59.7915 26.1286 55.8785 25.5625 51.8125C23.9233 38.3 23.9233 38.3 17 27C11.7018 24.3509 7.9915 26.1225 3.99999 30Z'
        fill='#1F1F1F'
      />
      <path
        d='M89.0976 2.53906C91 3 91 3 93.4375 5.3125C96.1586 9.99276 96.178 14.1126 96.2461 19.3828C96.2778 21.1137 96.3098 22.8446 96.342 24.5754C96.3574 25.4822 96.3728 26.3889 96.3887 27.3232C96.6322 41.3036 96.9728 55.2117 98.3396 69.1353C98.9824 75.7746 99.0977 82.3308 99 89C96.5041 88.0049 94.0126 87.0053 91.5351 85.9648C90.3112 85.4563 90.3112 85.4563 89.0625 84.9375C87.8424 84.4251 87.8424 84.4251 86.5976 83.9023C83.7463 82.9119 80.9774 82.4654 78 82C76.7702 65.9379 75.7895 49.8907 75.7004 33.7775C75.6919 32.3138 75.6783 30.8501 75.6594 29.3865C75.5553 20.4082 75.6056 12.1544 80.6875 4.4375C83.6031 2.62508 85.7 2.37456 89.0976 2.53906Z'
        fill='#FBFBFB'
      />
      <path
        d='M97 13C97.99 13.495 97.99 13.495 99 14C99.0297 15.8781 99.0297 15.8781 99.0601 17.7942C99.4473 46.9184 99.4473 46.9184 100.937 76C101.012 77.0574 101.087 78.1149 101.164 79.2043C101.646 85.1082 102.203 90.3434 105.602 95.3672C107.492 98.9262 107.45 102.194 107.375 106.125C107.366 106.881 107.356 107.638 107.346 108.417C107.18 114.639 106.185 120.152 104 126C103.636 126.996 103.273 127.993 102.898 129.02C98.2182 141.022 92.6784 149.349 80.7891 155.062C67.479 160.366 49.4234 159.559 36 155C32.4272 153.286 29.2162 151.308 26 149C24.6078 148.041 24.6078 148.041 23.1875 147.062C13.5484 137.974 10.832 124.805 9.99999 112C9.91815 101.992 10.4358 91.9898 11 82C11.33 82 11.66 82 12 82C12.0146 82.6118 12.0292 83.2236 12.0442 83.854C11.5946 115.845 11.5946 115.845 24.0625 143.875C28.854 148.273 33.89 150.868 40 153C40.6935 153.245 41.387 153.49 42.1016 153.742C56.9033 157.914 73.8284 155.325 87 148C88.3301 147.327 89.6624 146.658 91 146C91 145.34 91 144.68 91 144C91.66 144 92.32 144 93 144C100.044 130.286 105.786 114.602 104 99C102.157 94.9722 100.121 93.0631 96.3125 90.875C95.5042 90.398 94.696 89.9211 93.8633 89.4297C85.199 85.1035 78.1558 84.4842 68.5 84.3125C67.2006 84.2783 65.9012 84.2442 64.5625 84.209C61.3751 84.127 58.1879 84.0577 55 84C55 83.67 55 83.34 55 83C58.9087 82.7294 62.8179 82.4974 66.7309 82.2981C68.7007 82.1902 70.6688 82.0535 72.6367 81.916C82.854 81.4233 90.4653 83.3102 99 89C98.8637 87.6094 98.8637 87.6094 98.7246 86.1907C96.96 67.8915 95.697 49.7051 95.75 31.3125C95.751 30.5016 95.7521 29.6908 95.7532 28.8554C95.7901 15.4198 95.7901 15.4198 97 13Z'
        fill='#262114'
      />
      <path
        d='M68 51C72.86 54.06 74.644 56.5072 76 62C76.249 65.2763 76.2347 68.5285 76.1875 71.8125C76.1868 72.6833 76.1862 73.554 76.1855 74.4512C76.1406 80.8594 76.1406 80.8594 75 82C73.5113 82.0867 72.0185 82.107 70.5273 82.0976C69.6282 82.0944 68.7291 82.0912 67.8027 82.0879C66.8572 82.0795 65.9117 82.0711 64.9375 82.0625C63.9881 82.058 63.0387 82.0535 62.0605 82.0488C59.707 82.037 57.3535 82.0205 55 82C53.6352 77.2188 53.738 72.5029 53.6875 67.5625C53.6585 66.6208 53.6295 65.6792 53.5996 64.709C53.5591 60.2932 53.5488 57.7378 55.8945 53.9023C59.5767 50.5754 63.1766 50.211 68 51Z'
        fill='#F8F8F8'
      />
      <path
        d='M46 55C48.7557 57.1816 50.4359 58.8718 52 62C52.0837 63.5215 52.1073 65.0466 52.0977 66.5703C52.0944 67.4662 52.0912 68.3621 52.0879 69.2852C52.0795 70.2223 52.0711 71.1595 52.0625 72.125C52.058 73.0699 52.0535 74.0148 52.0488 74.9883C52.037 77.3256 52.0206 79.6628 52 82C50.9346 82.1992 50.9346 82.1992 49.8477 82.4023C48.9286 82.5789 48.0094 82.7555 47.0625 82.9375C46.146 83.1115 45.2294 83.2855 44.2852 83.4648C42.0471 83.7771 42.0471 83.7771 41 85C40.7692 86.3475 40.5885 87.7038 40.4375 89.0625C40.2931 90.3619 40.1487 91.6613 40 93C37 92 37 92 35.8672 90.1094C35.5398 89.3308 35.2123 88.5522 34.875 87.75C34.5424 86.9817 34.2098 86.2134 33.8672 85.4219C31.9715 80.1277 31.7884 75.065 31.75 69.5C31.7294 68.7536 31.7087 68.0073 31.6875 67.2383C31.6551 62.6607 32.0474 59.7266 35 56C38.4726 54.2637 42.2119 54.3981 46 55Z'
        fill='#FAFAFA'
      />
      <path
        d='M97 13C97.66 13.33 98.32 13.66 99 14C99.0297 15.8781 99.0297 15.8781 99.0601 17.7942C99.4473 46.9184 99.4473 46.9184 100.937 76C101.012 77.0574 101.087 78.1149 101.164 79.2043C101.566 84.1265 102.275 88.3364 104 93C103.625 95.375 103.625 95.375 103 97C102.361 96.2781 101.721 95.5563 101.062 94.8125C94.4402 88.1902 85.5236 84.8401 76.2891 84.5859C75.0451 84.5473 73.8012 84.5086 72.5195 84.4688C71.2343 84.4378 69.9491 84.4069 68.625 84.375C66.6624 84.317 66.6624 84.317 64.6601 84.2578C61.4402 84.1638 58.2203 84.0781 55 84C55 83.67 55 83.34 55 83C58.9087 82.7294 62.8179 82.4974 66.7309 82.2981C68.7007 82.1902 70.6688 82.0535 72.6367 81.916C82.854 81.4233 90.4653 83.3102 99 89C98.9091 88.0729 98.8182 87.1458 98.7246 86.1907C96.96 67.8915 95.697 49.7051 95.75 31.3125C95.751 30.5016 95.7521 29.6908 95.7532 28.8554C95.7901 15.4198 95.7901 15.4198 97 13Z'
        fill='#423B28'
      />
      <path
        d='M91 0.999996C94.3999 3.06951 96.8587 5.11957 98 9C97.625 12.25 97.625 12.25 97 15C95.804 12.6081 94.6146 10.2139 93.4375 7.8125C92.265 5.16236 92.265 5.16236 91 4C88.074 3.7122 85.8483 3.51695 83 4C79.1128 7.37574 78.178 11.0991 77 16C76.8329 18.5621 76.7615 21.1317 76.7695 23.6992C76.77 24.4155 76.7704 25.1318 76.7709 25.8698C76.7739 27.3783 76.7817 28.8868 76.7942 30.3953C76.8123 32.664 76.8147 34.9324 76.8144 37.2012C76.8329 44.6001 77.0765 51.888 77.7795 59.259C78.1413 63.7564 78.1068 68.2413 78.0625 72.75C78.058 73.6498 78.0535 74.5495 78.0488 75.4766C78.0373 77.6511 78.0193 79.8255 78 82C78.99 82.495 78.99 82.495 80 83C68.78 83.33 57.56 83.66 46 84C46.495 83.01 46.495 83.01 47 82C52.9349 80.7196 58.8909 80.8838 64.9375 80.9375C65.9075 80.942 66.8775 80.9465 67.8769 80.9512C70.2514 80.9629 72.6256 80.9793 75 81C75.0544 77.9997 75.0939 75.0005 75.125 72C75.1418 71.1608 75.1585 70.3216 75.1758 69.457C75.2185 63.9475 74.555 59.2895 73 54C73.66 54 74.32 54 75 54C74.9314 53.2211 74.8629 52.4422 74.7922 51.6396C74.1158 43.5036 73.7568 35.4131 73.6875 27.25C73.644 25.5194 73.644 25.5194 73.5996 23.7539C73.5376 15.3866 74.6189 8.85069 80.25 2.4375C83.9433 0.506911 86.9162 0.173322 91 0.999996Z'
        fill='#131311'
      />
      <path
        d='M15 24C20.2332 26.3601 22.1726 29.3732 24.1875 34.5195C26.8667 42.6988 27.2651 50.4282 27 59C26.67 59 26.34 59 26 59C25.8945 58.436 25.7891 57.8721 25.6804 57.291C25.1901 54.6926 24.6889 52.0963 24.1875 49.5C24.0218 48.6131 23.8562 47.7262 23.6855 46.8125C21.7568 35.5689 21.7568 35.5689 15 27C12.0431 26.2498 12.0431 26.2498 8.99999 27C5.97965 28.9369 5.97965 28.9369 3.99999 32C3.67226 36.9682 4.31774 41.4911 5.27733 46.3594C5.40814 47.0304 5.53894 47.7015 5.67371 48.3929C5.94892 49.7985 6.22723 51.2035 6.50854 52.6079C6.93887 54.7569 7.35989 56.9075 7.77929 59.0586C9.09359 66.104 9.09359 66.104 11 73C11.0836 75.2109 11.1073 77.4243 11.0976 79.6367C11.0944 80.9354 11.0912 82.2342 11.0879 83.5723C11.0795 84.944 11.0711 86.3158 11.0625 87.6875C11.0575 89.071 11.0529 90.4544 11.0488 91.8379C11.037 95.2253 11.0206 98.6126 11 102C8.54975 99.5498 8.73228 98.8194 8.65624 95.4492C8.62812 94.53 8.60001 93.6108 8.57104 92.6638C8.54759 91.6816 8.52415 90.6994 8.49999 89.6875C8.20265 81.3063 7.58164 73.2485 5.99999 65C5.67135 63.2175 5.34327 61.435 5.01562 59.6523C4.31985 55.9098 3.62013 52.1681 2.90233 48.4297C2.75272 47.6484 2.60311 46.867 2.44897 46.062C1.99909 43.8187 1.99909 43.8187 0.999995 41C0.505898 36.899 0.0476353 32.7768 2.04687 29.0469C6.06003 24.1839 8.81126 23.4843 15 24Z'
        fill='#2A2311'
      />
      <path
        d='M11 82C11.33 82 11.66 82 12 82C12.0146 82.6118 12.0292 83.2236 12.0442 83.854C11.5946 115.845 11.5946 115.845 24.0625 143.875C30.0569 149.404 36.9894 152.617 45 154C42 156 42 156 39.4375 156C29.964 153.244 20.8381 146.677 16 138C8.26993 120.062 9.92611 101.014 11 82Z'
        fill='#272214'
      />
      <path
        d='M68 49C70.3478 50.1116 71.9703 51.3346 74 53C73.34 53.66 72.68 54.32 72 55C71.505 54.505 71.01 54.01 70.5 53.5C67.6718 51.8031 65.3662 51.5622 62.0976 51.4062C58.4026 52.4521 57.1992 53.8264 55 57C54.3826 61.2861 54.5302 65.4938 54.6875 69.8125C54.7101 70.9823 54.7326 72.1521 54.7559 73.3574C54.8147 76.2396 54.8968 79.1191 55 82C54.01 82 53.02 82 52 82C51.9854 81.4203 51.9708 80.8407 51.9558 80.2434C51.881 77.5991 51.7845 74.9561 51.6875 72.3125C51.6649 71.4005 51.6424 70.4885 51.6191 69.5488C51.4223 64.6292 51.2621 60.9548 48 57C45.6603 55.8302 44.1661 55.8339 41.5625 55.8125C40.78 55.7983 39.9976 55.7841 39.1914 55.7695C36.7079 55.8591 36.7079 55.8591 34 58C32.7955 60.5518 32.7955 60.5518 32 63C31.34 63 30.68 63 30 63C30.2839 59.6879 31.0332 57.9518 32.9375 55.1875C36.7018 52.4987 38.9555 52.3484 43.4844 52.5586C47.3251 53.2325 49.8148 54.7842 53 57C53.0928 56.1338 53.0928 56.1338 53.1875 55.25C55.6091 48.544 61.7788 47.8649 68 49Z'
        fill='#1F1A0F'
      />
      <path
        d='M99 60C99.33 60 99.66 60 100 60C100.05 60.7865 100.1 61.573 100.152 62.3833C100.385 65.9645 100.63 69.5447 100.875 73.125C100.954 74.3625 101.032 75.6 101.113 76.875C101.197 78.0738 101.281 79.2727 101.367 80.5078C101.44 81.6075 101.514 82.7073 101.589 83.8403C102.013 87.1 102.94 89.8988 104 93C103.625 95.375 103.625 95.375 103 97C102.361 96.2781 101.721 95.5563 101.062 94.8125C94.4402 88.1902 85.5236 84.8401 76.2891 84.5859C74.4231 84.5279 74.4231 84.5279 72.5195 84.4688C71.2343 84.4378 69.9491 84.4069 68.625 84.375C67.3166 84.3363 66.0082 84.2977 64.6601 84.2578C61.4402 84.1638 58.2203 84.0781 55 84C55 83.67 55 83.34 55 83C58.9087 82.7294 62.8179 82.4974 66.7309 82.2981C68.7007 82.1902 70.6688 82.0535 72.6367 81.916C82.854 81.4233 90.4653 83.3102 99 89C98.9162 87.912 98.8324 86.8241 98.7461 85.7031C98.1266 77.012 97.9127 68.6814 99 60Z'
        fill='#332E22'
      />
      <path
        d='M15 24C20.2332 26.3601 22.1726 29.3732 24.1875 34.5195C26.8667 42.6988 27.2651 50.4282 27 59C26.67 59 26.34 59 26 59C25.8945 58.436 25.7891 57.8721 25.6804 57.291C25.1901 54.6926 24.6889 52.0963 24.1875 49.5C24.0218 48.6131 23.8562 47.7262 23.6855 46.8125C21.7568 35.5689 21.7568 35.5689 15 27C12.0431 26.2498 12.0431 26.2498 8.99999 27C5.2818 29.7267 4.15499 31.2727 3.18749 35.8125C3.12562 36.8644 3.06374 37.9163 2.99999 39C2.33999 39 1.67999 39 0.999992 39C0.330349 31.2321 0.330349 31.2321 3.37499 27.5625C7.31431 23.717 9.51597 23.543 15 24Z'
        fill='#1D180A'
      />
      <path
        d='M91 0.999996C94.3999 3.06951 96.8587 5.11957 98 9C97.625 12.25 97.625 12.25 97 15C95.804 12.6081 94.6146 10.2139 93.4375 7.8125C92.265 5.16236 92.265 5.16236 91 4C85.4345 3.33492 85.4345 3.33491 80.6875 5.75C78.5543 9.85841 77.6475 13.9354 76.7109 18.4531C76.4763 19.2936 76.2417 20.1341 76 21C75.34 21.33 74.68 21.66 74 22C73.5207 15.4102 74.5846 10.6998 78 5C81.755 0.723465 85.5463 -0.103998 91 0.999996Z'
        fill='#16130D'
      />
      <path
        d='M42 93C42.5569 93.7631 43.1137 94.5263 43.6875 95.3125C46.4238 98.4926 48.7165 100.679 53.0105 101.282C55.3425 101.411 57.6646 101.473 60 101.5C70.6207 101.621 70.6207 101.621 75 106C75.0406 107.666 75.0427 109.334 75 111C74.34 111 73.68 111 73 111C72.7112 110.196 72.4225 109.391 72.125 108.562C71.2674 105.867 71.2674 105.867 69 105C65.3044 104.833 61.615 104.703 57.916 104.658C52.1631 104.454 48.7484 103.292 44 100C41.5625 97.25 41.5625 97.25 40 95C40.66 95 41.32 95 42 95C42 94.34 42 93.68 42 93Z'
        fill='#2B2B2B'
      />
      <path
        d='M11 82C11.33 82 11.66 82 12 82C12.1682 86.6079 12.3287 91.216 12.4822 95.8245C12.5354 97.3909 12.5907 98.9574 12.6482 100.524C12.7306 102.78 12.8055 105.036 12.8789 107.293C12.9059 107.989 12.933 108.685 12.9608 109.402C13.0731 113.092 12.9015 116.415 12 120C11.67 120 11.34 120 11 120C9.63778 112.17 10.1119 104.4 10.4375 96.5C10.4908 95.0912 10.5436 93.6823 10.5957 92.2734C10.7247 88.8487 10.8596 85.4243 11 82Z'
        fill='#4D483B'
      />
      <path
        d='M43.4844 52.5586C47.3251 53.2325 49.8148 54.7842 53 57C52 59 52 59 50 60C49.5256 59.34 49.0512 58.68 48.5625 58C45.2656 55.4268 43.184 55.5955 39.1211 55.6641C36.7043 55.8955 36.7043 55.8955 34 58C32.7955 60.5518 32.7955 60.5518 32 63C31.34 63 30.68 63 30 63C30.2839 59.6879 31.0332 57.9518 32.9375 55.1875C36.7018 52.4987 38.9555 52.3484 43.4844 52.5586Z'
        fill='#221F16'
      />
      <path
        d='M76 73C76.33 73 76.66 73 77 73C77 75.97 77 78.94 77 82C78.485 82.495 78.485 82.495 80 83C68.78 83.33 57.56 83.66 46 84C46.33 83.34 46.66 82.68 47 82C52.9349 80.7196 58.8909 80.8838 64.9375 80.9375C65.9075 80.942 66.8775 80.9465 67.8769 80.9512C70.2514 80.9629 72.6256 80.9793 75 81C75.33 78.36 75.66 75.72 76 73Z'
        fill='#040404'
      />
      <path
        d='M27 54C27.33 54 27.66 54 28 54C28.33 56.97 28.66 59.94 29 63C29.99 63 30.98 63 32 63C32 66.96 32 70.92 32 75C31.01 74.67 30.02 74.34 29 74C28.8672 73.2523 28.7344 72.5047 28.5977 71.7344C28.421 70.7495 28.2444 69.7647 28.0625 68.75C27.8885 67.7755 27.7144 66.8009 27.5352 65.7969C27.0533 63.087 27.0533 63.087 26.4062 60.8125C25.8547 58.3515 26.3956 56.4176 27 54Z'
        fill='#434039'
      />
      <path
        d='M78 5C78.99 5.33 79.98 5.66 81 6C80.3194 6.92812 80.3194 6.92812 79.625 7.875C77.7233 11.532 77.1555 14.8461 76.5273 18.8906C76.3533 19.5867 76.1793 20.2828 76 21C75.34 21.33 74.68 21.66 74 22C73.5126 15.2987 74.9229 10.9344 78 5Z'
        fill='#2A2313'
      />
      <path
        d='M12 115C12.99 115.495 12.99 115.495 14 116C14.5334 118.483 14.9326 120.864 15.25 123.375C15.3531 124.061 15.4562 124.747 15.5625 125.453C16.0763 129.337 16.2441 130.634 14 134C12.6761 127.57 11.752 121.571 12 115Z'
        fill='#2F2C22'
      />
      <path
        d='M104 95C107 98 107 98 107.363 101.031C107.347 102.176 107.33 103.321 107.312 104.5C107.309 105.645 107.305 106.789 107.301 107.969C107 111 107 111 105 114C104.67 107.73 104.34 101.46 104 95Z'
        fill='#120F05'
      />
      <path
        d='M56 103C58.6048 102.919 61.2071 102.86 63.8125 102.812C64.5505 102.787 65.2885 102.762 66.0488 102.736C71.4975 102.662 71.4975 102.662 74 104.344C75.374 106.619 75.2112 108.396 75 111C74.34 111 73.68 111 73 111C72.7112 110.196 72.4225 109.391 72.125 108.562C71.2674 105.867 71.2674 105.867 69 105C66.7956 104.77 64.5861 104.589 62.375 104.438C61.1865 104.354 59.998 104.27 58.7734 104.184C57.4006 104.093 57.4006 104.093 56 104C56 103.67 56 103.34 56 103Z'
        fill='#101010'
      />
      <path
        d='M23 40C23.66 40 24.32 40 25 40C27.3084 46.3482 27.1982 52.2948 27 59C26.67 59 26.34 59 26 59C25.01 52.73 24.02 46.46 23 40Z'
        fill='#191409'
      />
      <path
        d='M47 83C46.3606 83.3094 45.7212 83.6187 45.0625 83.9375C41.9023 87.0977 42.181 90.6833 42 95C41.01 94.67 40.02 94.34 39 94C39.3463 85.7409 39.3463 85.7409 41.875 82.875C44 82 44 82 47 83Z'
        fill='#171717'
      />
      <path
        d='M53 61C53.33 61 53.66 61 54 61C54.33 67.93 54.66 74.86 55 82C54.01 82 53.02 82 52 82C52.33 75.07 52.66 68.14 53 61Z'
        fill='#444444'
      />
      <path
        d='M81 154C78.6696 156.33 77.8129 156.39 74.625 156.75C73.4687 156.897 73.4687 156.897 72.2891 157.047C69.6838 156.994 68.2195 156.317 66 155C67.7478 154.635 69.4984 154.284 71.25 153.938C72.7118 153.642 72.7118 153.642 74.2031 153.34C76.8681 153.016 78.4887 153.145 81 154Z'
        fill='#332F23'
      />
      <path
        d='M19 28C19.66 28 20.32 28 21 28C21.6735 29.4343 22.3386 30.8726 23 32.3125C23.5569 33.5133 23.5569 33.5133 24.125 34.7383C25 37 25 37 25 40C22 39 22 39 21.0508 37.2578C20.8071 36.554 20.5635 35.8502 20.3125 35.125C20.0611 34.4263 19.8098 33.7277 19.5508 33.0078C19 31 19 31 19 28Z'
        fill='#282213'
      />
      <path
        d='M102 87C104.429 93.2857 104.429 93.2857 103 97C100.437 94.75 100.437 94.75 98 92C98.0625 89.75 98.0625 89.75 99 88C101 87 101 87 102 87Z'
        fill='#37301F'
      />
      <path
        d='M53 56C53.33 56 53.66 56 54 56C53.67 62.27 53.34 68.54 53 75C52.67 75 52.34 75 52 75C51.7788 72.2088 51.5726 69.4179 51.375 66.625C51.3105 65.8309 51.2461 65.0369 51.1797 64.2188C51.0394 62.1497 51.0124 60.0737 51 58C51.66 57.34 52.32 56.68 53 56Z'
        fill='#030303'
      />
      <path
        d='M100 129C100.33 129 100.66 129 101 129C100.532 133.776 99.7567 137.045 97 141C96.34 140.67 95.68 140.34 95 140C96.65 136.37 98.3 132.74 100 129Z'
        fill='#1E1A12'
      />
      <path
        d='M15 131C17.7061 132.353 17.9618 133.81 19.125 136.562C19.4782 137.389 19.8314 138.215 20.1953 139.066C20.4609 139.704 20.7264 140.343 21 141C20.01 141 19.02 141 18 141C15.9656 137.27 15 135.331 15 131Z'
        fill='#1C1912'
      />
      <path
        d='M63 49C69.4 49.4923 69.4 49.4923 72.4375 52.0625C73.2109 53.0216 73.2109 53.0216 74 54C70.8039 54 69.5828 53.4533 66.8125 52C66.0971 51.6288 65.3816 51.2575 64.6445 50.875C64.1018 50.5863 63.5591 50.2975 63 50C63 49.67 63 49.34 63 49Z'
        fill='#13110C'
      />
      <path
        d='M0.999992 39C1.98999 39 2.97999 39 3.99999 39C5.24999 46.625 5.24999 46.625 2.99999 50C2.33999 46.37 1.67999 42.74 0.999992 39Z'
        fill='#312C1E'
      />
      <path
        d='M94 5C94.66 5 95.32 5 96 5C97.8041 7.75924 98.0127 8.88972 97.625 12.25C97.4187 13.1575 97.2125 14.065 97 15C95.1161 11.7345 94.5071 8.71888 94 5Z'
        fill='#292417'
      />
      <path
        d='M20 141C23.3672 142.393 24.9859 143.979 27 147C24.625 146.812 24.625 146.812 22 146C20.6875 143.438 20.6875 143.438 20 141Z'
        fill='#373328'
      />
      <path
        d='M86 83C86.33 83.99 86.66 84.98 87 86C83.37 85.34 79.74 84.68 76 84C80.3553 81.8223 81.4663 81.9696 86 83Z'
        fill='#2F2F2F'
      />
      <path
        d='M42 93C46 97.625 46 97.625 46 101C44.02 99.35 42.04 97.7 40 96C40.66 95.67 41.32 95.34 42 95C42 94.34 42 93.68 42 93Z'
        fill='#232323'
      />
      <path
        d='M34 55C34.66 55.33 35.32 55.66 36 56C35.5256 56.7838 35.0512 57.5675 34.5625 58.375C33.661 59.8895 32.7882 61.4236 32 63C31.34 63 30.68 63 30 63C30.4983 59.3125 31.1007 57.3951 34 55Z'
        fill='#110F0A'
      />
    </svg>
  )
}

export function BrowserUseIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      version='1.0'
      xmlns='http://www.w3.org/2000/svg'
      width='150pt'
      height='150pt'
      viewBox='0 0 150 150'
      preserveAspectRatio='xMidYMid meet'
    >
      <g transform='translate(0,150) scale(0.05,-0.05)' fill='#000000' stroke='none'>
        <path
          d='M786 2713 c-184 -61 -353 -217 -439 -405 -76 -165 -65 -539 19 -666
l57 -85 -48 -124 c-203 -517 -79 -930 346 -1155 159 -85 441 -71 585 28 l111
77 196 -76 c763 -293 1353 304 1051 1063 -77 191 -77 189 -14 282 163 239 97
660 -140 893 -235 231 -528 256 -975 83 l-96 -37 -121 67 c-144 79 -383 103
-532 55z m459 -235 c88 -23 96 -51 22 -79 -29 -11 -84 -47 -121 -80 -57 -50
-84 -59 -178 -59 -147 0 -190 -44 -238 -241 -102 -424 -230 -440 -230 -29 1
417 289 606 745 488z m1046 -18 c174 -85 266 -309 239 -582 -26 -256 -165
-165 -230 151 -73 356 -469 332 -954 -58 -587 -472 -829 -1251 -388 -1251 108
0 126 -7 214 -80 54 -44 104 -80 113 -80 54 0 -2 -43 -89 -69 -220 -66 -426
-22 -568 120 -599 599 871 2232 1663 1849z m-234 -510 c969 -1036 357 -1962
-787 -1190 -254 171 -348 303 -323 454 21 128 40 123 231 -59 691 -658 1362
-583 1052 117 -106 239 -366 585 -504 671 l-72 44 98 45 c150 68 169 63 305
-82z m-329 -310 c161 -184 163 -160 -30 -338 -188 -173 -180 -173 -386 19
-163 153 -163 157 7 324 218 213 219 213 409 -5z m354 -375 c92 -239 -179
-462 -377 -309 l-46 35 186 163 c211 186 209 185 237 111z'
        />
      </g>
    </svg>
  )
}

export function Mem0Icon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='479'
      height='108'
      viewBox='0 0 108 108'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        fill='currentColor'
        clipRule='evenodd'
        d='M56.1537 8.9569C58.6168 8.9569 60.6135 6.96017 60.6135 4.49707C60.6135 2.03397 58.6168 0.0372314 56.1537 0.0372314C53.6906 0.0372314 51.6939 2.03397 51.6939 4.49707C51.6939 6.96017 53.6906 8.9569 56.1537 8.9569ZM38.4182 29.3537C37.2703 27.8698 36.3474 26.0164 36.833 24.2042C37.3186 22.392 39.0445 21.2483 40.7806 20.5372C42.9589 19.645 44.71 17.7815 45.3663 15.3323C46.4819 11.1688 44.0111 6.88914 39.8475 5.77352C35.684 4.6579 31.4044 7.12874 30.2887 11.2923C29.6325 13.7416 30.2173 16.2311 31.6578 18.093C32.8058 19.5768 33.7287 21.4301 33.2431 23.2423C32.7575 25.0544 31.0317 26.198 29.2955 26.909C27.1171 27.8012 25.3658 29.6647 24.7096 32.1141C23.5939 36.2776 26.0648 40.5572 30.2283 41.6728C34.3919 42.7885 38.6715 40.3176 39.7871 36.1541C40.4434 33.7049 39.8586 31.2155 38.4182 29.3537ZM86.1482 38.6257C84.3362 38.1402 82.483 39.063 80.9994 40.2109C79.1375 41.6515 76.6479 42.2364 74.1986 41.5801C70.035 40.4645 67.5642 36.1849 68.6798 32.0213C69.7954 27.8578 74.075 25.3869 78.2386 26.5026C80.6877 27.1588 82.5512 28.9099 83.4434 31.0881C84.1546 32.8243 85.2983 34.5504 87.1106 35.036C88.9228 35.5215 90.7761 34.5988 92.2601 33.4509C94.1219 32.0108 96.6111 31.4262 99.0601 32.0824C103.224 33.198 105.694 37.4776 104.579 41.6412C103.463 45.8047 99.1836 48.2755 95.0201 47.1599C92.5706 46.5036 90.707 44.7521 89.8148 42.5734C89.1039 40.8373 87.9604 39.1113 86.1482 38.6257ZM103.562 77.1769C100.514 80.2249 95.5723 80.2249 92.5243 77.1769C90.7315 75.3841 89.9933 72.9362 90.3095 70.6038C90.5616 68.7447 90.434 66.6783 89.1074 65.3517C87.7808 64.0251 85.7143 63.8976 83.8553 64.1497C81.5228 64.466 79.0748 63.7277 77.2819 61.9349C74.234 58.8869 74.234 53.9453 77.2819 50.8973C80.3299 47.8494 85.2715 47.8494 88.3195 50.8973C90.1127 52.6906 90.8509 55.1394 90.534 57.4724C90.2816 59.3311 90.4089 61.3973 91.7353 62.7236C93.0617 64.05 95.1279 64.1774 96.9867 63.9249C99.3197 63.6079 101.769 64.3461 103.562 66.1394C106.61 69.1873 106.61 74.129 103.562 77.1769ZM64.1201 96.0379C63.0045 100.201 65.4753 104.481 69.6389 105.597C73.8024 106.712 78.082 104.241 79.1977 100.078C79.854 97.6285 79.2691 95.139 77.8285 93.2771C76.6805 91.7934 75.7578 89.9402 76.2433 88.1282C76.7288 86.3162 78.4545 85.1727 80.1904 84.4617C82.3688 83.5695 84.1201 81.706 84.7764 79.2567C85.892 75.0932 83.4212 70.8136 79.2576 69.6979C75.0941 68.5823 70.8144 71.0531 69.6988 75.2167C69.0425 77.6661 69.6275 80.1558 71.0681 82.0177C72.2161 83.5013 73.1389 85.3545 72.6534 87.1665C72.1678 88.9784 70.4422 90.1219 68.7063 90.8328C66.5278 91.7249 64.7764 93.5885 64.1201 96.0379ZM28.837 99.7095C26.6818 95.9765 27.9607 91.2033 31.6937 89.048C33.8898 87.7801 36.4459 87.7008 38.6172 88.6104C40.3475 89.3353 42.3764 89.747 44.0011 88.809C45.6257 87.871 46.2836 85.908 46.521 84.047C46.8189 81.7117 48.1657 79.5377 50.3618 78.2697C54.0947 76.1145 58.868 77.3935 61.0232 81.1264C63.1784 84.8594 61.8994 89.6327 58.1665 91.7879C55.9707 93.0556 53.4149 93.1351 51.2438 92.2259C49.5133 91.5012 47.4842 91.0895 45.8595 92.0275C44.2347 92.9656 43.5767 94.9288 43.3391 96.7898C43.041 99.1248 41.6942 101.298 39.4984 102.566C35.7655 104.721 30.9922 103.442 28.837 99.7095ZM28.4865 71.1594C27.0029 72.3074 25.1498 73.2301 23.3379 72.7446C21.5259 72.2591 20.3825 70.5334 19.6716 68.7974C18.7795 66.6187 16.9159 64.8673 14.4664 64.2109C10.3028 63.0953 6.02323 65.5661 4.90761 69.7297C3.792 73.8932 6.26283 78.1728 10.4264 79.2885C12.8756 79.9447 15.365 79.3599 17.2268 77.9196C18.7106 76.7717 20.5639 75.8489 22.3759 76.3345C24.188 76.82 25.3316 78.5458 26.0426 80.2818C26.9349 82.4601 28.7983 84.2113 31.2476 84.8676C35.4111 85.9832 39.6907 83.5124 40.8064 79.3488C41.922 75.1853 39.4511 70.9056 35.2876 69.79C32.8381 69.1337 30.3484 69.7187 28.4865 71.1594ZM5.92479 34.1938C8.97272 31.1459 13.9144 31.1459 16.9623 34.1938C18.7553 35.9868 19.4935 38.4351 19.177 40.7677C18.9248 42.6264 19.0524 44.6924 20.3787 46.0188C21.7052 47.3453 23.7716 47.4727 25.6305 47.2203C27.9634 46.9035 30.412 47.6418 32.2051 49.4349C35.2531 52.4828 35.2531 57.4245 32.2051 60.4724C29.1572 63.5204 24.2155 63.5204 21.1676 60.4724C19.3746 58.6794 18.6364 56.231 18.9529 53.8983C19.2052 52.0397 19.0776 49.9737 17.7513 48.6474C16.4248 47.3209 14.3585 47.1935 12.4996 47.4459C10.1667 47.7627 7.71801 47.0245 5.92479 45.2313C2.87686 42.1834 2.87686 37.2417 5.92479 34.1938ZM65.4846 22.5615C63.8601 23.4994 63.2023 25.4621 62.965 27.3228C62.6672 29.6582 61.3204 31.8323 59.1242 33.1003C55.3912 35.2555 50.618 33.9765 48.4627 30.2436C46.3075 26.5106 47.5865 21.7374 51.3195 19.5821C53.5153 18.3144 56.0712 18.2349 58.2424 19.1443C59.9728 19.869 62.0019 20.2807 63.6267 19.3427C65.2513 18.4047 65.9093 16.4418 66.147 14.5809C66.4451 12.2461 67.7918 10.0725 69.9876 8.8048C73.7205 6.64959 78.4938 7.92858 80.649 11.6615C82.8042 15.3945 81.5252 20.1677 77.7923 22.323C75.596 23.591 73.0396 23.6702 70.8681 22.7603C69.138 22.0353 67.1091 21.6236 65.4846 22.5615ZM104.469 60.245C106.932 60.245 108.928 58.2483 108.928 55.7852C108.928 53.3221 106.932 51.3253 104.469 51.3253C102.006 51.3253 100.009 53.3221 100.009 55.7852C100.009 58.2483 102.006 60.245 104.469 60.245ZM96.2923 21.5931C96.2923 24.0562 94.2955 26.053 91.8324 26.053C89.3693 26.053 87.3726 24.0562 87.3726 21.5931C87.3726 19.13 89.3693 17.1333 91.8324 17.1333C94.2955 17.1333 96.2923 19.13 96.2923 21.5931ZM23.4484 20.1063C23.4484 22.5694 21.4516 24.5662 18.9885 24.5662C16.5254 24.5662 14.5287 22.5694 14.5287 20.1063C14.5287 17.6432 16.5254 15.6465 18.9885 15.6465C21.4516 15.6465 23.4484 17.6432 23.4484 20.1063ZM4.86563 60.245C7.32873 60.245 9.32546 58.2483 9.32546 55.7852C9.32546 53.3221 7.32873 51.3253 4.86563 51.3253C2.40253 51.3253 0.405792 53.3221 0.405792 55.7852C0.405792 58.2483 2.40253 60.245 4.86563 60.245ZM22.7052 89.2338C22.7052 91.6969 20.7084 93.6936 18.2453 93.6936C15.7822 93.6936 13.7855 91.6969 13.7855 89.2338C13.7855 86.7707 15.7822 84.7739 18.2453 84.7739C20.7084 84.7739 22.7052 86.7707 22.7052 89.2338ZM52.4373 107.817C54.9004 107.817 56.8971 105.82 56.8971 103.357C56.8971 100.894 54.9004 98.8971 52.4373 98.8971C49.9742 98.8971 47.9774 100.894 47.9774 103.357C47.9774 105.82 49.9742 107.817 52.4373 107.817ZM95.5487 90.7205C95.5487 93.1836 93.552 95.1803 91.0889 95.1803C88.6258 95.1803 86.6291 93.1836 86.6291 90.7205C86.6291 88.2574 88.6258 86.2606 91.0889 86.2606C93.552 86.2606 95.5487 88.2574 95.5487 90.7205ZM54.4752 68.5612C61.8667 68.5612 67.8587 62.5692 67.8587 55.1777C67.8587 47.7862 61.8667 41.7942 54.4752 41.7942C47.0837 41.7942 41.0917 47.7862 41.0917 55.1777C41.0917 62.5692 47.0837 68.5612 54.4752 68.5612Z'
      />
    </svg>
  )
}

export function ElevenLabsIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns='http://www.w3.org/2000/svg'
      width='876'
      height='876'
      viewBox='0 0 876 876'
      fill='none'
    >
      <path d='M498 138H618V738H498V138Z' fill='currentColor' />
      <path d='M258 138H378V738H258V138Z' fill='currentColor' />
    </svg>
  )
}

export function LinkupIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns='http://www.w3.org/2000/svg'
      viewBox='0 0 24 24'
      width='24'
      height='24'
      fill='none'
    >
      <path
        d='M20.2 14.1c-.4-.3-1.6-.4-2.9-.2.5-1.4 1.3-3.9.1-5-.6-.5-1.5-.7-2.6-.5-.3 0-.6.1-1 .2-1.1-1.6-2.4-2.5-3.8-2.5-1.6 0-3.1 1-4.1 2.9-1.2 2.1-1.9 5.1-1.9 8.8v.03l.4.3c3-.9 7.5-2.3 10.7-2.9 0 .9.1 1.9.1 2.8v.03l.4.3c.1 0 5.4-1.7 5.3-3.3 0-.2-.1-.5-.3-.7zM19.9 14.7c.03.4-1.7 1.4-4 2.3.5-.7 1-1.6 1.3-2.5 1.4-.1 2.4-.1 2.7.2zM16.4 14.6c-.3.7-.7 1.4-1.2 2-.02-.6-.1-1.2-.2-1.8.4-.1.9-.1 1.4-.2zM16.5 9.4c.8.7.9 2.4.1 5.1-.5.1-1 .1-1.5.2-.3-2-.9-3.8-1.7-5.3.3-.1.6-.2.8-.2.9-.1 1.7.05 2.3.2zM9.5 6.8c1.2 0 2.3.7 3.2 2.1-2.8 1.1-5.9 3.4-8.4 7.8.2-5.1 1.9-9.9 5.2-9.9zM4.7 17c3.4-4.9 6.4-6.8 8.4-7.8.7 1.3 1.2 2.9 1.5 4.8-3.2.6-7.3 1.8-9.9 3z'
        fill='currentColor'
        stroke='currentColor'
        strokeWidth='0.5'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export function JiraIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns='http://www.w3.org/2000/svg'
      viewBox='0 0 30 30'
      width='24'
      height='24'
      focusable='false'
      aria-hidden='true'
    >
      <path
        fill='#1868DB'
        d='M11.034 21.99h-2.22c-3.346 0-5.747-2.05-5.747-5.052h11.932c.619 0 1.019.44 1.019 1.062v12.007c-2.983 0-4.984-2.416-4.984-5.784zm5.893-5.967h-2.219c-3.347 0-5.748-2.013-5.748-5.015h11.933c.618 0 1.055.402 1.055 1.025V24.04c-2.983 0-5.02-2.416-5.02-5.784zm5.93-5.93h-2.219c-3.347 0-5.748-2.05-5.748-5.052h11.933c.618 0 1.018.439 1.018 1.025v12.007c-2.983 0-4.984-2.416-4.984-5.784z'
      />
    </svg>
  )
}

export function LinearIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns='http://www.w3.org/2000/svg'
      fill='currentColor'
      width='200'
      height='200'
      viewBox='0 0 100 100'
    >
      <path
        fill='currentColor'
        d='M1.22541 61.5228c-.2225-.9485.90748-1.5459 1.59638-.857L39.3342 97.1782c.6889.6889.0915 1.8189-.857 1.5964C20.0515 94.4522 5.54779 79.9485 1.22541 61.5228ZM.00189135 46.8891c-.01764375.2833.08887215.5599.28957165.7606L52.3503 99.7085c.2007.2007.4773.3075.7606.2896 2.3692-.1476 4.6938-.46 6.9624-.9259.7645-.157 1.0301-1.0963.4782-1.6481L2.57595 39.4485c-.55186-.5519-1.49117-.2863-1.648174.4782-.465915 2.2686-.77832 4.5932-.92588465 6.9624ZM4.21093 29.7054c-.16649.3738-.08169.8106.20765 1.1l64.77602 64.776c.2894.2894.7262.3742 1.1.2077 1.7861-.7956 3.5171-1.6927 5.1855-2.684.5521-.328.6373-1.0867.1832-1.5407L8.43566 24.3367c-.45409-.4541-1.21271-.3689-1.54074.1832-.99132 1.6684-1.88843 3.3994-2.68399 5.1855ZM12.6587 18.074c-.3701-.3701-.393-.9637-.0443-1.3541C21.7795 6.45931 35.1114 0 49.9519 0 77.5927 0 100 22.4073 100 50.0481c0 14.8405-6.4593 28.1724-16.7199 37.3375-.3903.3487-.984.3258-1.3542-.0443L12.6587 18.074Z'
      />
    </svg>
  )
}

export function TelegramIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns='http://www.w3.org/2000/svg'
      viewBox='0 0 24 24'
      width='24'
      height='24'
      fill='none'
    >
      <circle cx='12' cy='12' r='10' fill='#0088CC' />
      <path
        d='M16.7 8.4c.1-.6-.4-1.1-1-.8l-9.8 4.3c-.4.2-.4.8.1.9l2.1.7c.4.1.8.1 1.1-.2l4.5-3.1c.1-.1.3.1.2.2l-3.2 3.5c-.3.3-.2.8.2 1l3.6 2.3c.4.2.9-.1 1-.5l1.2-7.8Z'
        fill='white'
      />
    </svg>
  )
}

export function ClayIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg {...props} xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 400 400'>
      <path
        xmlns='http://www.w3.org/2000/svg'
        fill='#41B9FD'
        d=' M225.000000,1.000000   C227.042313,1.000000 229.084641,1.000000 231.903046,1.237045   
      C233.981308,1.648251 235.283447,1.974177 236.585678,1.974532   C276.426849,1.985374 316.268005,1.964254 356.349304,2.036658   
      C356.713806,2.242061 356.838165,2.358902 357.013062,2.696568   C357.361633,3.243123 357.659729,3.568854 358.029053,3.919451   
      C358.100250,3.944317 358.064270,4.090822 358.043335,4.397895   C358.300018,5.454089 358.577637,6.203210 358.919647,7.420082   
      C358.919891,27.877140 358.855774,47.866444 358.406097,67.910400   C355.200592,68.111740 352.380737,68.384270 349.560669,68.386124  
       C311.434967,68.411194 273.308777,68.303810 235.184082,68.495499   C229.321579,68.524979 223.465759,69.888084 217.280884,70.633224   
       C216.309952,70.742836 215.664993,70.853645 214.722351,70.824722   C211.834686,71.349052 209.244675,72.013123 206.377716,72.681381   
       C205.743713,72.776283 205.386673,72.866997 204.740524,72.831818   C198.868668,74.719879 193.285919,76.733833 187.518951,78.776100   
       C187.334747,78.804405 187.002716,78.975388 186.619080,78.955429   C183.339905,80.398605 180.444336,81.861732 177.450043,83.356339   
       C177.351318,83.387817 177.199478,83.528885 176.863098,83.476791   C174.940445,84.544197 173.354172,85.663696 171.490601,86.873726   
       C170.873749,87.151909 170.534180,87.339554 169.900208,87.480209   C169.065109,87.950676 168.524414,88.468132 167.772736,89.059799   
       C167.561722,89.134003 167.180191,89.367592 166.874084,89.344360   C166.036011,89.874809 165.504074,90.428497 164.768677,91.071411   
       C164.565247,91.160652 164.195068,91.406326 163.886719,91.361374   C162.847015,91.962418 162.115631,92.608421 161.328308,93.267891   
       C161.272369,93.281357 161.208405,93.377022 160.867157,93.365463   C158.692642,94.907082 156.859375,96.460266 154.780716,98.176086   
       C154.099411,98.731529 153.663513,99.124352 153.029877,99.558502   C152.562164,99.788048 152.505905,100.026695 152.411484,100.477333   
       C151.745850,101.065102 151.332077,101.491318 150.666687,101.980057   C150.244827,102.329651 150.074554,102.616714 149.702332,103.025635  
        C149.247330,103.342041 149.041901,103.578056 148.626404,103.921570   C148.191071,104.281303 148.013428,104.574989 147.660767,104.971512   
        C147.485733,105.074348 147.185501,105.347694 146.854645,105.346924   C145.509140,106.645203 144.494507,107.944252 143.328308,109.398895   
        C143.176773,109.554497 142.944397,109.921532 142.688324,109.990189   C142.263062,110.355179 142.093887,110.651512 141.672485,111.133896  
        C140.733337,112.108200 140.046402,112.896461 139.056610,113.710732   C138.269180,114.554047 137.784592,115.371346 137.263580,116.208557  
        C137.227158,116.228470 137.222885,116.311386 136.910522,116.418571   C134.917343,118.573212 133.067978,120.505791 131.581848,122.685951   
        C117.236908,143.729858 109.909592,167.062012 108.797867,192.458298   C106.874710,236.390839 120.176277,274.069336 154.210175,303.200592   
        C157.543198,306.053497 161.524918,308.148560 165.395065,310.715118   C165.584625,310.834839 166.004089,310.993286 166.112747,311.305908   
        C169.421280,313.480804 172.621170,315.343109 176.067993,317.436401   C196.154831,328.754059 217.585236,333.047546 240.138840,332.968475   
        C276.608368,332.840607 313.078613,332.912872 349.548553,332.932007   C352.369659,332.933472 355.190643,333.181519 358.042847,333.756317   
        C358.105377,352.504913 358.140625,370.812134 358.166443,389.119385   C358.179047,398.047455 357.157593,399.080383 348.101379,399.081543   
        C309.488556,399.086456 270.875702,399.088837 232.262939,399.034698   C229.118195,399.030304 225.976639,398.454163 222.828934,398.396088   
        C219.876633,398.341614 216.918152,398.621979 213.655640,398.750488   C212.946808,398.674561 212.544739,398.603149 211.932861,398.249359  
         C205.139450,396.920532 198.555878,395.874084 191.660583,394.785370   C190.959366,394.590973 190.569855,394.438812 189.976242,394.044556   
         C188.751892,393.631897 187.731628,393.461365 186.520462,393.271667   C186.329559,393.252502 185.966660,393.127686 185.711517,392.875610   
         C179.817810,390.901337 174.179230,389.179169 168.376038,387.422913   C168.211411,387.388824 167.919205,387.222443 167.713623,386.935791   
         C163.177170,384.926636 158.846298,383.204132 154.354828,381.442505   C154.194229,381.403320 153.913010,381.229431 153.720596,380.940063   
         C150.958603,379.507599 148.389023,378.364502 145.862350,377.112976   C145.905273,377.004486 145.834991,377.222992 145.696899,376.907410   
         C143.278778,375.470276 140.998734,374.348724 138.546249,373.152405   C138.373810,373.077606 138.071228,372.854553 137.964508,372.539856   
         C136.491272,371.591217 135.124771,370.957306 133.835419,370.230103   C133.912552,370.136810 133.731659,370.297668 133.638489,369.968719   
         C130.257477,367.557678 126.969620,365.475616 123.676697,363.365906   C123.671616,363.338226 123.618034,363.355438 123.527176,363.037048   
         C122.530983,362.219849 121.625641,361.721039 120.554291,361.141144   C120.388283,361.060028 120.099663,360.829254 120.012115,360.507904   
         C116.854935,357.864441 113.785301,355.542328 110.448624,353.088013   C109.480820,352.261383 108.780060,351.566956 108.005241,350.545807   
         C106.569366,349.183838 105.207550,348.148560 103.618164,346.953125   C102.887856,346.250793 102.385124,345.708649 101.851944,344.819275   
         C99.227608,341.972198 96.633736,339.472412 93.829559,336.814728   C93.315529,336.231140 93.011803,335.805389 92.626633,335.113678   
         C92.241318,334.653351 91.937447,334.458984 91.470352,334.116333   C91.113121,333.744141 90.954285,333.497589 90.815475,332.884094   
         C89.432999,331.125000 88.065689,329.710205 86.750458,328.261658   C86.802551,328.227905 86.679573,328.244812 86.625587,328.004700   
         C86.408173,327.453064 86.154968,327.258301 85.840820,327.092529   C85.869644,327.004852 85.792236,327.175934 85.788193,326.847412   
         C85.086029,325.775726 84.387909,325.032593 83.748154,324.192444   C83.806519,324.095428 83.656967,324.265442 83.677109,323.924805   
         C82.691200,322.493195 81.685143,321.402222 80.701370,320.271667   C80.723648,320.232025 80.638077,320.262756 80.664627,319.911865   
         C79.348137,317.824493 78.005081,316.088074 76.632942,314.335297   C76.603851,314.318970 76.610863,314.252594 76.569603,314.015747   
         C76.383919,313.466492 76.145622,313.265167 75.849998,313.133301   C75.886536,313.091675 75.786301,313.138794 75.787926,312.843567   
         C75.413757,312.136780 75.037964,311.725281 74.650452,311.296570   C74.638725,311.279388 74.605232,311.254669 74.648026,310.925659   
         C74.042847,309.802277 73.394867,309.007935 72.848984,308.101166   C72.951088,307.988739 72.736649,308.207153 72.749344,307.902405   
         C72.247162,307.034119 71.732277,306.470612 71.116684,305.727478   C71.015976,305.547882 70.879890,305.159210 70.904739,304.782593   
         C66.198082,293.805145 61.429871,283.220459 56.753250,272.595459   C54.901436,268.388306 53.253181,264.091522 51.402115,259.538025   
         C51.225922,258.823547 51.159870,258.406525 51.280235,257.681335   C50.130058,252.530197 48.793461,247.687271 47.372990,242.549011   
         C47.250717,241.846664 47.212318,241.439667 47.345688,240.702484   C46.854862,237.196991 46.192276,234.021698 45.439560,230.551788   
         C45.308647,229.849213 45.267864,229.441223 45.399055,228.679535   C45.646000,226.680176 45.810993,225.032898 45.781715,223.389099   
         C45.543224,209.998566 45.243523,196.609085 45.021889,183.218307   C44.965343,179.801880 45.121227,176.381912 45.183868,172.656006   
         C45.260223,171.945328 45.332214,171.542252 45.692661,170.944855   C46.379547,167.156143 46.777977,163.561768 47.196243,159.658173   
         C47.326954,158.952240 47.437832,158.555511 47.816860,157.951569   C48.405701,156.819183 48.802628,155.912750 49.035774,154.966003   
         C53.321564,137.562775 58.709690,120.561356 67.075592,104.614586   C68.431061,102.030846 69.442665,99.266708 70.700943,96.329689   
         C70.963600,95.758194 71.138519,95.442963 71.626465,95.023987   C72.881813,93.185463 73.824142,91.450684 74.833984,89.540924   
         C74.901497,89.365936 75.115746,89.058022 75.414856,88.950439   C76.055374,88.124435 76.396790,87.406006 76.808441,86.516800   
         C76.878685,86.346008 77.099190,86.049721 77.426208,85.968033   C78.773079,84.202591 79.792938,82.518845 80.906425,80.889481   
         C81.000053,80.943871 80.811523,80.846413 81.112083,80.718071   C81.899254,79.675362 82.385872,78.760994 82.980141,77.647797   
         C83.256111,77.193130 83.468399,76.981361 83.972061,76.695953   C84.379341,76.259384 84.539192,75.940521 84.777573,75.467239   
         C84.856110,75.312813 85.091125,75.058212 85.387177,74.957954   C86.071411,74.171829 86.459602,73.485962 86.959831,72.547165   
         C87.574921,71.763893 88.077972,71.233551 88.917511,70.614960   C90.438446,68.934166 91.622894,67.341637 92.892502,65.577087   
         C92.977646,65.405067 93.223930,65.110596 93.540451,65.035034   C94.925735,63.668842 95.994484,62.378204 97.037460,61.053047   
         C97.011688,61.018532 97.086418,61.061367 97.418701,60.997078   C100.387512,58.135143 103.024048,55.337498 105.840828,52.291214   
         C107.274651,50.972633 108.528229,49.902691 110.120842,48.821507   C111.324287,47.898228 112.188705,46.986183 113.028954,46.039188   
         C113.004784,46.004234 113.069771,46.059036 113.418266,46.038719   C115.379044,44.556744 116.991333,43.095085 118.618896,41.600952   
         C118.634186,41.568470 118.705971,41.569565 118.943619,41.531807   C119.496582,41.345333 119.686287,41.099613 119.875092,40.861622   
         C119.999825,40.966347 119.751175,40.750431 120.085175,40.695145   C121.552383,39.660774 122.685600,38.681686 123.971207,37.539024   
         C124.353516,37.180477 124.609665,37.030270 125.248093,36.934944   C127.105858,35.720867 128.607605,34.496674 130.284821,33.157169   
         C130.460281,33.041859 130.850082,32.885620 131.191956,32.879478   C132.720169,31.979248 133.906525,31.085161 135.242615,30.070633   
         C135.392365,29.950191 135.742935,29.792681 136.116943,29.797058   C144.044449,25.665834 151.597931,21.530237 159.443359,17.267967   
         C160.335373,16.929420 160.935471,16.717543 161.932648,16.610218   C166.284805,15.022083 170.239853,13.329394 174.481018,11.497526   
         C175.179947,11.265512 175.592758,11.172676 176.284058,11.232684   C181.045059,9.931384 185.527557,8.477241 190.283020,6.942632   
         C190.929428,6.798172 191.302902,6.734176 192.106628,6.758037   C200.661499,5.630559 208.799301,4.494970 216.903397,3.155535   
         C219.646088,2.702227 222.303574,1.733297 225.000000,1.000000  z'
      />
      <path
        xmlns='http://www.w3.org/2000/svg'
        fill='#CF207F'
        d=' M139.359467,113.684723   C140.046402,112.896461 140.733337,112.108200 141.935272,111.074768   
      C142.614975,110.526917 142.779678,110.224220 142.944397,109.921524   C142.944397,109.921532 143.176773,109.554497 143.635193,109.340279   
      C145.124252,107.866608 146.154877,106.607147 147.185501,105.347694   C147.185501,105.347694 147.485733,105.074348 147.925735,104.915680   
      C148.538528,104.456520 148.711319,104.156021 148.884109,103.855530   C149.041901,103.578056 149.247330,103.342041 149.974884,103.098984   
      C150.636948,103.055161 150.824478,103.059845 151.047058,103.134651   C151.082077,103.204781 151.296890,103.193550 151.296890,103.193550   
      C151.296890,103.193550 151.065384,103.011589 151.060242,102.733826   C151.009506,102.276550 150.963913,102.097046 150.918304,101.917534   
      C151.332077,101.491318 151.745850,101.065102 152.635773,100.460251   C153.111908,100.281609 153.497894,100.049179 153.789368,100.038872   
      C154.772659,99.452271 155.464478,98.875984 156.408234,98.117584   C157.490311,97.320854 158.320465,96.706223 159.411987,96.018272   
      C160.091385,95.613731 160.509415,95.282509 161.005707,94.693756   C161.125443,94.083160 161.166931,93.730095 161.208405,93.377022   
      C161.208405,93.377022 161.272369,93.281357 161.637833,93.283844   C162.733887,92.659668 163.464478,92.032997 164.195068,91.406326   
      C164.195068,91.406326 164.565247,91.160652 165.074371,91.083725   C166.115738,90.460403 166.647964,89.913994 167.180191,89.367592   
      C167.180191,89.367592 167.561722,89.134003 168.067535,89.083694   C169.113785,88.531319 169.654205,88.029266 170.194611,87.527206   
      C170.534180,87.339554 170.873749,87.151909 171.836243,86.913345   C174.039276,85.751251 175.619370,84.640068 177.199478,83.528885   
      C177.199478,83.528885 177.351318,83.387817 177.799438,83.385483   C179.820572,82.883362 181.393585,82.383591 183.170273,81.808777   
      C183.633362,81.599014 183.861649,81.423775 184.373871,81.123398   C185.491287,80.703987 186.293686,80.369202 187.361908,79.991440   
      C188.096588,79.696411 188.565445,79.444366 189.280243,79.140625   C189.689667,79.052353 189.853149,79.015762 190.210281,78.900085   
      C190.651642,78.688210 190.867310,78.515427 191.369507,78.235207   C192.110519,78.067825 192.532990,77.967896 193.244263,77.853729   
      C194.045349,77.588539 194.557632,77.337585 195.404114,77.018097   C196.821823,76.607903 197.905350,76.266235 199.266159,75.907867   
      C200.036407,75.656876 200.529373,75.422592 201.364365,75.106812   C202.827423,74.692017 203.948425,74.358734 205.380356,74.019363   
      C206.468277,73.766235 207.245285,73.519203 208.389984,73.226074   C209.493317,73.091133 210.228912,73.002289 211.290283,72.935577   
      C212.412201,72.683113 213.208344,72.408524 214.267502,72.100060   C214.705307,72.039871 214.880112,72.013565 215.424881,71.999588   
      C217.201248,71.734070 218.607666,71.456200 220.413910,71.153488   C221.880417,71.070969 222.947083,71.013298 224.279190,71.170303   
      C226.068039,70.992416 227.591461,70.599854 229.423401,70.196625   C230.143173,70.169228 230.554443,70.152512 231.313034,70.332619   
      C235.115021,70.382599 238.569687,70.235756 242.491425,70.087082   C280.953430,70.102844 318.948334,70.120430 357.053223,70.529343   
      C357.455536,73.045441 357.992554,75.169182 358.001373,77.295113   C358.070374,93.940338 358.043671,110.585976 358.034363,127.231491   
      C358.030548,134.046967 358.016937,134.057816 351.099701,134.059860   C310.817535,134.071823 270.534180,133.934753 230.254730,134.268967   
      C225.246338,134.310516 220.258575,136.842316 215.230850,138.283905   C215.200439,138.347610 215.065262,138.306870 214.806305,138.286804   
      C214.115921,138.505325 213.684479,138.743896 213.009598,139.115082   C212.583405,139.275208 212.400635,139.302734 211.833679,139.280731   
      C208.407166,140.913559 205.364853,142.595886 202.282257,144.308472   C202.241974,144.338730 202.168381,144.269897 201.973877,144.345428   
      C201.529541,144.568588 201.364868,144.781921 201.061798,145.322937   C200.647766,145.713150 200.457306,145.841385 199.948059,145.977448   
      C197.417572,147.954681 195.205872,149.924103 192.993881,151.942596   C192.993607,151.991669 192.895477,151.990555 192.549149,152.015503   
      C187.409988,154.769379 184.238312,158.680161 183.252487,164.111267   C183.188980,163.991821 183.294250,164.239044 182.950150,164.345627   
      C180.427338,169.367905 177.154861,174.103409 176.308884,179.238663   C174.781265,188.511490 174.320831,198.014923 174.115677,207.437317   
      C173.843521,219.937164 178.269516,231.196472 184.901489,241.604797   C185.796005,243.008667 187.567444,243.853790 188.990707,244.966980   
      C189.048599,244.976334 189.032700,245.092545 189.039658,245.443787   C189.760330,247.068161 190.225784,248.594147 191.225662,249.575775   
      C202.884888,261.022064 217.215424,267.483948 233.244598,267.746521   C272.873535,268.395599 312.520477,268.025818 352.159454,267.873199   
      C356.777344,267.855408 358.164368,269.300385 358.106323,273.876007   C357.865570,292.859802 357.967224,311.847900 357.480347,330.882874   
      C338.906525,330.962463 320.795410,331.052429 302.684601,331.010834   C276.765686,330.951324 250.846970,330.795715 224.637268,330.524200   
      C223.236160,330.268494 222.125992,330.169708 220.602966,330.058136   C219.095612,329.927734 218.001114,329.810120 216.705780,329.546783   
      C216.025055,329.282104 215.545151,329.163147 214.711487,329.008087   C213.887634,328.910431 213.417526,328.848877 212.660461,328.610291   
      C211.246506,328.304504 210.119537,328.175751 208.744629,328.011780   C208.333069,327.943604 208.169434,327.910645 207.938263,327.637787   
      C207.248444,327.303284 206.626129,327.208649 205.594803,327.076263   C204.102722,326.877716 203.019669,326.716858 201.800995,326.447266   
      C201.471100,326.205719 201.260620,326.107544 200.685684,325.968201   C199.212677,325.508331 198.087952,325.124298 196.745544,324.584839   
      C196.008286,324.314789 195.488724,324.200195 194.630951,324.040466   C193.850174,323.890259 193.407623,323.785156 192.841400,323.544250   
      C192.535934,323.239014 192.330688,323.105682 192.067078,322.987274   C192.032166,322.966125 191.968018,322.915680 191.729294,322.721558   
      C190.699036,322.352661 189.907501,322.177887 188.818344,321.917145   C188.322571,321.773010 188.124420,321.714844 187.806183,321.529083   
      C187.508530,321.243896 187.309464,321.121094 186.809235,320.966248   C186.343460,320.853546 186.157333,320.807709 185.820770,320.618958   
      C185.449020,320.300232 185.201187,320.178223 184.579239,320.017242   C183.123337,319.463867 182.015015,319.003296 180.807480,318.445465   
      C180.565079,318.228424 180.407501,318.132172 179.911469,317.900696   C178.706055,317.357391 177.824753,316.972839 176.813736,316.472290   
      C176.496887,316.208344 176.292038,316.091339 175.768234,315.863037   C174.296906,315.078705 173.126801,314.436676 171.834732,313.642029   
      C171.530289,313.298096 171.319397,313.146332 170.800644,312.938660   C170.334427,312.781097 170.147659,312.718903 169.839874,312.529358   
      C169.543640,312.242981 169.349289,312.112366 168.837830,311.854187   C167.694580,311.463196 166.849335,311.228241 166.004089,310.993286   
      C166.004089,310.993286 165.584625,310.834839 165.340561,310.390503   C163.548645,308.481201 162.131165,306.841003 160.433350,305.577545   
      C135.450775,286.986084 120.418205,262.047058 113.761909,231.918289   C110.147652,215.558807 109.790779,198.967697 111.782127,182.339249   
      C113.832611,165.216965 118.597160,148.944382 127.160858,133.886154   C130.497955,128.018265 133.867905,122.169083 137.222885,116.311386   
      C137.222885,116.311386 137.227158,116.228470 137.540863,116.214661   C138.211945,116.106445 138.569351,116.012032 139.062988,115.851028   
      C139.427094,115.546883 139.469406,115.275383 139.372986,114.756676   C139.495758,114.250427 139.475632,113.964195 139.359467,113.684723  z'
      />
      <path
        xmlns='http://www.w3.org/2000/svg'
        fill='#FFC947'
        d=' M200.266830,145.969620   C200.457306,145.841385 200.647766,145.713150 201.270264,145.275589   
      C201.994553,144.826004 202.149918,144.593887 202.168381,144.269897   C202.168381,144.269897 202.241974,144.338730 202.627762,144.274597   
      C206.081650,142.583710 209.149765,140.956970 212.217880,139.330231   C212.400635,139.302734 212.583405,139.275208 213.260132,139.131683   
      C214.191147,138.779388 214.628204,138.543121 215.065262,138.306854   C215.065262,138.306870 215.200439,138.347610 215.615753,138.262543   
      C222.236084,137.117767 228.435684,135.178802 234.646988,135.140549   C276.033936,134.885590 317.423431,135.036758 358.812073,135.055969   
      C358.822845,178.409409 358.833618,221.762833 358.350433,265.618347   C317.222778,266.132172 276.588776,266.228516 235.955917,266.054840   
      C230.533264,266.031647 225.031219,265.015839 219.714111,263.807587   C207.453613,261.021515 197.827393,253.684341 189.032700,245.092545   
      C189.032700,245.092545 189.048599,244.976334 188.932205,244.635071   C178.652054,231.033371 175.024597,215.782471 175.030136,199.385284   
      C175.034317,187.007950 178.389404,175.448639 183.294250,164.239044   C183.294250,164.239044 183.188980,163.991821 183.536774,163.962189   
      C186.888184,159.951889 189.891830,155.971222 192.895477,151.990555   C192.895477,151.990555 192.993607,151.991669 193.307098,151.842606   
      C195.835999,149.785568 198.051407,147.877594 200.266830,145.969620  z'
      />
    </svg>
  )
}

export function MicrosoftIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 23 23' {...props}>
      <path fill='#f3f3f3' d='M0 0h23v23H0z' />
      <path fill='#f35325' d='M1 1h10v10H1z' />
      <path fill='#81bc06' d='M12 1h10v10H12z' />
      <path fill='#05a6f0' d='M1 12h10v10H1z' />
      <path fill='#ffba08' d='M12 12h10v10H12z' />
    </svg>
  )
}

export function MicrosoftTeamsIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg {...props} xmlns='http://www.w3.org/2000/svg' viewBox='0 0 2228.833 2073.333'>
      <path
        fill='#5059C9'
        d='M1554.637,777.5h575.713c54.391,0,98.483,44.092,98.483,98.483c0,0,0,0,0,0v524.398 c0,199.901-162.051,361.952-361.952,361.952h0h-1.711c-199.901,0.028-361.975-162-362.004-361.901c0-0.017,0-0.034,0-0.052V828.971 C1503.167,800.544,1526.211,777.5,1554.637,777.5L1554.637,777.5z'
      />
      <circle fill='#5059C9' cx='1943.75' cy='440.583' r='233.25' />
      <circle fill='#7B83EB' cx='1218.083' cy='336.917' r='336.917' />
      <path
        fill='#7B83EB'
        d='M1667.323,777.5H717.01c-53.743,1.33-96.257,45.931-95.01,99.676v598.105 c-7.505,322.519,247.657,590.16,570.167,598.053c322.51-7.893,577.671-275.534,570.167-598.053V877.176 C1763.579,823.431,1721.066,778.83,1667.323,777.5z'
      />
      <path
        opacity='.1'
        d='M1244,777.5v838.145c-0.258,38.435-23.549,72.964-59.09,87.598 c-11.316,4.787-23.478,7.254-35.765,7.257H667.613c-6.738-17.105-12.958-34.21-18.142-51.833 c-18.144-59.477-27.402-121.307-27.472-183.49V877.02c-1.246-53.659,41.198-98.19,94.855-99.52H1244z'
      />
      <path
        opacity='.2'
        d='M1192.167,777.5v889.978c-0.002,12.287-2.47,24.449-7.257,35.765 c-14.634,35.541-49.163,58.833-87.598,59.09H691.975c-8.812-17.105-17.105-34.21-24.362-51.833 c-7.257-17.623-12.958-34.21-18.142-51.833c-18.144-59.476-27.402-121.307-27.472-183.49V877.02 c-1.246-53.659,41.198-98.19,94.855-99.52H1192.167z'
      />
      <path
        opacity='.2'
        d='M1192.167,777.5v786.312c-0.395,52.223-42.632,94.46-94.855,94.855h-447.84 c-18.144-59.476-27.402-121.307-27.472-183.49V877.02c-1.246-53.659,41.198-98.19,94.855-99.52H1192.167z'
      />
      <path
        opacity='.2'
        d='M1140.333,777.5v786.312c-0.395,52.223-42.632,94.46-94.855,94.855H649.472 c-18.144-59.476-27.402-121.307-27.472-183.49V877.02c-1.246-53.659,41.198-98.19,94.855-99.52H1140.333z'
      />
      <path
        opacity='.1'
        d='M1244,509.522v163.275c-8.812,0.518-17.105,1.037-25.917,1.037 c-8.812,0-17.105-0.518-25.917-1.037c-17.496-1.161-34.848-3.937-51.833-8.293c-104.963-24.857-191.679-98.469-233.25-198.003 c-7.153-16.715-12.706-34.071-16.587-51.833h258.648C1201.449,414.866,1243.801,457.217,1244,509.522z'
      />
      <path
        opacity='.2'
        d='M1192.167,561.355v111.442c-17.496-1.161-34.848-3.937-51.833-8.293 c-104.963-24.857-191.679-98.469-233.25-198.003h190.228C1149.616,466.699,1191.968,509.051,1192.167,561.355z'
      />
      <path
        opacity='.2'
        d='M1192.167,561.355v111.442c-17.496-1.161-34.848-3.937-51.833-8.293 c-104.963-24.857-191.679-98.469-233.25-198.003h190.228C1149.616,466.699,1191.968,509.051,1192.167,561.355z'
      />
      <path
        opacity='.2'
        d='M1140.333,561.355v103.148c-104.963-24.857-191.679-98.469-233.25-198.003 h138.395C1097.783,466.699,1140.134,509.051,1140.333,561.355z'
      />
      <linearGradient
        id='a'
        gradientUnits='userSpaceOnUse'
        x1='198.099'
        y1='1683.0726'
        x2='942.2344'
        y2='394.2607'
        gradientTransform='matrix(1 0 0 -1 0 2075.3333)'
      >
        <stop offset='0' stopColor='#5a62c3' />
        <stop offset='.5' stopColor='#4d55bd' />
        <stop offset='1' stopColor='#3940ab' />
        <stop offset='0' stopColor='#5a62c3' />
        <stop offset='.5' stopColor='#4d55bd' />
        <stop offset='1' stopColor='#3940ab' />
      </linearGradient>
      <path
        fill='url(#a)'
        d='M95.01,466.5h950.312c52.473,0,95.01,42.538,95.01,95.01v950.312c0,52.473-42.538,95.01-95.01,95.01 H95.01c-52.473,0-95.01-42.538-95.01-95.01V561.51C0,509.038,42.538,466.5,95.01,466.5z'
      />
      <path
        fill='#FFF'
        d='M820.211,828.193H630.241v517.297H509.211V828.193H320.123V727.844h500.088V828.193z'
      />
    </svg>
  )
}

export function OutlookIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns='http://www.w3.org/2000/svg'
      version='1.1'
      id='Livello_1'
      x='0px'
      y='0px'
      viewBox='0 0 1831.085 1703.335'
      enableBackground='new 0 0 1831.085 1703.335'
    >
      <path
        fill='#0A2767'
        d='M1831.083,894.25c0.1-14.318-7.298-27.644-19.503-35.131h-0.213l-0.767-0.426l-634.492-375.585  c-2.74-1.851-5.583-3.543-8.517-5.067c-24.498-12.639-53.599-12.639-78.098,0c-2.934,1.525-5.777,3.216-8.517,5.067L446.486,858.693  l-0.766,0.426c-19.392,12.059-25.337,37.556-13.278,56.948c3.553,5.714,8.447,10.474,14.257,13.868l634.492,375.585  c2.749,1.835,5.592,3.527,8.517,5.068c24.498,12.639,53.599,12.639,78.098,0c2.925-1.541,5.767-3.232,8.517-5.068l634.492-375.585  C1823.49,922.545,1831.228,908.923,1831.083,894.25z'
      />
      <path
        fill='#0364B8'
        d='M520.453,643.477h416.38v381.674h-416.38V643.477z M1745.917,255.5V80.908  c1-43.652-33.552-79.862-77.203-80.908H588.204C544.552,1.046,510,37.256,511,80.908V255.5l638.75,170.333L1745.917,255.5z'
      />
      <path fill='#0078D4' d='M511,255.5h425.833v383.25H511V255.5z' />
      <path
        fill='#28A8EA'
        d='M1362.667,255.5H936.833v383.25L1362.667,1022h383.25V638.75L1362.667,255.5z'
      />
      <path fill='#0078D4' d='M936.833,638.75h425.833V1022H936.833V638.75z' />
      <path fill='#0364B8' d='M936.833,1022h425.833v383.25H936.833V1022z' />
      <path fill='#14447D' d='M520.453,1025.151h416.38v346.969h-416.38V1025.151z' />
      <path fill='#0078D4' d='M1362.667,1022h383.25v383.25h-383.25V1022z' />
      <linearGradient
        id='SVGID_1_'
        gradientUnits='userSpaceOnUse'
        x1='1128.4584'
        y1='811.0833'
        x2='1128.4584'
        y2='1.9982'
        gradientTransform='matrix(1 0 0 -1 0 1705.3334)'
      >
        <stop offset='0' style={{ stopColor: '#35B8F1' }} />
        <stop offset='1' style={{ stopColor: '#28A8EA' }} />
      </linearGradient>
      <path
        fill='url(#SVGID_1_)'
        d='M1811.58,927.593l-0.809,0.426l-634.492,356.848c-2.768,1.703-5.578,3.321-8.517,4.769  c-10.777,5.132-22.481,8.029-34.407,8.517l-34.663-20.27c-2.929-1.47-5.773-3.105-8.517-4.897L447.167,906.003h-0.298  l-21.036-11.753v722.384c0.328,48.196,39.653,87.006,87.849,86.7h1230.914c0.724,0,1.363-0.341,2.129-0.341  c10.18-0.651,20.216-2.745,29.808-6.217c4.145-1.756,8.146-3.835,11.966-6.217c2.853-1.618,7.75-5.152,7.75-5.152  c21.814-16.142,34.726-41.635,34.833-68.772V894.25C1831.068,908.067,1823.616,920.807,1811.58,927.593z'
      />
      <path
        opacity='0.5'
        fill='#0A2767'
        enableBackground='new    '
        d='M1797.017,891.397v44.287l-663.448,456.791L446.699,906.301  c0-0.235-0.191-0.426-0.426-0.426l0,0l-63.023-37.899v-31.938l25.976-0.426l54.932,31.512l1.277,0.426l4.684,2.981  c0,0,645.563,368.346,647.267,369.197l24.698,14.478c2.129-0.852,4.258-1.703,6.813-2.555  c1.278-0.852,640.879-360.681,640.879-360.681L1797.017,891.397z'
      />
      <path
        fill='#1490DF'
        d='M1811.58,927.593l-0.809,0.468l-634.492,356.848c-2.768,1.703-5.578,3.321-8.517,4.769  c-24.641,12.038-53.457,12.038-78.098,0c-2.918-1.445-5.76-3.037-8.517-4.769L446.657,928.061l-0.766-0.468  c-12.25-6.642-19.93-19.409-20.057-33.343v722.384c0.305,48.188,39.616,87.004,87.803,86.7c0.001,0,0.002,0,0.004,0h1229.636  c48.188,0.307,87.5-38.509,87.807-86.696c0-0.001,0-0.002,0-0.004V894.25C1831.068,908.067,1823.616,920.807,1811.58,927.593z'
      />
      <path
        opacity='0.1'
        enableBackground='new    '
        d='M1185.52,1279.629l-9.496,5.323c-2.752,1.752-5.595,3.359-8.517,4.812  c-10.462,5.135-21.838,8.146-33.47,8.857l241.405,285.479l421.107,101.476c11.539-8.716,20.717-20.178,26.7-33.343L1185.52,1279.629  z'
      />
      <path
        opacity='0.05'
        enableBackground='new    '
        d='M1228.529,1255.442l-52.505,29.51c-2.752,1.752-5.595,3.359-8.517,4.812  c-10.462,5.135-21.838,8.146-33.47,8.857l113.101,311.838l549.538,74.989c21.649-16.254,34.394-41.743,34.407-68.815v-9.326  L1228.529,1255.442z'
      />
      <path
        fill='#28A8EA'
        d='M514.833,1703.333h1228.316c18.901,0.096,37.335-5.874,52.59-17.033l-697.089-408.331  c-2.929-1.47-5.773-3.105-8.517-4.897L447.125,906.088h-0.298l-20.993-11.838v719.914  C425.786,1663.364,465.632,1703.286,514.833,1703.333C514.832,1703.333,514.832,1703.333,514.833,1703.333z'
      />
      <path
        opacity='0.1'
        enableBackground='new    '
        d='M1022,418.722v908.303c-0.076,31.846-19.44,60.471-48.971,72.392  c-9.148,3.931-19,5.96-28.957,5.962H425.833V383.25H511v-42.583h433.073C987.092,340.83,1021.907,375.702,1022,418.722z'
      />
      <path
        opacity='0.2'
        enableBackground='new    '
        d='M979.417,461.305v908.302c0.107,10.287-2.074,20.469-6.388,29.808  c-11.826,29.149-40.083,48.273-71.54,48.417H425.833V383.25h475.656c12.356-0.124,24.533,2.958,35.344,8.943  C962.937,405.344,979.407,432.076,979.417,461.305z'
      />
      <path
        opacity='0.2'
        enableBackground='new    '
        d='M979.417,461.305v823.136c-0.208,43-34.928,77.853-77.927,78.225H425.833V383.25  h475.656c12.356-0.124,24.533,2.958,35.344,8.943C962.937,405.344,979.407,432.076,979.417,461.305z'
      />
      <path
        opacity='0.2'
        enableBackground='new    '
        d='M936.833,461.305v823.136c-0.046,43.067-34.861,78.015-77.927,78.225H425.833  V383.25h433.072c43.062,0.023,77.951,34.951,77.927,78.013C936.833,461.277,936.833,461.291,936.833,461.305z'
      />
      <linearGradient
        id='SVGID_2_'
        gradientUnits='userSpaceOnUse'
        x1='162.7469'
        y1='1383.0741'
        x2='774.0864'
        y2='324.2592'
        gradientTransform='matrix(1 0 0 -1 0 1705.3334)'
      >
        <stop offset='0' style={{ stopColor: '#1784D9' }} />
        <stop offset='0.5' style={{ stopColor: '#107AD5' }} />
        <stop offset='1' style={{ stopColor: '#0A63C9' }} />
      </linearGradient>
      <path
        fill='url(#SVGID_2_)'
        d='M78.055,383.25h780.723c43.109,0,78.055,34.947,78.055,78.055v780.723  c0,43.109-34.946,78.055-78.055,78.055H78.055c-43.109,0-78.055-34.947-78.055-78.055V461.305  C0,418.197,34.947,383.25,78.055,383.25z'
      />
      <path
        fill='#FFFFFF'
        d='M243.96,710.631c19.238-40.988,50.29-75.289,89.17-98.495c43.057-24.651,92.081-36.94,141.675-35.515  c45.965-0.997,91.321,10.655,131.114,33.683c37.414,22.312,67.547,55.004,86.742,94.109c20.904,43.09,31.322,90.512,30.405,138.396  c1.013,50.043-9.706,99.628-31.299,144.783c-19.652,40.503-50.741,74.36-89.425,97.388c-41.327,23.734-88.367,35.692-136.011,34.578  c-46.947,1.133-93.303-10.651-134.01-34.067c-37.738-22.341-68.249-55.07-87.892-94.28c-21.028-42.467-31.57-89.355-30.745-136.735  C212.808,804.859,223.158,755.686,243.96,710.631z M339.006,941.858c10.257,25.912,27.651,48.385,50.163,64.812  c22.93,16.026,50.387,24.294,78.353,23.591c29.783,1.178,59.14-7.372,83.634-24.358c22.227-16.375,39.164-38.909,48.715-64.812  c10.677-28.928,15.946-59.572,15.543-90.404c0.33-31.127-4.623-62.084-14.649-91.554c-8.855-26.607-25.246-50.069-47.182-67.537  c-23.88-17.79-53.158-26.813-82.91-25.55c-28.572-0.74-56.644,7.593-80.184,23.804c-22.893,16.496-40.617,39.168-51.1,65.365  c-23.255,60.049-23.376,126.595-0.341,186.728L339.006,941.858z'
      />
      <path fill='#50D9FF' d='M1362.667,255.5h383.25v383.25h-383.25V255.5z' />
    </svg>
  )
}

export function MicrosoftExcelIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns='http://www.w3.org/2000/svg'
      version='1.1'
      id='Livello_1'
      x='0px'
      y='0px'
      viewBox='0 0 2289.75 2130'
      enableBackground='new 0 0 2289.75 2130'
    >
      <path
        fill='#185C37'
        d='M1437.75,1011.75L532.5,852v1180.393c0,53.907,43.7,97.607,97.607,97.607l0,0h1562.036  c53.907,0,97.607-43.7,97.607-97.607l0,0V1597.5L1437.75,1011.75z'
      />
      <path
        fill='#21A366'
        d='M1437.75,0H630.107C576.2,0,532.5,43.7,532.5,97.607c0,0,0,0,0,0V532.5l905.25,532.5L1917,1224.75  L2289.75,1065V532.5L1437.75,0z'
      />
      <path fill='#107C41' d='M532.5,532.5h905.25V1065H532.5V532.5z' />
      <path
        opacity='0.1'
        enableBackground='new'
        d='M1180.393,426H532.5v1331.25h647.893c53.834-0.175,97.432-43.773,97.607-97.607  V523.607C1277.825,469.773,1234.227,426.175,1180.393,426z'
      />
      <path
        opacity='0.2'
        enableBackground='new'
        d='M1127.143,479.25H532.5V1810.5h594.643  c53.834-0.175,97.432-43.773,97.607-97.607V576.857C1224.575,523.023,1180.977,479.425,1127.143,479.25z'
      />
      <path
        opacity='0.2'
        enableBackground='new'
        d='M1127.143,479.25H532.5V1704h594.643c53.834-0.175,97.432-43.773,97.607-97.607  V576.857C1224.575,523.023,1180.977,479.425,1127.143,479.25z'
      />
      <path
        opacity='0.2'
        enableBackground='new'
        d='M1073.893,479.25H532.5V1704h541.393c53.834-0.175,97.432-43.773,97.607-97.607  V576.857C1171.325,523.023,1127.727,479.425,1073.893,479.25z'
      />
      <linearGradient
        id='SVGID_1_'
        gradientUnits='userSpaceOnUse'
        x1='203.5132'
        y1='1729.0183'
        x2='967.9868'
        y2='404.9817'
        gradientTransform='matrix(1 0 0 -1 0 2132)'
      >
        <stop offset='0' style={{ stopColor: '#18884F' }} />
        <stop offset='0.5' style={{ stopColor: '#117E43' }} />
        <stop offset='1' style={{ stopColor: '#0B6631' }} />
      </linearGradient>
      <path
        fill='url(#SVGID_1_)'
        d='M97.607,479.25h976.285c53.907,0,97.607,43.7,97.607,97.607v976.285  c0,53.907-43.7,97.607-97.607,97.607H97.607C43.7,1650.75,0,1607.05,0,1553.143V576.857C0,522.95,43.7,479.25,97.607,479.25z'
      />
      <path
        fill='#FFFFFF'
        d='M302.3,1382.264l205.332-318.169L319.5,747.683h151.336l102.666,202.35  c9.479,19.223,15.975,33.494,19.49,42.919h1.331c6.745-15.336,13.845-30.228,21.3-44.677L725.371,747.79h138.929l-192.925,314.548  L869.2,1382.263H721.378L602.79,1160.158c-5.586-9.45-10.326-19.376-14.164-29.66h-1.757c-3.474,10.075-8.083,19.722-13.739,28.755  l-122.102,223.011H302.3z'
      />
      <path
        fill='#33C481'
        d='M2192.143,0H1437.75v532.5h852V97.607C2289.75,43.7,2246.05,0,2192.143,0L2192.143,0z'
      />
      <path fill='#107C41' d='M1437.75,1065h852v532.5h-852V1065z' />
    </svg>
  )
}

export function PackageSearchIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns='http://www.w3.org/2000/svg'
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      stroke='currentColor'
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    >
      <path d='M21 10V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l2-1.14' />
      <path d='m7.5 4.27 9 5.15' />
      <polyline points='3.29 7 12 12 20.71 7' />
      <line x1='12' x2='12' y1='22' y2='12' />
      <circle cx='18.5' cy='15.5' r='2.5' />
      <path d='M20.27 17.27 22 19' />
    </svg>
  )
}
export function HuggingFaceIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='256'
      height='256'
      viewBox='0 0 256 256'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M230.721 172.7C230.183 170.673 229.313 168.75 228.146 167.008C228.396 166.091 228.587 165.159 228.714 164.217C229.543 158.241 227.471 152.77 223.567 148.537C221.452 146.225 219.185 144.698 216.784 143.761C218.36 137.018 219.157 130.117 219.161 123.193C219.161 120.03 218.982 116.932 218.682 113.88C218.526 112.356 218.337 110.836 218.115 109.32C217.428 104.847 216.408 100.431 215.064 96.11C214.183 93.2707 213.164 90.476 212.01 87.736C210.281 83.6782 208.262 79.75 205.969 75.982C204.465 73.475 202.827 71.0508 201.062 68.72C200.197 67.543 199.296 66.3938 198.358 65.274C195.58 61.898 192.561 58.7277 189.325 55.788C188.25 54.7997 187.145 53.8453 186.01 52.926C184.893 51.9943 183.751 51.0927 182.586 50.222C180.241 48.4766 177.818 46.8392 175.324 45.315C161.543 36.945 145.382 32.145 128.109 32.145C77.817 32.145 37.057 72.907 37.057 123.196C37.055 130.208 37.867 137.196 39.477 144.02C37.317 144.958 35.247 146.42 33.327 148.535C29.424 152.766 27.351 158.217 28.18 164.193C28.306 165.142 28.495 166.082 28.747 167.006C27.5811 168.749 26.7117 170.673 26.174 172.7C24.974 177.261 25.369 181.374 26.894 184.978C25.236 189.688 25.65 194.704 27.809 199.065C29.379 202.25 31.626 204.714 34.396 206.916C37.689 209.534 41.811 211.758 46.783 213.892C52.715 216.422 59.956 218.799 63.249 219.671C71.755 221.873 79.911 223.269 88.177 223.337C99.954 223.446 110.096 220.677 117.357 213.59C120.924 214.027 124.515 214.246 128.109 214.244C131.906 214.236 135.699 213.997 139.467 213.529C146.711 220.661 156.892 223.455 168.712 223.343C176.977 223.277 185.133 221.881 193.617 219.676C196.932 218.804 204.17 216.427 210.105 213.897C215.077 211.76 219.199 209.536 222.514 206.922C225.263 204.719 227.508 202.256 229.079 199.071C231.26 194.709 231.652 189.693 230.017 184.983C231.527 181.379 231.92 177.257 230.721 172.7ZM222.281 184.673C223.952 187.844 224.059 191.427 222.585 194.764C220.349 199.821 214.795 203.805 204.008 208.082C197.3 210.742 191.158 212.443 191.104 212.458C182.232 214.759 174.208 215.928 167.262 215.928C155.76 215.928 147.201 212.754 141.773 206.486C132.594 208.05 123.222 208.103 114.026 206.644C108.591 212.808 100.081 215.928 88.676 215.928C81.729 215.928 73.706 214.759 64.833 212.458C64.779 212.443 58.639 210.742 51.929 208.082C41.143 203.805 35.587 199.824 33.352 194.764C31.878 191.427 31.985 187.844 33.656 184.673C33.81 184.378 33.976 184.091 34.153 183.813C33.1516 182.309 32.4799 180.61 32.182 178.827C31.8842 177.045 31.967 175.22 32.425 173.472C33.089 170.949 34.46 168.851 36.322 167.344C35.425 165.87 34.8365 164.23 34.592 162.522C34.056 158.808 35.289 155.1 38.062 152.076C40.222 149.723 43.275 148.428 46.655 148.428H46.745C44.1965 140.259 42.9044 131.75 42.913 123.193C42.913 76.522 80.749 38.683 127.427 38.683C174.104 38.683 211.94 76.518 211.94 123.193C211.947 131.773 210.646 140.304 208.081 148.492C208.489 148.452 208.889 148.432 209.282 148.431C212.662 148.431 215.716 149.726 217.874 152.079C220.647 155.1 221.881 158.811 221.344 162.525C221.1 164.233 220.511 165.873 219.615 167.347C221.477 168.854 222.849 170.952 223.512 173.475C223.97 175.223 224.053 177.048 223.755 178.831C223.458 180.613 222.786 182.312 221.784 183.816C221.961 184.091 222.129 184.378 222.281 184.673Z'
        fill='white'
      />
      <path
        d='M221.784 183.816C222.786 182.312 223.458 180.613 223.756 178.831C224.053 177.048 223.97 175.223 223.512 173.475C222.848 170.952 221.476 168.854 219.615 167.347C220.512 165.873 221.1 164.233 221.344 162.525C221.881 158.811 220.648 155.103 217.874 152.079C215.716 149.726 212.662 148.431 209.282 148.431C208.889 148.431 208.489 148.452 208.081 148.492C210.643 140.304 211.942 131.774 211.933 123.195C211.933 76.5231 174.097 38.6851 127.424 38.6851C80.75 38.6851 42.9099 76.5191 42.9099 123.195C42.9015 131.752 44.1936 140.261 46.742 148.43H46.6519C43.2719 148.43 40.219 149.724 38.06 152.077C35.287 155.098 34.0529 158.81 34.5899 162.523C34.8346 164.231 35.4231 165.872 36.3199 167.346C34.4579 168.852 33.086 170.95 32.422 173.473C31.9642 175.222 31.8817 177.047 32.1799 178.83C32.4781 180.612 33.1501 182.312 34.1519 183.816C33.9739 184.094 33.8099 184.381 33.6549 184.676C31.9849 187.847 31.877 191.43 33.352 194.767C35.588 199.824 41.1419 203.808 51.9289 208.085C58.6359 210.745 64.779 212.446 64.833 212.461C73.705 214.762 81.729 215.931 88.675 215.931C100.081 215.931 108.591 212.811 114.026 206.647C123.222 208.106 132.594 208.052 141.773 206.489C147.201 212.757 155.76 215.931 167.262 215.931C174.208 215.931 182.232 214.762 191.103 212.461C191.158 212.446 197.298 210.745 204.008 208.085C214.795 203.808 220.35 199.824 222.585 194.767C224.059 191.43 223.952 187.847 222.281 184.676C222.129 184.379 221.961 184.091 221.784 183.816ZM110.137 196.997C109.669 197.815 109.168 198.614 108.635 199.391C107.23 201.448 105.382 203.02 103.237 204.188C99.1369 206.424 93.947 207.205 88.675 207.205C80.346 207.205 71.808 205.256 67.023 204.015C66.787 203.954 37.689 195.735 41.373 188.739C41.993 187.562 43.0129 187.092 44.2979 187.092C49.4849 187.092 58.9299 194.816 62.9889 194.816C63.8959 194.816 64.5359 194.43 64.7969 193.488C66.5269 187.284 38.5039 184.676 40.8639 175.692C41.2799 174.102 42.41 173.456 43.998 173.456C50.856 173.455 66.248 185.516 69.467 185.516C69.714 185.516 69.8909 185.443 69.9869 185.291C70.0009 185.268 70.015 185.246 70.028 185.222C71.539 182.727 70.6719 180.913 60.3209 174.573L59.3269 173.968C47.9359 167.074 39.9409 162.925 44.4879 157.975C45.0109 157.404 45.7529 157.151 46.6539 157.151C47.7219 157.151 49.0149 157.508 50.4389 158.108C56.4549 160.645 64.793 167.564 68.276 170.581C68.8239 171.057 69.3683 171.538 69.9089 172.022C69.9089 172.022 74.319 176.608 76.985 176.608C77.599 176.608 78.1199 176.366 78.4729 175.768C80.364 172.58 60.9099 157.838 59.8129 151.755C59.0689 147.634 60.3349 145.546 62.6749 145.546C63.7879 145.546 65.1459 146.02 66.6449 146.971C71.2949 149.922 80.2729 165.35 83.5599 171.352C84.6619 173.363 86.5429 174.213 88.2379 174.213C91.6009 174.213 94.2299 170.87 88.5459 166.622C80.0029 160.23 83.001 149.782 87.078 149.139C87.252 149.111 87.4279 149.097 87.6029 149.097C91.3109 149.097 92.9459 155.486 92.9459 155.486C92.9459 155.486 97.7399 167.524 105.975 175.753C113.447 183.222 114.491 189.351 110.137 196.997ZM136.766 198.407L136.339 198.458L135.611 198.541C135.228 198.581 134.844 198.619 134.459 198.654L134.084 198.688L133.741 198.717L133.255 198.756L132.718 198.795L132.182 198.83L132.063 198.838C131.923 198.846 131.783 198.855 131.641 198.862L131.462 198.872C131.296 198.881 131.13 198.889 130.962 198.896L130.381 198.921L129.854 198.939L129.502 198.949H129.323C129.213 198.949 129.104 198.955 128.994 198.956H128.82C128.71 198.956 128.601 198.956 128.491 198.961L128.043 198.967H127.418C126.927 198.967 126.437 198.962 125.949 198.952L125.553 198.943C125.44 198.943 125.327 198.938 125.216 198.934L124.796 198.922L124.275 198.902L123.805 198.881L123.684 198.876L123.237 198.853C123.112 198.846 122.989 198.84 122.865 198.831L122.576 198.814C122.213 198.791 121.85 198.766 121.487 198.738L121.107 198.707C120.947 198.695 120.787 198.68 120.628 198.666C120.441 198.65 120.254 198.632 120.067 198.614C119.754 198.585 119.441 198.553 119.128 198.519H119.113C123.683 188.324 121.372 178.802 112.137 169.575C106.08 163.526 102.051 154.594 101.215 152.633C99.5229 146.828 95.045 140.375 87.608 140.375C86.979 140.375 86.351 140.425 85.73 140.523C82.472 141.036 79.624 142.911 77.592 145.733C75.396 143.002 73.262 140.831 71.332 139.605C68.422 137.76 65.5179 136.824 62.6889 136.824C59.1579 136.824 56.0019 138.274 53.8019 140.904L53.7459 140.971C53.7039 140.798 53.6639 140.625 53.6229 140.451L53.6179 140.428C53.1992 138.638 52.8477 136.833 52.5639 135.016C52.5639 135.004 52.5639 134.992 52.5579 134.98C52.5359 134.843 52.5159 134.705 52.4949 134.568C52.4334 134.162 52.3757 133.755 52.3219 133.348C52.2979 133.163 52.2719 132.978 52.2489 132.793L52.1809 132.238C52.1589 132.053 52.1409 131.885 52.1209 131.709L52.115 131.665C52.0351 130.945 51.9651 130.225 51.9049 129.503L51.8829 129.226L51.8479 128.754C51.8379 128.625 51.8279 128.495 51.8209 128.365C51.8209 128.334 51.8159 128.304 51.8149 128.275C51.7895 127.913 51.7678 127.55 51.7499 127.187C51.7399 126.998 51.7299 126.81 51.7219 126.62L51.7019 126.124L51.6969 125.974L51.6809 125.517L51.6709 125.128C51.6709 124.973 51.6629 124.818 51.6609 124.663C51.6579 124.508 51.6539 124.338 51.6529 124.174C51.6509 124.01 51.6529 123.848 51.6479 123.685C51.6439 123.521 51.6479 123.358 51.6479 123.195C51.6479 81.3421 85.5789 47.4111 127.436 47.4111C169.292 47.4111 203.222 81.3411 203.222 123.195V124.174C203.222 124.337 203.217 124.501 203.214 124.663C203.214 124.798 203.208 124.931 203.204 125.068C203.204 125.188 203.199 125.309 203.195 125.425C203.195 125.578 203.186 125.731 203.181 125.884V125.896L203.16 126.427C203.153 126.582 203.147 126.738 203.139 126.893L203.134 127.003L203.107 127.499C203.048 128.562 202.967 129.623 202.866 130.683V130.696C202.849 130.87 202.832 131.044 202.813 131.218L202.768 131.629L202.679 132.433L202.628 132.84L202.565 133.319C202.542 133.493 202.519 133.668 202.493 133.841C202.467 134.036 202.438 134.23 202.409 134.424L202.34 134.883L202.258 135.403C202.23 135.576 202.2 135.748 202.168 135.92C202.135 136.093 202.109 136.265 202.079 136.437C202.019 136.781 201.956 137.125 201.89 137.468C201.789 137.981 201.686 138.493 201.58 139.005L201.47 139.512C201.434 139.681 201.395 139.851 201.357 140.02C199.224 137.947 196.399 136.818 193.284 136.818C190.457 136.818 187.55 137.753 184.641 139.598C182.711 140.824 180.578 142.996 178.381 145.726C176.346 142.904 173.498 141.029 170.242 140.516C169.621 140.418 168.993 140.368 168.364 140.368C160.925 140.368 156.45 146.821 154.757 152.626C153.917 154.587 149.887 163.519 143.825 169.577C134.596 178.775 132.268 188.254 136.766 198.407ZM215.007 177.998L214.977 178.087C214.901 178.288 214.813 178.484 214.714 178.674C214.639 178.814 214.558 178.95 214.47 179.082C214.303 179.331 214.12 179.569 213.921 179.793C213.875 179.845 213.831 179.897 213.779 179.948C213.707 180.025 213.634 180.101 213.559 180.175C212.213 181.509 210.161 182.679 207.841 183.752C207.578 183.871 207.311 183.99 207.042 184.11L206.774 184.229C206.595 184.308 206.416 184.386 206.228 184.463C206.049 184.541 205.863 184.619 205.677 184.695L205.119 184.925C203.814 185.462 202.477 185.974 201.173 186.479L200.615 186.696L200.064 186.912C199.697 187.055 199.335 187.198 198.979 187.341L198.448 187.555L197.926 187.768L197.67 187.876C197.499 187.947 197.332 188.018 197.165 188.089C193.328 189.736 190.567 191.411 191.147 193.489C191.163 193.548 191.181 193.604 191.201 193.659C191.253 193.813 191.324 193.958 191.413 194.095C191.465 194.176 191.525 194.253 191.592 194.323C192.274 195.032 193.515 194.92 195.08 194.357C195.3 194.276 195.519 194.192 195.736 194.104L195.872 194.048C196.23 193.896 196.609 193.726 196.996 193.542C197.093 193.496 197.191 193.452 197.289 193.401C199.203 192.465 201.372 191.205 203.524 190.058C204.385 189.593 205.258 189.152 206.142 188.733C208.18 187.774 210.096 187.094 211.636 187.094C212.359 187.094 212.997 187.242 213.529 187.582L213.618 187.641C213.952 187.876 214.232 188.178 214.441 188.528C214.482 188.595 214.522 188.666 214.561 188.739C215.322 190.184 214.685 191.68 213.194 193.147C211.763 194.556 209.537 195.937 207.007 197.215C206.819 197.31 206.631 197.405 206.44 197.498C198.91 201.196 189.049 203.981 188.912 204.016C186.284 204.697 182.526 205.591 178.292 206.26L177.666 206.358L177.563 206.373C177.089 206.445 176.614 206.512 176.138 206.574C175.655 206.639 175.167 206.698 174.676 206.753L174.586 206.763C172.806 206.968 171.019 207.104 169.228 207.169H169.202C168.554 207.192 167.907 207.204 167.259 207.204H166.512C165.524 207.191 164.538 207.146 163.553 207.07C163.53 207.07 163.505 207.07 163.482 207.064C163.129 207.037 162.777 207.004 162.425 206.965C162.06 206.926 161.696 206.882 161.333 206.833C161.094 206.801 160.856 206.765 160.618 206.726C160.376 206.687 160.134 206.647 159.893 206.605L159.564 206.543L159.539 206.538C159.192 206.472 158.847 206.399 158.503 206.319C158.303 206.274 158.104 206.23 157.907 206.176L157.788 206.146C157.69 206.122 157.595 206.096 157.498 206.07L157.445 206.056L157.137 205.966C157.025 205.935 156.913 205.901 156.801 205.868L156.762 205.857L156.471 205.768C156.361 205.734 156.251 205.698 156.142 205.662L155.874 205.573L155.677 205.504C155.487 205.437 155.298 205.368 155.111 205.296L154.933 205.226L154.786 205.168C154.502 205.054 154.22 204.935 153.941 204.81L153.756 204.72L153.725 204.706C153.659 204.675 153.594 204.644 153.528 204.617C153.399 204.555 153.271 204.491 153.144 204.426L153.105 204.407L152.921 204.31C152.594 204.139 152.274 203.957 151.96 203.764L151.788 203.658C151.702 203.605 151.616 203.55 151.532 203.494L151.308 203.346L151.067 203.18L150.923 203.077C150.771 202.969 150.622 202.857 150.476 202.742L150.243 202.563C150.15 202.488 150.058 202.412 149.967 202.335C149.89 202.272 149.815 202.206 149.74 202.14L149.734 202.135C149.653 202.064 149.574 201.993 149.495 201.92C149.417 201.849 149.339 201.777 149.263 201.704L149.254 201.695C149.174 201.619 149.096 201.542 149.019 201.463C148.942 201.385 148.863 201.307 148.788 201.227C148.713 201.148 148.636 201.067 148.562 200.984C148.488 200.902 148.42 200.827 148.35 200.746L148.327 200.719C148.259 200.641 148.192 200.562 148.126 200.481C147.983 200.31 147.844 200.135 147.71 199.956C147.575 199.776 147.443 199.592 147.314 199.405L147.191 199.221C147.027 198.981 146.867 198.739 146.712 198.493C146.596 198.316 146.483 198.138 146.373 197.957C146.302 197.844 146.234 197.73 146.166 197.618L146.138 197.572C146.073 197.462 146.009 197.354 145.947 197.245C145.911 197.186 145.877 197.127 145.845 197.066C145.812 197.004 145.774 196.941 145.739 196.878L145.682 196.779L145.647 196.715C145.58 196.595 145.514 196.474 145.45 196.352C145.42 196.298 145.391 196.244 145.36 196.192L145.271 196.019L145.181 195.848C144.956 195.398 144.743 194.942 144.543 194.48L144.472 194.311C144.426 194.198 144.383 194.086 144.337 193.975C144.315 193.921 144.293 193.868 144.274 193.814C144.167 193.537 144.067 193.257 143.975 192.975C143.942 192.874 143.91 192.775 143.88 192.675C143.808 192.448 143.743 192.219 143.685 191.988C143.614 191.719 143.551 191.448 143.498 191.175C143.487 191.12 143.476 191.065 143.467 191.012C143.415 190.745 143.373 190.476 143.34 190.206C143.332 190.153 143.326 190.1 143.32 190.047L143.303 189.885C143.281 189.673 143.264 189.46 143.254 189.247C143.254 189.193 143.249 189.139 143.247 189.087C143.242 188.981 143.24 188.875 143.239 188.769C143.183 184.496 145.345 180.388 149.968 175.767C158.203 167.54 162.997 155.501 162.997 155.501C162.997 155.501 163.126 154.996 163.394 154.269C163.431 154.168 163.47 154.064 163.514 153.955C163.67 153.548 163.846 153.148 164.041 152.758L164.08 152.683C164.246 152.351 164.428 152.027 164.624 151.712C164.67 151.639 164.714 151.567 164.765 151.494C164.912 151.277 165.067 151.065 165.23 150.86C165.319 150.749 165.416 150.639 165.513 150.532C165.552 150.49 165.59 150.448 165.631 150.408C166.108 149.915 166.653 149.513 167.27 149.299L167.348 149.273C167.4 149.256 167.452 149.24 167.505 149.225C167.566 149.209 167.627 149.195 167.69 149.182L167.719 149.176C167.849 149.15 167.981 149.133 168.114 149.124H168.125C168.194 149.124 168.264 149.117 168.335 149.117C168.424 149.117 168.507 149.117 168.594 149.126C168.684 149.134 168.773 149.144 168.863 149.158C169.605 149.276 170.311 149.718 170.919 150.4C171.15 150.66 171.358 150.94 171.54 151.236C171.66 151.428 171.773 151.631 171.88 151.845C171.923 151.934 171.964 152.016 172.004 152.104C172.108 152.33 172.202 152.56 172.284 152.795C172.479 153.345 172.626 153.911 172.723 154.487C172.807 154.992 172.857 155.502 172.873 156.013C172.881 156.286 172.881 156.563 172.873 156.842C172.819 158.14 172.553 159.421 172.086 160.634C172.044 160.745 171.997 160.857 171.952 160.969C171.86 161.195 171.759 161.417 171.65 161.634C171.569 161.799 171.484 161.965 171.392 162.13C171.332 162.24 171.269 162.35 171.206 162.46C171.045 162.734 170.871 163.006 170.684 163.277L170.571 163.439C170.129 164.055 169.637 164.633 169.099 165.167C168.569 165.698 168.001 166.189 167.4 166.637C166.798 167.083 166.233 167.577 165.711 168.114C164.208 169.691 163.858 171.083 164.196 172.138C164.25 172.304 164.321 172.465 164.407 172.617C164.508 172.791 164.628 172.951 164.764 173.097L164.817 173.152L164.871 173.206C164.925 173.258 164.982 173.309 165.043 173.359L165.103 173.407C165.248 173.519 165.402 173.619 165.563 173.707C165.61 173.732 165.652 173.757 165.705 173.781C165.879 173.866 166.058 173.939 166.242 173.998C166.293 174.015 166.344 174.03 166.396 174.046L166.461 174.063L166.551 174.087L166.628 174.106L166.712 174.124L166.795 174.141L166.874 174.154C166.932 174.164 166.992 174.174 167.052 174.181L167.109 174.19L167.213 174.2L167.277 174.207L167.382 174.214H167.444L167.554 174.22H167.9L167.999 174.214L168.113 174.207L168.252 174.194L168.382 174.179C168.412 174.179 168.442 174.171 168.472 174.165C168.872 174.107 169.264 174.001 169.639 173.849L169.798 173.782C169.887 173.743 169.977 173.702 170.059 173.658C170.235 173.57 170.406 173.47 170.57 173.361C170.799 173.211 171.015 173.043 171.217 172.858C171.265 172.815 171.312 172.769 171.358 172.725C171.381 172.703 171.403 172.682 171.425 172.658C171.469 172.613 171.514 172.569 171.558 172.52C171.878 172.168 172.155 171.78 172.383 171.363C174.34 167.804 176.391 164.298 178.534 160.849L178.828 160.378L179.125 159.907C179.273 159.668 179.423 159.433 179.572 159.199L179.722 158.965C180.22 158.185 180.726 157.41 181.241 156.641L181.546 156.185C182.158 155.278 182.768 154.396 183.373 153.558L183.674 153.143C184.332 152.236 185.017 151.348 185.728 150.482L186.01 150.144C186.057 150.088 186.1 150.032 186.151 149.978C186.244 149.868 186.337 149.761 186.428 149.657C186.474 149.604 186.517 149.552 186.566 149.5L186.834 149.198L186.968 149.051C187.103 148.906 187.235 148.767 187.365 148.634C187.455 148.544 187.538 148.455 187.624 148.371C188.131 147.853 188.69 147.388 189.293 146.985L189.433 146.895C189.567 146.805 189.706 146.721 189.848 146.645C192.212 145.303 194.169 145.204 195.296 146.331C195.978 147.013 196.356 148.144 196.335 149.718C196.335 149.787 196.335 149.857 196.33 149.929V150.006C196.33 150.078 196.324 150.15 196.318 150.223C196.318 150.313 196.308 150.402 196.299 150.492C196.29 150.581 196.285 150.649 196.276 150.729C196.276 150.751 196.272 150.774 196.268 150.798C196.262 150.867 196.253 150.938 196.243 151.009C196.243 151.03 196.243 151.052 196.235 151.074C196.224 151.169 196.21 151.263 196.194 151.357C196.183 151.447 196.168 151.531 196.152 151.619L196.126 151.768C196.1 151.91 196.067 152.05 196.026 152.188C195.948 152.447 195.854 152.7 195.743 152.946C195.588 153.284 195.417 153.613 195.229 153.933C195.125 154.111 195.018 154.286 194.907 154.459C194.793 154.638 194.673 154.819 194.549 155.002C194.233 155.454 193.905 155.897 193.564 156.33L193.408 156.527C192.852 157.22 192.278 157.899 191.686 158.562L191.499 158.772C191.247 159.053 190.991 159.336 190.729 159.62L190.532 159.834C190.401 159.977 190.264 160.12 190.132 160.264C190.001 160.407 189.864 160.552 189.726 160.697L189.315 161.13L188.898 161.566L188.478 162.002C188.196 162.294 187.913 162.586 187.628 162.878C183.573 167.037 179.301 171.182 177.855 173.766C177.758 173.934 177.671 174.108 177.593 174.285C177.387 174.755 177.301 175.157 177.36 175.482C177.379 175.589 177.416 175.691 177.471 175.785C177.552 175.926 177.651 176.056 177.766 176.172C177.819 176.224 177.875 176.272 177.934 176.316C178.232 176.528 178.591 176.637 178.957 176.627H179.071L179.188 176.618L179.305 176.605L179.402 176.591C179.415 176.589 179.429 176.587 179.442 176.583L179.531 176.566L179.554 176.561L179.653 176.54L179.688 176.531C179.723 176.522 179.757 176.513 179.792 176.503C179.827 176.493 179.875 176.48 179.917 176.466C180.093 176.413 180.265 176.35 180.434 176.278C180.523 176.242 180.61 176.203 180.696 176.161C180.741 176.141 180.786 176.12 180.828 176.098L180.962 176.032C181.282 175.866 181.594 175.685 181.898 175.491L182.031 175.401C182.076 175.373 182.121 175.344 182.164 175.312L182.297 175.223L182.368 175.174L182.56 175.039C182.739 174.916 182.906 174.789 183.075 174.66L183.09 174.648L183.359 174.44C183.726 174.15 184.074 173.858 184.39 173.583L184.6 173.399L184.619 173.381L184.729 173.284C184.987 173.052 185.217 172.836 185.408 172.658L185.487 172.581C185.556 172.516 185.619 172.455 185.676 172.403L185.788 172.292L185.828 172.253L185.839 172.242L185.956 172.125L186.03 172.048L186.039 172.041L186.074 172.009L186.118 171.969L186.132 171.956L186.169 171.922L186.373 171.743L186.487 171.641C186.548 171.588 186.607 171.534 186.666 171.479L186.802 171.358C186.827 171.338 186.851 171.316 186.876 171.294L187.019 171.169L187.229 170.984L187.341 170.887C187.776 170.509 188.305 170.052 188.913 169.537L189.162 169.326L189.573 168.981L189.994 168.63C190.544 168.173 191.136 167.688 191.762 167.185L192.173 166.855C192.523 166.576 192.882 166.292 193.246 166.006C193.393 165.891 193.542 165.776 193.694 165.662C194.066 165.373 194.44 165.086 194.817 164.803C195.675 164.155 196.56 163.506 197.456 162.874L197.84 162.606C198.109 162.421 198.377 162.235 198.645 162.054L198.888 161.89C199.367 161.565 199.853 161.248 200.343 160.939L200.586 160.786L200.827 160.636C201.069 160.486 201.309 160.339 201.548 160.196L201.787 160.053L202.265 159.775L202.734 159.506L202.829 159.454L203.2 159.25C203.355 159.166 203.509 159.085 203.663 159.006L203.892 158.888L204.115 158.776C204.193 158.739 204.27 158.7 204.346 158.663C204.848 158.415 205.36 158.187 205.88 157.979C206.021 157.919 206.161 157.865 206.3 157.818L206.71 157.674C206.833 157.633 206.953 157.594 207.068 157.559L207.108 157.547C207.17 157.527 207.232 157.509 207.293 157.493L207.311 157.488C207.439 157.451 207.566 157.419 207.691 157.389H207.7C208.054 157.304 208.414 157.243 208.777 157.206C208.944 157.189 209.111 157.18 209.279 157.181H209.363C209.475 157.181 209.583 157.188 209.69 157.199C209.739 157.199 209.788 157.209 209.836 157.215H209.856C209.904 157.221 209.952 157.228 210 157.239C210.047 157.248 210.095 157.256 210.141 157.267H210.156C210.203 157.277 210.245 157.289 210.294 157.303C210.548 157.374 210.79 157.484 211.012 157.628C211.121 157.699 211.223 157.779 211.317 157.868L211.344 157.894C211.362 157.91 211.379 157.927 211.395 157.944L211.444 157.997C211.846 158.418 212.178 158.901 212.428 159.427L212.466 159.517C212.551 159.717 212.618 159.924 212.666 160.135C212.808 160.781 212.753 161.455 212.508 162.07C212.415 162.318 212.302 162.557 212.169 162.785C211.858 163.309 211.489 163.796 211.07 164.237L210.981 164.332C210.848 164.472 210.71 164.612 210.565 164.752C210.501 164.815 210.434 164.877 210.367 164.94L210.162 165.129L210.055 165.224C209.797 165.454 209.532 165.677 209.263 165.893C209.1 166.025 208.936 166.154 208.77 166.281C208.184 166.729 207.587 167.161 206.979 167.578C206.612 167.83 206.242 168.077 205.869 168.321C204.95 168.924 204.021 169.512 203.083 170.084C201.115 171.294 198.934 172.588 196.609 173.995L196.007 174.36C195.348 174.762 194.726 175.146 194.14 175.512L193.845 175.697L193.287 176.055C192.917 176.292 192.548 176.531 192.179 176.77L191.882 176.966C191.737 177.06 191.593 177.156 191.449 177.252L191.308 177.342L190.876 177.633L190.647 177.79L190.379 177.976L190.13 178.149C189.713 178.444 189.325 178.725 188.968 178.992L188.834 179.094C188.624 179.253 188.416 179.415 188.211 179.58C187.902 179.829 187.62 180.067 187.367 180.296L187.243 180.409C187.172 180.474 187.102 180.539 187.035 180.603C186.989 180.648 186.946 180.693 186.898 180.736L186.834 180.8C186.691 180.944 186.551 181.091 186.416 181.242L186.35 181.318C186.203 181.488 186.075 181.651 185.963 181.81L185.913 181.881C185.825 182.009 185.744 182.141 185.671 182.277C185.652 182.311 185.635 182.345 185.618 182.379L185.569 182.481L185.536 182.555L185.515 182.605L185.498 182.65L185.475 182.711C185.413 182.88 185.37 183.056 185.345 183.234L185.337 183.296L185.331 183.354V183.669C185.331 183.695 185.331 183.721 185.338 183.749L185.343 183.797C185.343 183.823 185.349 183.848 185.353 183.876C185.357 183.902 185.364 183.949 185.372 183.986V183.991C185.379 184.026 185.386 184.06 185.395 184.095C185.404 184.13 185.413 184.17 185.424 184.206C185.443 184.277 185.467 184.347 185.492 184.417C185.508 184.459 185.523 184.5 185.54 184.541C185.54 184.549 185.546 184.558 185.55 184.566L185.586 184.647L185.636 184.758C185.69 184.873 185.749 184.985 185.813 185.094L185.879 185.208L185.947 185.322C185.959 185.341 185.973 185.359 185.988 185.376L186.01 185.399L186.035 185.422L186.061 185.442C186.099 185.469 186.14 185.49 186.183 185.505C186.206 185.513 186.23 185.519 186.254 185.525C186.831 185.655 188.017 185.178 189.593 184.346C189.682 184.298 189.78 184.248 189.875 184.196L190.355 183.934L190.589 183.804C190.756 183.715 190.926 183.614 191.1 183.515L191.417 183.336C193.5 182.137 195.988 180.597 198.56 179.093C198.801 178.952 199.043 178.811 199.285 178.672L199.771 178.361C200.335 178.038 200.902 177.719 201.471 177.404C202.188 177.01 202.91 176.626 203.639 176.254L204.115 176.013C204.431 175.857 204.744 175.705 205.053 175.557C205.651 175.273 206.256 175.003 206.868 174.748L207.203 174.612L207.243 174.596C209.018 173.893 210.627 173.459 211.929 173.459C212.21 173.456 212.492 173.48 212.769 173.528H212.778C212.867 173.544 212.948 173.562 213.031 173.582H213.046C213.259 173.636 213.466 173.713 213.662 173.812C213.937 173.954 214.184 174.143 214.393 174.371C214.489 174.477 214.574 174.592 214.649 174.714C214.789 174.929 214.899 175.162 214.978 175.406C215.01 175.501 215.038 175.594 215.067 175.693C215.278 176.45 215.257 177.253 215.007 177.998Z'
        fill='#FF9D00'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M203.21 123.685V123.194C203.21 81.34 169.292 47.411 127.435 47.411C85.5791 47.411 51.648 81.342 51.648 123.194V123.358C51.646 123.467 51.645 123.576 51.648 123.685C51.6529 123.848 51.6546 124.011 51.653 124.174L51.6581 124.534L51.661 124.663C51.661 124.723 51.6631 124.782 51.6651 124.842C51.6681 124.937 51.67 125.033 51.67 125.128L51.681 125.517L51.697 125.974L51.702 126.124L51.722 126.597V126.62C51.73 126.805 51.7401 126.989 51.7491 127.173L51.75 127.187C51.76 127.375 51.7701 127.564 51.7821 127.753C51.7921 127.927 51.802 128.101 51.815 128.275L51.8171 128.306C51.8258 128.455 51.8358 128.605 51.847 128.754L51.85 128.794L51.883 129.226L51.8861 129.254C51.8921 129.338 51.898 129.422 51.906 129.503C51.9658 130.224 52.0355 130.945 52.1151 131.664L52.12 131.709L52.181 132.238L52.2491 132.793L52.299 133.17L52.322 133.347C52.3753 133.755 52.433 134.162 52.495 134.568L52.4991 134.595L52.558 134.979C52.8435 136.808 53.1971 138.626 53.618 140.429L53.6231 140.451L53.655 140.586L53.746 140.971L53.802 140.904C56.002 138.274 59.158 136.824 62.689 136.824C65.519 136.824 68.4221 137.76 71.3321 139.605C73.2621 140.831 75.3961 143.002 77.5921 145.733C79.6241 142.911 82.4721 141.035 85.7301 140.523C86.3513 140.425 86.9792 140.376 87.6081 140.375C95.0441 140.375 99.523 146.828 101.215 152.633C102.051 154.594 106.08 163.526 112.156 169.568C121.392 178.795 123.703 188.316 119.132 198.511H119.148C119.459 198.546 119.772 198.578 120.087 198.607C120.274 198.625 120.46 198.643 120.648 198.659L120.714 198.665L121.127 198.7L121.507 198.73C121.869 198.758 122.232 198.784 122.596 198.807L122.885 198.824L123.114 198.838L123.256 198.846L123.703 198.869L123.825 198.874L124.294 198.895L124.816 198.915L125.235 198.927L125.305 198.929C125.394 198.933 125.483 198.936 125.572 198.936L125.668 198.939C126.258 198.953 126.847 198.96 127.437 198.959H128.063L128.51 198.954C128.62 198.949 128.729 198.949 128.84 198.949H129.014L129.165 198.945C129.224 198.943 129.283 198.941 129.343 198.941H129.522L129.873 198.932L130.401 198.914L130.982 198.888C131.15 198.882 131.316 198.873 131.482 198.865L131.661 198.854L131.927 198.84L132.083 198.831L132.201 198.823L132.738 198.788L133.274 198.749L133.761 198.71L134.103 198.681L134.479 198.647C135.107 198.591 135.733 198.525 136.359 198.45L136.786 198.399C132.287 188.247 134.616 178.767 143.813 169.577C149.876 163.519 153.905 154.587 154.745 152.625C156.438 146.821 160.914 140.368 168.352 140.368C168.981 140.368 169.61 140.418 170.231 140.516C173.486 141.028 176.334 142.904 178.369 145.726C180.566 142.996 182.699 140.823 184.63 139.597C187.539 137.753 190.445 136.817 193.272 136.817C196.388 136.817 199.212 137.947 201.345 140.02C201.384 139.851 201.422 139.682 201.459 139.512L201.568 139.006C201.607 138.821 201.646 138.636 201.683 138.451C201.749 138.124 201.815 137.797 201.878 137.467C201.944 137.125 202.007 136.781 202.067 136.437L202.098 136.251C202.117 136.141 202.135 136.031 202.156 135.92C202.19 135.748 202.218 135.576 202.246 135.402L202.257 135.336L202.328 134.883L202.398 134.424V134.42C202.449 134.081 202.497 133.742 202.542 133.403L202.553 133.319L202.616 132.841L202.667 132.433L202.757 131.629L202.792 131.306L202.801 131.218C202.82 131.044 202.838 130.87 202.854 130.696V130.682C202.867 130.544 202.881 130.405 202.893 130.266C202.964 129.478 203.024 128.686 203.072 127.891C203.081 127.761 203.088 127.63 203.096 127.499V127.493L203.122 127.002L203.128 126.892C203.144 126.56 203.158 126.228 203.169 125.896V125.884L203.174 125.754C203.179 125.645 203.183 125.535 203.183 125.425L203.185 125.381C203.189 125.278 203.193 125.172 203.193 125.067L203.196 124.977C203.199 124.872 203.202 124.768 203.202 124.663L203.204 124.574C203.207 124.441 203.21 124.307 203.21 124.174V123.685ZM108.638 199.391C114.64 190.59 114.214 183.984 105.98 175.754C97.7441 167.523 92.951 155.487 92.951 155.487C92.951 155.487 91.1621 148.496 87.0821 149.138C83.0021 149.78 80.0091 160.227 88.5521 166.622C97.0941 173.017 86.8521 177.353 83.5641 171.352C80.2761 165.35 71.299 149.923 66.645 146.972C61.991 144.021 58.718 145.675 59.815 151.757C60.36 154.776 65.4281 159.929 70.1631 164.743C74.9671 169.627 79.428 174.163 78.474 175.768C76.581 178.955 69.9141 172.023 69.9141 172.023C69.9141 172.023 49.038 153.025 44.494 157.976C40.304 162.539 46.765 166.418 56.7211 172.397C57.5671 172.905 58.4391 173.429 59.3321 173.969C70.7231 180.865 71.609 182.684 69.992 185.293C69.395 186.257 65.582 183.968 60.892 181.153C52.897 176.352 42.3551 170.023 40.8661 175.688C39.5781 180.591 47.334 183.595 54.368 186.32C60.228 188.59 65.5881 190.666 64.7991 193.484C63.9821 196.406 59.5531 193.969 54.7121 191.305C49.2771 188.314 43.3221 185.038 41.3731 188.735C37.6901 195.725 66.7831 203.954 67.0231 204.015C76.4231 206.453 100.295 211.619 108.638 199.391ZM147.303 199.391C141.301 190.59 141.727 183.984 149.962 175.754C158.197 167.523 162.99 155.487 162.99 155.487C162.99 155.487 164.779 148.496 168.859 149.138C172.939 149.78 175.932 160.227 167.39 166.622C158.847 173.017 169.089 177.353 172.377 171.352C175.666 165.35 184.637 149.923 189.291 146.972C193.945 144.021 197.22 145.675 196.122 151.757C195.578 154.776 190.509 159.929 185.774 164.744C180.97 169.628 176.509 174.163 177.462 175.768C179.355 178.955 186.027 172.019 186.027 172.019C186.027 172.019 206.902 153.022 211.448 157.973C215.637 162.535 209.176 166.415 199.219 172.394C198.348 172.917 197.478 173.441 196.609 173.966C185.218 180.862 184.332 182.681 185.948 185.289C186.546 186.254 190.359 183.964 195.048 181.149C203.044 176.349 213.586 170.019 215.075 175.685C216.364 180.588 208.607 183.592 201.573 186.317C195.713 188.587 190.353 190.663 191.141 193.481C191.957 196.402 196.385 193.965 201.225 191.301C206.66 188.31 212.616 185.032 214.564 188.732C218.248 195.726 189.15 203.947 188.915 204.007C179.515 206.453 155.643 211.619 147.303 199.391Z'
        fill='#FFD21E'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M152.047 102.567C153.229 102.985 154.108 104.257 154.944 105.468C156.074 107.104 157.126 108.627 158.74 107.769C160.644 106.756 162.205 105.202 163.225 103.302C164.246 101.402 164.681 99.2427 164.475 97.096C164.321 95.4908 163.813 93.9398 162.987 92.5548C162.161 91.1697 161.038 89.985 159.7 89.0862C158.361 88.1874 156.839 87.5968 155.245 87.3569C153.65 87.117 152.022 87.2339 150.478 87.699C148.934 88.1639 147.513 88.9653 146.316 90.0455C145.119 91.1257 144.176 92.4578 143.556 93.946C142.936 95.4342 142.653 97.0415 142.728 98.652C142.804 100.263 143.235 101.836 143.992 103.26C144.74 104.667 146.4 104.003 148.152 103.302C149.525 102.753 150.956 102.181 152.047 102.567ZM100.672 102.567C99.49 102.985 98.611 104.258 97.775 105.468C96.645 107.105 95.592 108.627 93.979 107.769C91.5845 106.501 89.7482 104.386 88.8278 101.838C87.9075 99.2895 87.9692 96.4896 89.0008 93.9841C90.0324 91.4786 91.9601 89.4471 94.408 88.2855C96.856 87.1239 99.6488 86.9156 102.242 87.701C104.307 88.3228 106.141 89.5427 107.513 91.2065C108.885 92.8704 109.732 94.9035 109.949 97.049C110.165 99.1945 109.74 101.356 108.728 103.26C107.979 104.667 106.319 104.003 104.567 103.303C103.193 102.753 101.764 102.181 100.672 102.567ZM144.099 149.318C152.242 142.903 155.233 132.429 155.233 125.977C155.233 120.877 151.802 122.482 146.309 125.202L145.999 125.355C140.957 127.852 134.245 131.177 126.877 131.177C119.508 131.177 112.796 127.852 107.755 125.354C102.084 122.545 98.527 120.783 98.527 125.978C98.527 132.634 101.709 143.563 110.443 149.912C111.596 147.573 113.219 145.497 115.211 143.813C117.202 142.129 119.52 140.874 122.018 140.126C122.89 139.866 123.788 141.367 124.707 142.904C125.594 144.386 126.501 145.902 127.423 145.902C128.406 145.902 129.371 144.408 130.314 142.95C131.299 141.425 132.26 139.94 133.189 140.237C137.864 141.738 141.775 144.993 144.099 149.318Z'
        fill='#32343D'
      />
      <path
        d='M144.097 149.317C139.856 152.659 134.219 154.9 126.878 154.9C119.981 154.9 114.587 152.922 110.443 149.911C111.596 147.572 113.219 145.495 115.211 143.812C117.202 142.128 119.52 140.873 122.018 140.125C123.73 139.614 125.545 145.901 127.423 145.901C129.433 145.901 131.37 139.655 133.189 140.236C137.863 141.738 141.773 144.993 144.097 149.317Z'
        fill='#FF323D'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M81.2 111.64C80.2312 112.288 79.1173 112.687 77.9572 112.801C76.7971 112.916 75.6267 112.742 74.55 112.295C73.6893 111.94 72.9072 111.418 72.2488 110.759C71.5903 110.101 71.0684 109.319 70.713 108.458C70.267 107.381 70.0935 106.211 70.2082 105.051C70.3228 103.891 70.7219 102.777 71.37 101.808C72.1488 100.642 73.2558 99.7333 74.5512 99.1967C75.8466 98.6601 77.272 98.5197 78.6471 98.7935C80.0223 99.0672 81.2853 99.7427 82.2764 100.734C83.2675 101.726 83.9422 102.99 84.215 104.365C84.4883 105.74 84.3477 107.165 83.8113 108.46C83.2748 109.755 82.3654 110.861 81.2 111.64ZM182.613 111.64C181.644 112.288 180.53 112.687 179.37 112.801C178.209 112.916 177.039 112.742 175.962 112.295C175.101 111.939 174.319 111.418 173.661 110.759C173.003 110.101 172.481 109.319 172.125 108.458C171.68 107.381 171.507 106.211 171.621 105.051C171.736 103.891 172.135 102.777 172.782 101.808C173.364 100.936 174.133 100.205 175.032 99.6658C175.931 99.1269 176.938 98.7942 177.981 98.6917C179.025 98.5891 180.078 98.7193 181.064 99.0728C182.051 99.4264 182.947 99.9944 183.688 100.736C184.68 101.727 185.355 102.99 185.628 104.365C185.902 105.74 185.761 107.165 185.224 108.46C184.687 109.755 183.779 110.861 182.613 111.64Z'
        fill='#FFAD03'
      />
    </svg>
  )
}

export const ResponseIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    fill='currentColor'
    width='800px'
    height='800px'
    viewBox='0 0 1920 1920'
    xmlns='http://www.w3.org/2000/svg'
  >
    <path
      d='m1030.975 188 81.249 81.249-429.228 429.228h300.747c516.223 0 936.257 420.034 936.257 936.257v98.028h-114.92v-98.028c0-452.901-368.436-821.337-821.337-821.337H682.996l429.228 429.229-81.25 81.248-567.936-567.937L1030.975 188Zm-463.038.011 81.249 81.25-486.688 486.688 486.688 486.688-81.249 81.249L0 755.949 567.937 188.01Z'
      fillRule='evenodd'
    />
  </svg>
)

export const AnthropicIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    fill='currentColor'
    fillRule='evenodd'
    height='1em'
    viewBox='0 0 24 24'
    width='1em'
    xmlns='http://www.w3.org/2000/svg'
  >
    <title>Anthropic</title>
    <path d='M13.827 3.52h3.603L24 20h-3.603l-6.57-16.48zm-7.258 0h3.767L16.906 20h-3.674l-1.343-3.461H5.017l-1.344 3.46H0L6.57 3.522zm4.132 9.959L8.453 7.687 6.205 13.48H10.7z' />
  </svg>
)

export const AzureIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    width='18'
    height='18'
    viewBox='0 0 18 18'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
  >
    <path
      d='M5.33492 1.37491C5.44717 1.04229 5.75909 0.818359 6.11014 0.818359H11.25L5.91513 16.6255C5.80287 16.9581 5.49095 17.182 5.13991 17.182H1.13968C0.579936 17.182 0.185466 16.6325 0.364461 16.1022L5.33492 1.37491Z'
      fill='url(#paint0_linear_6102_134469)'
    />
    <path
      d='M13.5517 11.4546H5.45126C5.1109 11.4546 4.94657 11.8715 5.19539 12.1037L10.4005 16.9618C10.552 17.1032 10.7515 17.1819 10.9587 17.1819H15.5453L13.5517 11.4546Z'
      fill='#0078D4'
    />
    <path
      d='M6.11014 0.818359C5.75909 0.818359 5.44717 1.04229 5.33492 1.37491L0.364461 16.1022C0.185466 16.6325 0.579936 17.182 1.13968 17.182H5.13991C5.49095 17.182 5.80287 16.9581 5.91513 16.6255L6.90327 13.6976L10.4005 16.9617C10.552 17.1032 10.7515 17.1818 10.9588 17.1818H15.5454L13.5517 11.4545H7.66032L11.25 0.818359H6.11014Z'
      fill='url(#paint1_linear_6102_134469)'
    />
    <path
      d='M12.665 1.37478C12.5528 1.04217 12.2409 0.818237 11.8898 0.818237H6.13629H6.16254C6.51358 0.818237 6.82551 1.04217 6.93776 1.37478L11.9082 16.1021C12.0872 16.6324 11.6927 17.1819 11.133 17.1819H11.0454H16.8603C17.42 17.1819 17.8145 16.6324 17.6355 16.1021L12.665 1.37478Z'
      fill='url(#paint2_linear_6102_134469)'
    />
    <defs>
      <linearGradient
        id='paint0_linear_6102_134469'
        x1='6.07512'
        y1='1.38476'
        x2='0.738178'
        y2='17.1514'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#114A8B' />
        <stop offset='1' stopColor='#0669BC' />
      </linearGradient>
      <linearGradient
        id='paint1_linear_6102_134469'
        x1='10.3402'
        y1='11.4564'
        x2='9.107'
        y2='11.8734'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopOpacity='0.3' />
        <stop offset='0.0711768' stopOpacity='0.2' />
        <stop offset='0.321031' stopOpacity='0.1' />
        <stop offset='0.623053' stopOpacity='0.05' />
        <stop offset='1' stopOpacity='0' />
      </linearGradient>
      <linearGradient
        id='paint2_linear_6102_134469'
        x1='9.45858'
        y1='1.38467'
        x2='15.3168'
        y2='16.9926'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#3CCBF4' />
        <stop offset='1' stopColor='#2892DF' />
      </linearGradient>
    </defs>
  </svg>
)

export const GroqIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    fill='currentColor'
    fillRule='evenodd'
    height='1em'
    viewBox='0 0 24 24'
    width='1em'
    xmlns='http://www.w3.org/2000/svg'
  >
    <title>Groq</title>
    <path d='M12.036 2c-3.853-.035-7 3-7.036 6.781-.035 3.782 3.055 6.872 6.908 6.907h2.42v-2.566h-2.292c-2.407.028-4.38-1.866-4.408-4.23-.029-2.362 1.901-4.298 4.308-4.326h.1c2.407 0 4.358 1.915 4.365 4.278v6.305c0 2.342-1.944 4.25-4.323 4.279a4.375 4.375 0 01-3.033-1.252l-1.851 1.818A7 7 0 0012.029 22h.092c3.803-.056 6.858-3.083 6.879-6.816v-6.5C18.907 4.963 15.817 2 12.036 2z' />
  </svg>
)

export const DeepseekIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg {...props} height='1em' viewBox='0 0 24 24' width='1em' xmlns='http://www.w3.org/2000/svg'>
    <title>DeepSeek</title>
    <path
      d='M23.748 4.482c-.254-.124-.364.113-.512.234-.051.039-.094.09-.137.136-.372.397-.806.657-1.373.626-.829-.046-1.537.214-2.163.848-.133-.782-.575-1.248-1.247-1.548-.352-.156-.708-.311-.955-.65-.172-.241-.219-.51-.305-.774-.055-.16-.11-.323-.293-.35-.2-.031-.278.136-.356.276-.313.572-.434 1.202-.422 1.84.027 1.436.633 2.58 1.838 3.393.137.093.172.187.129.323-.082.28-.18.552-.266.833-.055.179-.137.217-.329.14a5.526 5.526 0 01-1.736-1.18c-.857-.828-1.631-1.742-2.597-2.458a11.365 11.365 0 00-.689-.471c-.985-.957.13-1.743.388-1.836.27-.098.093-.432-.779-.428-.872.004-1.67.295-2.687.684a3.055 3.055 0 01-.465.137 9.597 9.597 0 00-2.883-.102c-1.885.21-3.39 1.102-4.497 2.623C.082 8.606-.231 10.684.152 12.85c.403 2.284 1.569 4.175 3.36 5.653 1.858 1.533 3.997 2.284 6.438 2.14 1.482-.085 3.133-.284 4.994-1.86.47.234.962.327 1.78.397.63.059 1.236-.03 1.705-.128.735-.156.684-.837.419-.961-2.155-1.004-1.682-.595-2.113-.926 1.096-1.296 2.746-2.642 3.392-7.003.05-.347.007-.565 0-.845-.004-.17.035-.237.23-.256a4.173 4.173 0 001.545-.475c1.396-.763 1.96-2.015 2.093-3.517.02-.23-.004-.467-.247-.588zM11.581 18c-2.089-1.642-3.102-2.183-3.52-2.16-.392.024-.321.471-.235.763.09.288.207.486.371.739.114.167.192.416-.113.603-.673.416-1.842-.14-1.897-.167-1.361-.802-2.5-1.86-3.301-3.307-.774-1.393-1.224-2.887-1.298-4.482-.02-.386.093-.522.477-.592a4.696 4.696 0 011.529-.039c2.132.312 3.946 1.265 5.468 2.774.868.86 1.525 1.887 2.202 2.891.72 1.066 1.494 2.082 2.48 2.914.348.292.625.514.891.677-.802.09-2.14.11-3.054-.614zm1-6.44a.306.306 0 01.415-.287.302.302 0 01.2.288.306.306 0 01-.31.307.303.303 0 01-.304-.308zm3.11 1.596c-.2.081-.399.151-.59.16a1.245 1.245 0 01-.798-.254c-.274-.23-.47-.358-.552-.758a1.73 1.73 0 01.016-.588c.07-.327-.008-.537-.239-.727-.187-.156-.426-.199-.688-.199a.559.559 0 01-.254-.078c-.11-.054-.2-.19-.114-.358.028-.054.16-.186.192-.21.356-.202.767-.136 1.146.016.352.144.618.408 1.001.782.391.451.462.576.685.914.176.265.336.537.445.848.067.195-.019.354-.25.452z'
      fill='#4D6BFE'
    />
  </svg>
)

export const GeminiIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg {...props} height='1em' viewBox='0 0 24 24' width='1em' xmlns='http://www.w3.org/2000/svg'>
    <title>Gemini</title>
    <defs>
      <linearGradient id='lobe-icons-gemini-fill' x1='0%' x2='68.73%' y1='100%' y2='30.395%'>
        <stop offset='0%' stopColor='#1C7DFF' />
        <stop offset='52.021%' stopColor='#1C69FF' />
        <stop offset='100%' stopColor='#F0DCD6' />
      </linearGradient>
    </defs>
    <path
      d='M12 24A14.304 14.304 0 000 12 14.304 14.304 0 0012 0a14.305 14.305 0 0012 12 14.305 14.305 0 00-12 12'
      fill='url(#lobe-icons-gemini-fill)'
      fillRule='nonzero'
    />
  </svg>
)

export const CerebrasIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    fill='currentColor'
    height='1em'
    viewBox='0 0 24 24'
    width='1em'
    xmlns='http://www.w3.org/2000/svg'
  >
    <title>Cerebras</title>
    <path
      clipRule='evenodd'
      d='M14.121 2.701a9.299 9.299 0 000 18.598V22.7c-5.91 0-10.7-4.791-10.7-10.701S8.21 1.299 14.12 1.299V2.7zm4.752 3.677A7.353 7.353 0 109.42 17.643l-.901 1.074a8.754 8.754 0 01-1.08-12.334 8.755 8.755 0 0112.335-1.08l-.901 1.075zm-2.255.844a5.407 5.407 0 00-5.048 9.563l-.656 1.24a6.81 6.81 0 016.358-12.043l-.654 1.24zM14.12 8.539a3.46 3.46 0 100 6.922v1.402a4.863 4.863 0 010-9.726v1.402z'
      fill='#F15A29'
      fillRule='evenodd'
    />
    <path d='M15.407 10.836a2.24 2.24 0 00-.51-.409 1.084 1.084 0 00-.544-.152c-.255 0-.483.047-.684.14a1.58 1.58 0 00-.84.912c-.074.203-.11.416-.11.631 0 .*************.631a1.594 1.594 0 00.84.913c.***********.684.14.216 0 .417-.046.602-.135.188-.09.35-.225.475-.392l.928 1.006c-.14.14-.3.261-.482.363a3.367 3.367 0 01-1.083.38c-.17.026-.317.04-.44.04a3.315 3.315 0 01-1.182-.21 2.825 2.825 0 01-.961-.597 2.816 2.816 0 01-.644-.929 2.987 2.987 0 01-.238-1.21c0-.444.08-.847.238-1.21.15-.35.368-.666.643-.929.278-.261.605-.464.962-.596a3.315 3.315 0 011.182-.21c.355 0 .712.068 1.072.204.361.138.685.36.944.649l-.962.97z' />
  </svg>
)

export const OllamaIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    fill='currentColor'
    fillRule='evenodd'
    height='1em'
    viewBox='0 0 24 24'
    width='1em'
    xmlns='http://www.w3.org/2000/svg'
  >
    <title>Ollama</title>
    <path d='M7.905 1.09c.216.085.411.225.588.41.295.306.544.744.734 1.263.191.522.315 1.1.362 1.68a5.054 5.054 0 012.049-.636l.051-.004c.87-.07 1.73.087 2.48.474.101.053.2.11.297.17.05-.569.172-1.134.36-1.644.19-.52.439-.957.733-1.264a1.67 1.67 0 01.589-.41c.257-.1.53-.118.796-.042.401.114.745.368 1.016.737.248.337.434.769.561 1.287.23.934.27 2.163.115 3.645l.053.04.026.019c.757.576 1.284 1.397 1.563 2.35.435 1.487.216 3.155-.534 4.088l-.018.021.002.003c.417.762.67 1.567.724 2.4l.002.03c.064 1.065-.2 2.137-.814 3.19l-.007.01.01.024c.472 1.157.62 2.322.438 3.486l-.006.039a.651.651 0 01-.747.536.648.648 0 01-.54-.742c.167-1.033.01-2.069-.48-3.123a.643.643 0 01.04-.617l.004-.006c.604-.924.854-1.83.8-2.72-.046-.779-.325-1.544-.8-2.273a.644.644 0 01.18-.886l.009-.006c.243-.159.467-.565.58-1.12a4.229 4.229 0 00-.095-1.974c-.205-.7-.58-1.284-1.105-1.683-.595-.454-1.383-.673-2.38-.61a.653.653 0 01-.632-.371c-.314-.665-.772-1.141-1.343-1.436a3.288 3.288 0 00-1.772-.332c-1.245.099-2.343.801-2.67 1.686a.652.652 0 01-.61.425c-1.067.002-1.893.252-2.497.703-.522.39-.878.935-1.066 1.588a4.07 4.07 0 00-.068 1.886c.112.558.331 1.02.582 1.269l.008.007c.212.207.257.53.109.785-.36.622-.629 1.549-.673 2.44-.05 1.018.186 1.902.719 2.536l.016.019a.643.643 0 01.095.69c-.576 1.236-.753 2.252-.562 3.052a.652.652 0 01-1.269.298c-.243-1.018-.078-2.184.473-3.498l.014-.035-.008-.012a4.339 4.339 0 01-.598-1.309l-.005-.019a5.764 5.764 0 01-.177-1.785c.044-.91.278-1.842.622-2.59l.012-.026-.002-.002c-.293-.418-.51-.953-.63-1.545l-.005-.024a5.352 5.352 0 01.093-2.49c.262-.915.777-1.701 1.536-2.269.06-.045.123-.09.186-.132-.159-1.493-.119-2.73.112-3.67.127-.518.314-.95.562-1.287.27-.368.614-.622 1.015-.737.266-.076.54-.059.797.042zm4.116 9.09c.936 0 1.8.313 2.446.855.63.527 1.005 1.235 1.005 1.94 0 .888-.406 1.58-1.133 2.022-.62.375-1.451.557-2.403.557-1.009 0-1.871-.259-2.493-.734-.617-.47-.963-1.13-.963-1.845 0-.707.398-1.417 1.056-1.946.668-.537 1.55-.849 2.485-.849zm0 .896a3.07 3.07 0 00-1.916.65c-.461.37-.722.835-.722 1.25 0 .428.21.829.61 1.134.455.347 1.124.548 1.943.548.799 0 1.473-.147 1.932-.426.463-.28.7-.686.7-1.257 0-.423-.246-.89-.683-1.256-.484-.405-1.14-.643-1.864-.643zm.662 1.21l.004.004c.12.151.095.37-.056.49l-.292.23v.446a.375.375 0 01-.376.373.375.375 0 01-.376-.373v-.46l-.271-.218a.347.347 0 01-.052-.49.353.353 0 01.494-.051l.215.172.22-.174a.353.353 0 01.49.051zm-5.04-1.919c.478 0 .867.39.867.871a.87.87 0 01-.868.871.87.87 0 01-.867-.87.87.87 0 01.867-.872zm8.706 0c.48 0 .868.39.868.871a.87.87 0 01-.868.871.87.87 0 01-.867-.87.87.87 0 01.867-.872zM7.44 2.3l-.003.002a.659.659 0 00-.285.238l-.005.006c-.138.189-.258.467-.348.832-.17.692-.216 1.631-.124 2.782.43-.128.899-.208 1.404-.237l.01-.001.019-.034c.046-.082.095-.161.148-.239.123-.771.022-1.692-.253-2.444-.134-.364-.297-.65-.453-.813a.628.628 0 00-.107-.09L7.44 2.3zm9.174.04l-.002.001a.628.628 0 00-.107.09c-.156.163-.32.45-.453.814-.29.794-.387 1.776-.23 2.572l.058.097.008.014h.03a5.184 5.184 0 011.466.212c.086-1.124.038-2.043-.128-2.722-.09-.365-.21-.643-.349-.832l-.004-.006a.659.659 0 00-.285-.239h-.004z' />
  </svg>
)
export function WealthboxIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns='http://www.w3.org/2000/svg'
      version='1.0'
      width='200'
      height='200'
      viewBox='50 -50 200 200'
    >
      <g fill='#106ED4' stroke='none' transform='translate(0, 200) scale(0.15, -0.15)'>
        <path d='M764 1542 c-110 -64 -230 -134 -266 -156 -42 -24 -71 -49 -78 -65 -7 -19 -10 -126 -8 -334 3 -291 4 -307 23 -326 11 -11 103 -67 205 -126 102 -59 219 -127 261 -151 42 -24 85 -44 96 -44 23 0 527 288 561 320 22 22 22 23 22 340 0 288 -2 320 -17 338 -32 37 -537 322 -569 321 -18 0 -107 -46 -230 -117z m445 -144 c108 -62 206 -123 219 -135 22 -22 22 -26 22 -261 0 -214 -2 -242 -17 -260 -23 -26 -414 -252 -437 -252 -9 0 -70 31 -134 69 -64 37 -161 94 -215 125 l-97 57 2 261 3 261 210 123 c116 67 219 123 229 123 10 1 107 -50 215 -111z' />
        <path d='M700 1246 l-55 -32 -3 -211 -2 -211 37 -23 c21 -12 52 -30 69 -40 l30 -18 103 59 c56 33 109 60 117 60 8 0 62 -27 119 -60 l104 -60 63 37 c35 21 66 42 70 48 4 5 8 101 8 212 l0 202 -62 35 -63 35 -3 -197 c-1 -108 -6 -200 -11 -205 -5 -5 -54 17 -114 52 -58 34 -108 61 -111 61 -2 0 -51 -27 -107 -60 -56 -32 -106 -57 -111 -54 -4 3 -8 95 -8 205 0 109 -3 199 -7 199 -5 -1 -33 -16 -63 -34z' />
      </g>
    </svg>
  )
}

export function WebhookIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      fill='currentColor'
      width='800px'
      height='800px'
      viewBox='0 0 24 24'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path d='M17.974 7A4.967 4.967 0 0 0 18 6.5a5.5 5.5 0 1 0-8.672 4.491L7.18 15.114A2.428 2.428 0 0 0 6.496 15 2.5 2.5 0 1 0 9 17.496a2.36 2.36 0 0 0-.93-1.925l2.576-4.943-.41-.241A4.5 4.5 0 1 1 17 6.5a4.8 4.8 0 0 1-.022.452zM6.503 18.999a1.5 1.5 0 1 1 1.496-1.503A1.518 1.518 0 0 1 6.503 19zM18.5 12a5.735 5.735 0 0 0-1.453.157l-2.744-3.941A2.414 2.414 0 0 0 15 6.5a2.544 2.544 0 1 0-1.518 2.284l3.17 4.557.36-.13A4.267 4.267 0 0 1 18.5 13a4.5 4.5 0 1 1-.008 9h-.006a4.684 4.684 0 0 1-3.12-1.355l-.703.71A5.653 5.653 0 0 0 18.49 23h.011a5.5 5.5 0 0 0 0-11zM11 6.5A1.5 1.5 0 1 1 12.5 8 1.509 1.509 0 0 1 11 6.5zM18.5 20a2.5 2.5 0 1 0-2.447-3h-5.05l-.003.497A4.546 4.546 0 0 1 6.5 22 4.526 4.526 0 0 1 2 17.5a4.596 4.596 0 0 1 3.148-4.37l-.296-.954A5.606 5.606 0 0 0 1 17.5 5.532 5.532 0 0 0 6.5 23a5.573 5.573 0 0 0 5.478-5h4.08a2.487 2.487 0 0 0 2.442 2zm0-4a1.5 1.5 0 1 1-1.5 1.5 1.509 1.509 0 0 1 1.5-1.5z' />
      <path fill='none' d='M0 0h24v24H0z' />
    </svg>
  )
}

export function ScheduleIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns='http://www.w3.org/2000/svg'
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      stroke='currentColor'
      strokeWidth='2'
      strokeLinecap='round'
      strokeLinejoin='round'
    >
      <path d='M8 2v4' />
      <path d='M16 2v4' />
      <rect width='18' height='18' x='3' y='4' rx='2' />
      <path d='M3 10h18' />
    </svg>
  )
}

export function QdrantIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg {...props} fill='none' viewBox='0 0 49 56' xmlns='http://www.w3.org/2000/svg'>
      <g clipPath='url(#b)'>
        <path
          d='m38.489 51.477-1.1167-30.787-2.0223-8.1167 13.498 1.429v37.242l-8.2456 4.7589-2.1138-4.5259z'
          clipRule='evenodd'
          fill='#24386C'
          fillRule='evenodd'
        />
        <path
          d='m48.847 14-8.2457 4.7622-17.016-3.7326-19.917 8.1094-3.3183-9.139 12.122-7 12.126-7 12.123 7 12.126 7z'
          clipRule='evenodd'
          fill='#7589BE'
          fillRule='evenodd'
        />
        <path
          d='m0.34961 13.999 8.2457 4.7622 4.7798 14.215 16.139 12.913-4.9158 10.109-12.126-7.0004-12.123-7v-28z'
          clipRule='evenodd'
          fill='#B2BFE8'
          fillRule='evenodd'
        />
        <path
          d='m30.066 38.421-5.4666 8.059v9.5207l7.757-4.4756 3.9968-5.9681'
          clipRule='evenodd'
          fill='#24386C'
          fillRule='evenodd'
        />
        <path
          d='m24.602 36.962-7.7603-13.436 1.6715-4.4531 6.3544-3.0809 7.488 7.5343-7.7536 13.436z'
          clipRule='evenodd'
          fill='#7589BE'
          fillRule='evenodd'
        />
        <path
          d='m16.843 23.525 7.7569 4.4756v8.9585l-7.1741 0.3087-4.3397-5.5412 3.7569-8.2016z'
          clipRule='evenodd'
          fill='#B2BFE8'
          fillRule='evenodd'
        />
        <path
          d='m24.6 28 7.757-4.4752 5.2792 8.7903-6.3886 5.2784-6.6476-0.6346v-8.9589z'
          clipRule='evenodd'
          fill='#24386C'
          fillRule='evenodd'
        />
        <path
          d='m32.355 51.524 8.2457 4.476v-37.238l-8.0032-4.6189-7.9995-4.6189-8.0031 4.6189-7.9995 4.6189v18.479l7.9995 4.6189 8.0031 4.6193 7.757-4.4797v9.5244zm0-19.045-7.757 4.4793-7.7569-4.4793v-8.9549l7.7569-4.4792 7.757 4.4792v8.9549z'
          clipRule='evenodd'
          fill='#DC244C'
          fillRule='evenodd'
        />
        <path d='m24.603 46.483v-9.5222l-7.7166-4.4411v9.5064l7.7166 4.4569z' fill='url(#a)' />
      </g>
      <defs>
        <linearGradient
          id='a'
          x1='23.18'
          x2='15.491'
          y1='38.781'
          y2='38.781'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#FF3364' offset='0' />
          <stop stopColor='#C91540' stopOpacity='0' offset='1' />
        </linearGradient>
        <clipPath id='b'>
          <rect transform='translate(.34961)' width='48.3' height='56' fill='#fff' />
        </clipPath>
      </defs>
    </svg>
  )
}

export function ArxivIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg {...props} id='logomark' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 17.732 24.269'>
      <g id='tiny'>
        <path
          d='M573.549,280.916l2.266,2.738,6.674-7.84c.353-.47.52-.717.353-1.117a1.218,1.218,0,0,0-1.061-.748h0a.953.953,0,0,0-.712.262Z'
          transform='translate(-566.984 -271.548)'
          fill='#bdb9b4'
        />
        <path
          d='M579.525,282.225l-10.606-10.174a1.413,1.413,0,0,0-.834-.5,1.09,1.09,0,0,0-1.027.66c-.167.4-.047.681.319,1.206l8.44,10.242h0l-6.282,7.716a1.336,1.336,0,0,0-.323,1.3,1.114,1.114,0,0,0,1.04.69A.992.992,0,0,0,571,293l8.519-7.92A1.924,1.924,0,0,0,579.525,282.225Z'
          transform='translate(-566.984 -271.548)'
          fill='#b31b1b'
        />
        <path
          d='M584.32,293.912l-8.525-10.275,0,0L573.53,280.9l-1.389,1.254a2.063,2.063,0,0,0,0,2.965l10.812,10.419a.925.925,0,0,0,.742.282,1.039,1.039,0,0,0,.953-.667A1.261,1.261,0,0,0,584.32,293.912Z'
          transform='translate(-566.984 -271.548)'
          fill='#bdb9b4'
        />
      </g>
    </svg>
  )
}

export function WikipediaIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      fill='currentColor'
      version='1.1'
      id='Capa_1'
      xmlns='http://www.w3.org/2000/svg'
      xmlnsXlink='http://www.w3.org/1999/xlink'
      width='800px'
      height='800px'
      viewBox='0 0 98.05 98.05'
      xmlSpace='preserve'
    >
      <g>
        <path
          d='M98.023,17.465l-19.584-0.056c-0.004,0.711-0.006,1.563-0.017,2.121c1.664,0.039,5.922,0.822,7.257,4.327L66.92,67.155
		c-0.919-2.149-9.643-21.528-10.639-24.02l9.072-18.818c1.873-2.863,5.455-4.709,8.918-4.843l-0.01-1.968L55.42,17.489
		c-0.045,0.499,0.001,1.548-0.068,2.069c5.315,0.144,7.215,1.334,5.941,4.508c-2.102,4.776-6.51,13.824-7.372,15.475
		c-2.696-5.635-4.41-9.972-7.345-16.064c-1.266-2.823,1.529-3.922,4.485-4.004v-1.981l-21.82-0.067
		c0.016,0.93-0.021,1.451-0.021,2.131c3.041,0.046,6.988,0.371,8.562,3.019c2.087,4.063,9.044,20.194,11.149,24.514
		c-2.685,5.153-9.207,17.341-11.544,21.913c-3.348-7.43-15.732-36.689-19.232-44.241c-1.304-3.218,3.732-5.077,6.646-5.213
		l0.019-2.148L0,17.398c0.005,0.646,0.027,1.71,0.029,2.187c4.025-0.037,9.908,6.573,11.588,10.683
		c7.244,16.811,14.719,33.524,21.928,50.349c0.002,0.029,2.256,0.059,2.281,0.008c4.717-9.653,10.229-19.797,15.206-29.56
		L63.588,80.64c0.005,0.004,2.082,0.016,2.093,0.007c7.962-18.196,19.892-46.118,23.794-54.933c1.588-3.767,4.245-6.064,8.543-6.194
		l0.032-1.956L98.023,17.465z'
        />
      </g>
    </svg>
  )
}

export function HunterIOIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='20'
      height='19'
      viewBox='0 0 20 19'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M12.0671 8.43455C11.6625 8.55094 11.2164 8.55288 10.7992 8.53525C10.3141 8.51472 9.80024 8.45339 9.35223 8.25426C8.98359 8.09047 8.68787 7.79493 8.84262 7.36805C8.95175 7.06699 9.19361 6.79803 9.47319 6.64644C9.78751 6.4759 10.1329 6.50361 10.4474 6.65774C10.8005 6.83082 11.0942 7.11235 11.3604 7.3964C11.5 7.54536 11.6332 7.70002 11.7646 7.85617C11.8252 7.92801 12.2364 8.33865 12.0671 8.43455ZM18.7923 8.58131C18.17 8.43655 17.4348 8.4884 16.811 8.38867C15.8284 8.23146 14.3648 7.08576 13.5714 5.92122C13.0201 5.11202 12.757 4.28785 12.3356 3.28356C12.0415 2.58257 11.4001 0.365389 10.5032 1.40318C10.1339 1.83057 9.7204 3.23752 9.41837 3.2177C9.19467 3.26971 9.15818 2.83371 9.08739 2.64738C8.95886 2.30903 8.89071 1.9176 8.7185 1.59854C8.58086 1.34353 8.40014 1.03806 8.12337 0.91412C7.63027 0.660572 7.03575 1.42476 6.74072 2.33095C6.61457 2.81687 5.76653 3.75879 5.39721 3.9866C3.71684 5.02352 0.344233 6.11595 0.000262184 9.75358C-0.00114142 9.76867 0.000262182 9.81455 0.0573714 9.77323C0.459591 9.48197 5.02183 6.19605 2.09392 12.5476C0.300195 16.439 8.96062 18.917 9.40582 18.9271C9.46582 18.9284 9.46144 18.9011 9.46347 18.8832C10.1546 12.6724 16.9819 13.3262 18.5718 11.8387C20.1474 10.3649 20.1796 8.93816 18.7923 8.58131Z'
        fill='#FA5320'
      />
    </svg>
  )
}

export function MicrosoftOneDriveIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg {...props} fill='currentColor' viewBox='0 0 32 32' xmlns='http://www.w3.org/2000/svg'>
      <g>
        <path
          d='M12.20245,11.19292l.00031-.0011,6.71765,4.02379,4.00293-1.68451.00018.00068A6.4768,6.4768,0,0,1,25.5,13c.14764,0,.29358.0067.43878.01639a10.00075,10.00075,0,0,0-18.041-3.01381C7.932,10.00215,7.9657,10,8,10A7.96073,7.96073,0,0,1,12.20245,11.19292Z'
          fill='#0364b8'
        />
        <path
          d='M12.20276,11.19182l-.00031.0011A7.96073,7.96073,0,0,0,8,10c-.0343,0-.06805.00215-.10223.00258A7.99676,7.99676,0,0,0,1.43732,22.57277l5.924-2.49292,2.63342-1.10819,5.86353-2.46746,3.06213-1.28859Z'
          fill='#0078d4'
        />
        <path
          d='M25.93878,13.01639C25.79358,13.0067,25.64764,13,25.5,13a6.4768,6.4768,0,0,0-2.57648.53178l-.00018-.00068-4.00293,1.68451,1.16077.69528L23.88611,18.19l1.66009.99438,5.67633,3.40007a6.5002,6.5002,0,0,0-5.28375-9.56805Z'
          fill='#1490df'
        />
        <path
          d='M25.5462,19.18437,23.88611,18.19l-3.80493-2.2791-1.16077-.69528L15.85828,16.5042,9.99475,18.97166,7.36133,20.07985l-5.924,2.49292A7.98889,7.98889,0,0,0,8,26H25.5a6.49837,6.49837,0,0,0,5.72253-3.41556Z'
          fill='#28a8ea'
        />
      </g>
    </svg>
  )
}

export function MicrosoftSharepointIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg {...props} fill='currentColor' viewBox='0 0 32 32' xmlns='http://www.w3.org/2000/svg'>
      <circle fill='#036C70' cx='16.31' cy='8.90' r='8.90' />
      <circle fill='#1A9BA1' cx='23.72' cy='17.05' r='8.15' />
      <circle fill='#37C6D0' cx='17.42' cy='24.83' r='6.30' />
      <path
        fill='#000000'
        opacity='0.1'
        d='M17.79,8.03v15.82c0,0.55-0.34,1.04-0.85,1.25c-0.16,0.07-0.34,0.10-0.51,0.10H11.13c-0.01-0.13-0.01-0.24-0.01-0.37c0-0.12,0-0.25,0.01-0.37c0.14-2.37,1.59-4.46,3.77-5.40v-1.38c-4.85-0.77-8.15-5.32-7.39-10.17c0.01-0.03,0.01-0.07,0.02-0.10c0.04-0.25,0.09-0.50,0.16-0.74h8.74c0.74,0,1.36,0.60,1.36,1.36z'
      />
      <path
        fill='#000000'
        opacity='0.2'
        d='M15.69,7.41H7.54c-0.82,4.84,2.43,9.43,7.27,10.25c0.15,0.02,0.29,0.05,0.44,0.06c-2.30,1.09-3.97,4.18-4.12,6.73c-0.01,0.12-0.02,0.25-0.01,0.37c0,0.13,0,0.24,0.01,0.37c0.01,0.25,0.05,0.50,0.10,0.74h4.47c0.55,0,1.04-0.34,1.25-0.85c0.07-0.16,0.10-0.34,0.10-0.51V8.77c0-0.75-0.61-1.36-1.36-1.36z'
      />
      <path
        fill='#000000'
        opacity='0.2'
        d='M15.69,7.41H7.54c-0.82,4.84,2.43,9.43,7.27,10.26c0.10,0.02,0.20,0.03,0.30,0.05c-2.22,1.17-3.83,4.26-3.97,6.75h4.56c0.75,0,1.35-0.61,1.36-1.36V8.77c0-0.75-0.61-1.36-1.36-1.36z'
      />
      <path
        fill='#000000'
        opacity='0.2'
        d='M14.95,7.41H7.54c-0.78,4.57,2.08,8.97,6.58,10.11c-1.84,2.43-2.27,5.61-2.58,7.22h3.82c0.75,0,1.35-0.61,1.36-1.36V8.77c0-0.75-0.61-1.36-1.36-1.36z'
      />
      <path
        fill='#008789'
        d='M1.36,7.41h13.58c0.75,0,1.36,0.61,1.36,1.36v13.58c0,0.75-0.61,1.36-1.36,1.36H1.36c-0.75,0-1.36-0.61-1.36-1.36V8.77C0,8.02,0.61,7.41,1.36,7.41z'
      />
      <path
        fill='#FFFFFF'
        d='M6.07,15.42c-0.32-0.21-0.58-0.49-0.78-0.82c-0.19-0.34-0.28-0.73-0.27-1.12c-0.02-0.53,0.16-1.05,0.50-1.46c0.36-0.41,0.82-0.71,1.34-0.87c0.59-0.19,1.21-0.29,1.83-0.28c0.82-0.03,1.63,0.08,2.41,0.34v1.71c-0.34-0.20-0.71-0.35-1.09-0.44c-0.42-0.10-0.84-0.15-1.27-0.15c-0.45-0.02-0.90,0.08-1.31,0.28c-0.31,0.14-0.52,0.44-0.52,0.79c0,0.21,0.08,0.41,0.22,0.56c0.17,0.18,0.37,0.32,0.59,0.42c0.25,0.12,0.62,0.29,1.11,0.49c0.05,0.02,0.11,0.04,0.16,0.06c0.49,0.19,0.96,0.42,1.40,0.69c0.34,0.21,0.62,0.49,0.83,0.83c0.21,0.39,0.31,0.82,0.30,1.26c0.02,0.54-0.14,1.08-0.47,1.52c-0.33,0.40-0.77,0.69-1.26,0.85c-0.58,0.18-1.19,0.27-1.80,0.26c-0.55,0-1.09-0.04-1.63-0.13c-0.45-0.07-0.90-0.20-1.32-0.39v-1.80c0.40,0.29,0.86,0.50,1.34,0.64c0.48,0.15,0.97,0.23,1.47,0.24c0.46,0.03,0.92-0.07,1.34-0.28c0.29-0.16,0.46-0.47,0.46-0.80c0-0.23-0.09-0.45-0.25-0.61c-0.20-0.20-0.44-0.36-0.69-0.48c-0.30-0.15-0.73-0.34-1.31-0.59C6.91,16.14,6.48,15.80,6.07,15.42z'
      />
    </svg>
  )
}

export function MicrosoftPlannerIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg {...props} fill='currentColor' viewBox='-1 -1 27 27' xmlns='http://www.w3.org/2000/svg'>
      <defs>
        <linearGradient
          id='paint0_linear_3984_11038'
          x1='6.38724'
          y1='3.74167'
          x2='2.15779'
          y2='12.777'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#8752E0' />
          <stop offset='1' stopColor='#541278' />
        </linearGradient>
        <linearGradient
          id='paint1_linear_3984_11038'
          x1='8.38032'
          y1='11.0696'
          x2='4.94062'
          y2='7.69244'
          gradientUnits='userSpaceOnUse'
        >
          <stop offset='0.12172' stopColor='#3D0D59' />
          <stop offset='1' stopColor='#7034B0' stopOpacity='0' />
        </linearGradient>
        <linearGradient
          id='paint2_linear_3984_11038'
          x1='18.3701'
          y1='-3.33385e-05'
          x2='9.85717'
          y2='20.4192'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#DB45E0' />
          <stop offset='1' stopColor='#6C0F71' />
        </linearGradient>
        <linearGradient
          id='paint3_linear_3984_11038'
          x1='18.3701'
          y1='-3.33385e-05'
          x2='9.85717'
          y2='20.4192'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#DB45E0' />
          <stop offset='0.677403' stopColor='#A829AE' />
          <stop offset='1' stopColor='#8F28B3' />
        </linearGradient>
        <linearGradient
          id='paint4_linear_3984_11038'
          x1='18.0002'
          y1='7.49958'
          x2='14.0004'
          y2='23.9988'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#3DCBFF' />
          <stop offset='1' stopColor='#00479E' />
        </linearGradient>
        <linearGradient
          id='paint5_linear_3984_11038'
          x1='18.2164'
          y1='7.92626'
          x2='10.5237'
          y2='22.9363'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#3DCBFF' />
          <stop offset='1' stopColor='#4A40D4' />
        </linearGradient>
      </defs>
      <path
        d='M8.25809 15.7412C7.22488 16.7744 5.54971 16.7744 4.5165 15.7412L0.774909 11.9996C-0.258303 10.9664 -0.258303 9.29129 0.774908 8.25809L4.5165 4.51655C5.54971 3.48335 7.22488 3.48335 8.25809 4.51655L11.9997 8.2581C13.0329 9.29129 13.0329 10.9664 11.9997 11.9996L8.25809 15.7412Z'
        fill='url(#paint0_linear_3984_11038)'
      />
      <path
        d='M8.25809 15.7412C7.22488 16.7744 5.54971 16.7744 4.5165 15.7412L0.774909 11.9996C-0.258303 10.9664 -0.258303 9.29129 0.774908 8.25809L4.5165 4.51655C5.54971 3.48335 7.22488 3.48335 8.25809 4.51655L11.9997 8.2581C13.0329 9.29129 13.0329 10.9664 11.9997 11.9996L8.25809 15.7412Z'
        fill='url(#paint1_linear_3984_11038)'
      />
      <path
        d='M0.774857 11.9999C1.80809 13.0331 3.48331 13.0331 4.51655 11.9999L15.7417 0.774926C16.7749 -0.258304 18.4501 -0.258309 19.4834 0.774914L23.225 4.51655C24.2583 5.54977 24.2583 7.22496 23.225 8.25819L11.9999 19.4832C10.9667 20.5164 9.29146 20.5164 8.25822 19.4832L0.774857 11.9999Z'
        fill='url(#paint2_linear_3984_11038)'
      />
      <path
        d='M0.774857 11.9999C1.80809 13.0331 3.48331 13.0331 4.51655 11.9999L15.7417 0.774926C16.7749 -0.258304 18.4501 -0.258309 19.4834 0.774914L23.225 4.51655C24.2583 5.54977 24.2583 7.22496 23.225 8.25819L11.9999 19.4832C10.9667 20.5164 9.29146 20.5164 8.25822 19.4832L0.774857 11.9999Z'
        fill='url(#paint3_linear_3984_11038)'
      />
      <path
        d='M4.51642 15.7413C5.54966 16.7746 7.22487 16.7746 8.25812 15.7413L15.7415 8.25803C16.7748 7.2248 18.45 7.2248 19.4832 8.25803L23.2249 11.9997C24.2582 13.0329 24.2582 14.7081 23.2249 15.7413L15.7415 23.2246C14.7083 24.2579 13.033 24.2579 11.9998 23.2246L4.51642 15.7413Z'
        fill='url(#paint4_linear_3984_11038)'
      />
      <path
        d='M4.51642 15.7413C5.54966 16.7746 7.22487 16.7746 8.25812 15.7413L15.7415 8.25803C16.7748 7.2248 18.45 7.2248 19.4832 8.25803L23.2249 11.9997C24.2582 13.0329 24.2582 14.7081 23.2249 15.7413L15.7415 23.2246C14.7083 24.2579 13.033 24.2579 11.9998 23.2246L4.51642 15.7413Z'
        fill='url(#paint5_linear_3984_11038)'
      />
    </svg>
  )
}

export function ParallelIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      fill='currentColor'
      width='271'
      height='270'
      viewBox='0 0 271 270'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M267.804 105.65H193.828C194.026 106.814 194.187 107.996 194.349 109.178H76.6703C76.4546 110.736 76.2388 112.312 76.0591 113.87H1.63342C1.27387 116.198 0.950289 118.543 0.698608 120.925H75.3759C75.2501 122.483 75.1602 124.059 75.0703 125.617H195.949C196.003 126.781 196.057 127.962 196.093 129.144H270.68V125.384C270.195 118.651 269.242 112.061 267.804 105.65Z'
        fill='#1D1C1A'
      />
      <path
        d='M195.949 144.401H75.0703C75.1422 145.977 75.2501 147.535 75.3759 149.093H0.698608C0.950289 151.457 1.2559 153.802 1.63342 156.148H76.0591C76.2388 157.724 76.4366 159.282 76.6703 160.84H194.349C194.187 162.022 194.008 163.186 193.828 164.367H267.804C269.242 157.957 270.195 151.367 270.68 144.634V140.874H196.093C196.057 142.055 196.003 143.219 195.949 144.401Z'
        fill='#1D1C1A'
      />
      <path
        d='M190.628 179.642H80.3559C80.7514 181.218 81.1828 182.776 81.6143 184.334H9.30994C10.2448 186.715 11.2515 189.061 12.3121 191.389H83.7536C84.2749 192.965 84.7962 194.523 85.3535 196.08H185.594C185.163 197.262 184.732 198.426 184.282 199.608H254.519C258.6 192.177 261.98 184.316 264.604 176.114H191.455C191.185 177.296 190.898 178.46 190.61 179.642H190.628Z'
        fill='#1D1C1A'
      />
      <path
        d='M177.666 214.883H93.3352C94.1082 216.458 94.9172 218.034 95.7441 219.574H29.8756C31.8351 221.992 33.8666 224.337 35.9699 226.63H99.6632C100.598 228.205 101.551 229.781 102.522 231.321H168.498C167.761 232.503 167.006 233.685 166.233 234.849H226.762C234.474 227.847 241.36 219.95 247.292 211.355H179.356C178.799 212.537 178.26 213.719 177.684 214.883H177.666Z'
        fill='#1D1C1A'
      />
      <path
        d='M154.943 250.106H116.058C117.371 251.699 118.701 253.257 120.067 254.797H73.021C91.6094 264.431 112.715 269.946 135.096 270C135.24 270 135.366 270 135.492 270C135.618 270 135.761 270 135.887 270C164.04 269.911 190.178 261.28 211.805 246.56H157.748C156.813 247.742 155.878 248.924 154.925 250.088L154.943 250.106Z'
        fill='#1D1C1A'
      />
      <path
        d='M116.059 19.9124H154.943C155.896 21.0764 156.831 22.2582 157.766 23.4401H211.823C190.179 8.72065 164.058 0.0895344 135.906 0C135.762 0 135.636 0 135.51 0C135.384 0 135.24 0 135.115 0C112.715 0.0716275 91.6277 5.56904 73.0393 15.2029H120.086C118.719 16.7429 117.389 18.3187 116.077 19.8945L116.059 19.9124Z'
        fill='#1D1C1A'
      />
      <path
        d='M93.3356 55.1532H177.667C178.242 56.3171 178.799 57.499 179.339 58.6808H247.274C241.342 50.0855 234.457 42.1886 226.744 35.187H166.215C166.988 36.351 167.743 37.5328 168.48 38.7147H102.504C101.533 40.2726 100.58 41.8305 99.6456 43.4063H35.9523C33.831 45.6804 31.7996 48.0262 29.858 50.4616H95.7265C94.8996 52.0195 94.1086 53.5774 93.3176 55.1532H93.3356Z'
        fill='#1D1C1A'
      />
      <path
        d='M80.3736 90.3758H190.646C190.933 91.5398 191.221 92.7216 191.491 93.9035H264.64C262.015 85.7021 258.636 77.841 254.555 70.4097H184.318C184.767 71.5736 185.199 72.7555 185.63 73.9373H85.3893C84.832 75.4952 84.2927 77.0531 83.7893 78.6289H12.3479C11.2872 80.9389 10.2805 83.2847 9.3457 85.6842H81.65C81.2186 87.2421 80.7871 88.8 80.3916 90.3758H80.3736Z'
        fill='#1D1C1A'
      />
    </svg>
  )
}

export function PostgresIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='800px'
      height='800px'
      viewBox='-4 0 264 264'
      xmlns='http://www.w3.org/2000/svg'
      preserveAspectRatio='xMinYMin meet'
    >
      <path d='M255.008 158.086c-1.535-4.649-5.556-7.887-10.756-8.664-2.452-.366-5.26-.21-8.583.475-5.792 1.195-10.089 1.65-13.225 1.738 11.837-19.985 21.462-42.775 27.003-64.228 8.96-34.689 4.172-50.492-1.423-57.64C233.217 10.847 211.614.683 185.552.372c-13.903-.17-26.108 2.575-32.475 4.549-5.928-1.046-12.302-1.63-18.99-1.738-12.537-.2-23.614 2.533-33.079 8.15-5.24-1.772-13.65-4.27-23.362-5.864-22.842-3.75-41.252-.828-54.718 8.685C6.622 25.672-.937 45.684.461 73.634c.444 8.874 5.408 35.874 13.224 61.48 4.492 14.718 9.282 26.94 14.237 36.33 7.027 13.315 14.546 21.156 22.987 23.972 4.731 1.576 13.327 2.68 22.368-4.85 1.146 1.388 2.675 2.767 4.704 4.048 2.577 1.625 5.728 2.953 8.875 3.74 11.341 2.835 21.964 2.126 31.027-1.848.056 1.612.099 3.152.135 4.482.06 2.157.12 4.272.199 6.25.537 13.374 1.447 23.773 4.143 31.049.148.4.347 1.01.557 1.657 1.345 4.118 3.594 11.012 9.316 16.411 5.925 5.593 13.092 7.308 19.656 7.308 3.292 0 6.433-.432 9.188-1.022 9.82-2.105 20.973-5.311 29.041-16.799 7.628-10.86 11.336-27.217 12.007-52.99.087-.729.167-1.425.244-2.088l.16-1.362 1.797.158.463.031c10.002.456 22.232-1.665 29.743-5.154 5.935-2.754 24.954-12.795 20.476-26.351' />
      <path
        d='M237.906 160.722c-29.74 6.135-31.785-3.934-31.785-3.934 31.4-46.593 44.527-105.736 33.2-120.211-30.904-39.485-84.399-20.811-85.292-20.327l-.287.052c-5.876-1.22-12.451-1.946-19.842-2.067-13.456-.22-23.664 3.528-31.41 9.402 0 0-95.43-39.314-90.991 49.444.944 18.882 27.064 142.873 58.218 105.422 11.387-13.695 22.39-25.274 22.39-25.274 5.464 3.63 12.006 5.482 18.864 4.817l.533-.452c-.166 1.7-.09 3.363.213 5.332-8.026 8.967-5.667 10.541-21.711 13.844-16.235 3.346-6.698 9.302-.471 10.86 7.549 1.887 25.013 4.561 36.813-11.958l-.47 1.885c3.144 2.519 5.352 16.383 4.982 28.952-.37 12.568-.617 21.197 1.86 27.937 2.479 6.74 4.948 21.905 26.04 17.386 17.623-3.777 26.756-13.564 28.027-29.89.901-11.606 2.942-9.89 3.07-20.267l1.637-4.912c1.887-15.733.3-20.809 11.157-18.448l2.64.232c7.99.363 18.45-1.286 24.589-4.139 13.218-6.134 21.058-16.377 8.024-13.686h.002'
        fill='#336791'
      />
      <path
        d='M108.076 81.525c-2.68-.373-5.107-.028-6.335.902-.69.523-.904 1.129-.962 1.546-.154 1.105.62 2.327 1.096 2.957 1.346 1.784 3.312 3.01 5.258 3.28.282.04.563.058.842.058 3.245 0 6.196-2.527 6.456-4.392.325-2.336-3.066-3.893-6.355-4.35M196.86 81.599c-.256-1.831-3.514-2.353-6.606-1.923-3.088.43-6.082 1.824-5.832 3.659.2 1.427 2.777 3.863 5.827 3.863.258 0 .518-.017.78-.054 2.036-.282 3.53-1.575 4.24-2.32 1.08-1.136 1.706-2.402 1.591-3.225'
        fill='#FFF'
      />
      <path
        d='M247.802 160.025c-1.134-3.429-4.784-4.532-10.848-3.28-18.005 3.716-24.453 1.142-26.57-.417 13.995-21.32 25.508-47.092 31.719-71.137 2.942-11.39 4.567-21.968 4.7-30.59.147-9.463-1.465-16.417-4.789-20.665-13.402-17.125-33.072-26.311-56.882-26.563-16.369-.184-30.199 4.005-32.88 5.183-5.646-1.404-11.801-2.266-18.502-2.376-12.288-.199-22.91 2.743-31.704 8.74-3.82-1.422-13.692-4.811-25.765-6.756-20.872-3.36-37.458-.814-49.294 7.571-14.123 10.006-20.643 27.892-19.38 53.16.425 8.501 5.269 34.653 12.913 59.698 10.062 32.964 21 51.625 32.508 55.464 1.347.449 2.9.763 4.613.763 4.198 0 9.345-1.892 14.7-8.33a529.832 529.832 0 0 1 20.261-22.926c4.524 2.428 9.494 3.784 14.577 3.92.01.133.023.266.035.398a117.66 117.66 0 0 0-2.57 3.175c-3.522 4.471-4.255 5.402-15.592 7.736-3.225.666-11.79 2.431-11.916 8.435-.136 6.56 10.125 9.315 11.294 9.607 4.074 1.02 7.999 1.523 11.742 1.523 9.103 0 17.114-2.992 23.516-8.781-.197 23.386.778 46.43 3.586 53.451 2.3 5.748 7.918 19.795 25.664 19.794 2.604 0 5.47-.303 8.623-.979 18.521-3.97 26.564-12.156 29.675-30.203 1.665-9.645 4.522-32.676 5.866-45.03 2.836.885 6.487 1.29 10.434 1.289 8.232 0 17.731-1.749 23.688-4.514 6.692-3.108 18.768-10.734 16.578-17.36zm-44.106-83.48c-.061 3.647-.563 6.958-1.095 10.414-.573 3.717-1.165 7.56-1.314 12.225-.147 4.54.42 9.26.968 13.825 1.108 9.22 2.245 18.712-2.156 28.078a36.508 36.508 0 0 1-1.95-4.009c-.547-1.326-1.735-3.456-3.38-6.404-6.399-11.476-21.384-38.35-13.713-49.316 2.285-3.264 8.084-6.62 22.64-4.813zm-17.644-61.787c21.334.471 38.21 8.452 50.158 23.72 9.164 11.711-.927 64.998-30.14 110.969a171.33 171.33 0 0 0-.886-1.117l-.37-.462c7.549-12.467 6.073-24.802 4.759-35.738-.54-4.488-1.05-8.727-.92-12.709.134-4.22.692-7.84 1.232-11.34.663-4.313 1.338-8.776 1.152-14.037.139-.552.195-1.204.122-1.978-.475-5.045-6.235-20.144-17.975-33.81-6.422-7.475-15.787-15.84-28.574-21.482 5.5-1.14 13.021-2.203 21.442-2.016zM66.674 175.778c-5.9 7.094-9.974 5.734-11.314 5.288-8.73-2.912-18.86-21.364-27.791-50.624-7.728-25.318-12.244-50.777-12.602-57.916-1.128-22.578 4.345-38.313 16.268-46.769 19.404-13.76 51.306-5.524 64.125-1.347-.184.182-.376.352-.558.537-21.036 21.244-20.537 57.54-20.485 59.759-.002.856.07 2.068.168 3.735.362 6.105 1.036 17.467-.764 30.334-1.672 11.957 2.014 23.66 10.111 32.109a36.275 36.275 0 0 0 2.617 2.468c-3.604 3.86-11.437 12.396-19.775 22.426zm22.479-29.993c-6.526-6.81-9.49-16.282-8.133-25.99 1.9-13.592 1.199-25.43.822-31.79-.053-.89-.1-1.67-.127-2.285 3.073-2.725 17.314-10.355 27.47-8.028 4.634 1.061 7.458 4.217 8.632 9.645 6.076 28.103.804 39.816-3.432 49.229-.873 1.939-1.698 3.772-2.402 5.668l-.546 1.466c-1.382 3.706-2.668 7.152-3.465 10.424-6.938-.02-13.687-2.984-18.819-8.34zm1.065 37.9c-2.026-.506-3.848-1.385-4.917-2.114.893-.42 2.482-.992 5.238-1.56 13.337-2.745 15.397-4.683 19.895-10.394 1.031-1.31 2.2-2.794 3.819-4.602l.002-.002c2.411-2.7 3.514-2.242 5.514-1.412 1.621.67 3.2 2.702 3.84 4.938.303 1.056.643 3.06-.47 4.62-9.396 13.156-23.088 12.987-32.921 10.526zm69.799 64.952c-16.316 3.496-22.093-4.829-25.9-14.346-2.457-6.144-3.665-33.85-2.808-64.447.011-.407-.047-.8-.159-1.17a15.444 15.444 0 0 0-.456-2.162c-1.274-4.452-4.379-8.176-8.104-9.72-1.48-.613-4.196-1.738-7.46-.903.696-2.868 1.903-6.107 3.212-9.614l.549-1.475c.618-1.663 1.394-3.386 2.214-5.21 4.433-9.848 10.504-23.337 3.915-53.81-2.468-11.414-10.71-16.988-23.204-15.693-7.49.775-14.343 3.797-17.761 5.53-.735.372-1.407.732-2.035 1.082.954-11.5 4.558-32.992 18.04-46.59 8.489-8.56 19.794-12.788 33.568-12.56 27.14.444 44.544 14.372 54.366 25.979 8.464 10.001 13.047 20.076 14.876 25.51-13.755-1.399-23.11 1.316-27.852 8.096-10.317 14.748 5.644 43.372 13.315 57.129 1.407 2.521 2.621 4.7 3.003 5.626 2.498 6.054 5.732 10.096 8.093 13.046.724.904 1.426 1.781 1.96 2.547-4.166 1.201-11.649 3.976-10.967 17.847-.55 6.96-4.461 39.546-6.448 51.059-2.623 15.21-8.22 20.875-23.957 24.25zm68.104-77.936c-4.26 1.977-11.389 3.46-18.161 3.779-7.48.35-11.288-.838-12.184-1.569-.42-8.644 2.797-9.547 6.202-10.503.535-.15 1.057-.297 1.561-.473.313.255.656.508 1.032.756 6.012 3.968 16.735 4.396 31.874 1.271l.166-.033c-2.042 1.909-5.536 4.471-10.49 6.772z'
        fill='#FFF'
      />
    </svg>
  )
}

export function MySQLIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns='http://www.w3.org/2000/svg'
      width='64'
      height='64'
      viewBox='0 0 25.6 25.6'
    >
      <path
        d='M179.076 94.886c-3.568-.1-6.336.268-8.656 1.25-.668.27-1.74.27-1.828 1.116.357.355.4.936.713 1.428.535.893 1.473 2.096 2.32 2.72l2.855 2.053c1.74 1.07 3.703 1.695 5.398 2.766.982.625 1.963 1.428 2.945 2.098.5.357.803.938 1.428 1.16v-.135c-.312-.4-.402-.98-.713-1.428l-1.34-1.293c-1.293-1.74-2.9-3.258-4.64-4.506-1.428-.982-4.55-2.32-5.13-3.97l-.088-.1c.98-.1 2.14-.447 3.078-.715 1.518-.4 2.9-.312 4.46-.713l2.143-.625v-.4c-.803-.803-1.383-1.874-2.23-2.632-2.275-1.963-4.775-3.882-7.363-5.488-1.383-.892-3.168-1.473-4.64-2.23-.537-.268-1.428-.402-1.74-.848-.805-.98-1.25-2.275-1.83-3.436l-3.658-7.763c-.803-1.74-1.295-3.48-2.275-5.086-4.596-7.585-9.594-12.18-17.268-16.687-1.65-.937-3.613-1.34-5.7-1.83l-3.346-.18c-.715-.312-1.428-1.16-2.053-1.562-2.543-1.606-9.102-5.086-10.977-.5-1.205 2.9 1.785 5.755 2.8 7.228.76 1.026 1.74 2.186 2.277 3.346.3.758.4 1.562.713 2.365.713 1.963 1.383 4.15 2.32 5.98.5.937 1.025 1.92 1.65 2.767.357.5.982.714 1.115 1.517-.625.893-.668 2.23-1.025 3.347-1.607 5.042-.982 11.288 1.293 15 .715 1.115 2.4 3.57 4.686 2.632 2.008-.803 1.56-3.346 2.14-5.577.135-.535.045-.892.312-1.25v.1l1.83 3.703c1.383 2.186 3.793 4.462 5.8 5.98 1.07.803 1.918 2.187 3.256 2.677v-.135h-.088c-.268-.4-.67-.58-1.027-.892-.803-.803-1.695-1.785-2.32-2.677-1.873-2.498-3.523-5.265-4.996-8.12-.715-1.383-1.34-2.9-1.918-4.283-.27-.536-.27-1.34-.715-1.606-.67.98-1.65 1.83-2.143 3.034-.848 1.918-.936 4.283-1.248 6.737-.18.045-.1 0-.18.1-1.426-.356-1.918-1.83-2.453-3.078-1.338-3.168-1.562-8.254-.402-11.913.312-.937 1.652-3.882 1.117-4.774-.27-.848-1.16-1.338-1.652-2.008-.58-.848-1.203-1.918-1.605-2.855-1.07-2.5-1.605-5.265-2.766-7.764-.537-1.16-1.473-2.365-2.232-3.435-.848-1.205-1.783-2.053-2.453-3.48-.223-.5-.535-1.294-.178-1.83.088-.357.268-.5.623-.58.58-.5 2.232.134 2.812.4 1.65.67 3.033 1.294 4.416 2.23.625.446 1.295 1.294 2.098 1.518h.938c1.428.312 3.033.1 4.37.5 2.365.76 4.506 1.874 6.426 3.08 5.844 3.703 10.664 8.968 13.92 15.26.535 1.026.758 1.963 1.25 3.034.938 2.187 2.098 4.417 3.033 6.56.938 2.097 1.83 4.24 3.168 5.98.67.937 3.346 1.427 4.55 1.918.893.4 2.275.76 3.08 1.25 1.516.937 3.033 2.008 4.46 3.034.713.534 2.945 1.65 3.078 2.54zm-45.5-38.772a7.09 7.09 0 0 0-1.828.223v.1h.088c.357.714.982 1.205 1.428 1.83l1.027 2.142.088-.1c.625-.446.938-1.16.938-2.23-.268-.312-.312-.625-.535-.937-.268-.446-.848-.67-1.206-1.026z'
        transform='matrix(.390229 0 0 .38781 -46.300037 -16.856717)'
        fillRule='evenodd'
        fill='#00678c'
      />
    </svg>
  )
}

export function OpenRouterIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      fill='currentColor'
      fillRule='evenodd'
      height='1em'
      viewBox='0 0 24 24'
      width='1em'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path d='M16.804 1.957l7.22 4.105v.087L16.73 10.21l.017-2.117-.821-.03c-1.059-.028-1.611.002-2.268.11-1.064.175-2.038.577-3.147 1.352L8.345 11.03c-.284.195-.495.336-.68.455l-.515.322-.397.234.385.23.53.338c.476.314 1.17.796 2.701 1.866 1.11.775 2.083 1.177 3.147 1.352l.3.045c.694.091 1.375.094 2.825.033l.022-2.159 7.22 4.105v.087L16.589 22l.014-1.862-.635.022c-1.386.042-2.137.002-3.138-.162-1.694-.28-3.26-.926-4.881-2.059l-2.158-1.5a21.997 21.997 0 00-.755-.498l-.467-.28a55.927 55.927 0 00-.76-.43C2.908 14.73.563 14.116 0 14.116V9.888l.14.004c.564-.007 2.91-.622 3.809-1.124l1.016-.58.438-.274c.428-.28 1.072-.726 2.686-1.853 1.621-1.133 3.186-1.78 4.881-2.059 1.152-.19 1.974-.213 3.814-.138l.02-1.907z' />
    </svg>
  )
}

export function MongoDBIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg {...props} xmlns='http://www.w3.org/2000/svg' viewBox='0 0 128 128'>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        fill='currentColor'
        d='M88.038 42.812c1.605 4.643 2.761 9.383 3.141 14.296.472 6.095.256 12.147-1.029 18.142-.035.165-.109.32-.164.48-.403.001-.814-.049-1.208.012-3.329.523-6.655 1.065-9.981 1.604-3.438.557-6.881 1.092-10.313 1.687-1.216.21-2.721-.041-3.212 1.641-.014.046-.154.054-.235.08l.166-10.051-.169-24.252 1.602-.275c2.62-.429 5.24-.864 7.862-1.281 3.129-.497 6.261-.98 9.392-1.465 1.381-.215 2.764-.412 4.148-.618z'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        fill='#45A538'
        d='M61.729 110.054c-1.69-1.453-3.439-2.842-5.059-4.37-8.717-8.222-15.093-17.899-18.233-29.566-.865-3.211-1.442-6.474-1.627-9.792-.13-2.322-.318-4.665-.154-6.975.437-6.144 1.325-12.229 3.127-18.147l.099-.138c.175.233.427.439.516.702 1.759 5.18 3.505 10.364 5.242 15.551 5.458 16.3 10.909 32.604 16.376 48.9.107.318.384.579.583.866l-.87 2.969z'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        fill='#46A037'
        d='M88.038 42.812c-1.384.206-2.768.403-4.149.616-3.131.485-6.263.968-9.392 1.465-2.622.417-5.242.852-7.862 1.281l-1.602.275-.012-1.045c-.053-.859-.144-1.717-.154-2.576-.069-5.478-.112-10.956-.18-16.434-.042-3.429-.105-6.857-.175-10.285-.043-2.13-.089-4.261-.185-6.388-.052-1.143-.236-2.28-.311-3.423-.042-.657.016-1.319.029-1.979.817 1.583 1.616 3.178 2.456 4.749 1.327 2.484 3.441 4.314 5.344 6.311 7.523 7.892 12.864 17.068 16.193 27.433z'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        fill='#409433'
        d='M65.036 80.753c.081-.026.222-.034.235-.08.491-1.682 1.996-1.431 3.212-1.641 3.432-.594 6.875-1.13 10.313-1.687 3.326-.539 6.652-1.081 9.981-1.604.394-.062.805-.011 1.208-.012-.622 2.22-1.112 4.488-1.901 6.647-.896 2.449-1.98 4.839-3.131 7.182a49.142 49.142 0 01-6.353 9.763c-1.919 2.308-4.058 4.441-6.202 6.548-1.185 1.165-2.582 2.114-3.882 3.161l-.337-.23-1.214-1.038-1.256-2.753a41.402 41.402 0 01-1.394-9.838l.023-.561.171-2.426c.057-.828.133-1.655.168-2.485.129-2.982.241-5.964.359-8.946z'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        fill='#4FAA41'
        d='M65.036 80.753c-.118 2.982-.23 5.964-.357 8.947-.035.83-.111 1.657-.168 2.485l-.765.289c-1.699-5.002-3.399-9.951-5.062-14.913-2.75-8.209-5.467-16.431-8.213-24.642a4498.887 4498.887 0 00-6.7-19.867c-.105-.31-.407-.552-.617-.826l4.896-9.002c.168.292.39.565.496.879a6167.476 6167.476 0 016.768 20.118c2.916 8.73 5.814 17.467 8.728 26.198.116.349.308.671.491 1.062l.67-.78-.167 10.052z'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        fill='#4AA73C'
        d='M43.155 32.227c.21.274.511.516.617.826a4498.887 4498.887 0 016.7 19.867c2.746 8.211 5.463 16.433 8.213 24.642 1.662 4.961 3.362 9.911 5.062 14.913l.765-.289-.171 2.426-.155.559c-.266 2.656-.49 5.318-.814 7.968-.163 1.328-.509 2.632-.772 3.947-.198-.287-.476-.548-.583-.866-5.467-16.297-10.918-32.6-16.376-48.9a3888.972 3888.972 0 00-5.242-15.551c-.089-.263-.34-.469-.516-.702l3.272-8.84z'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        fill='#57AE47'
        d='M65.202 70.702l-.67.78c-.183-.391-.375-.714-.491-1.062-2.913-8.731-5.812-17.468-8.728-26.198a6167.476 6167.476 0 00-6.768-20.118c-.105-.314-.327-.588-.496-.879l6.055-7.965c.191.255.463.482.562.769 1.681 4.921 3.347 9.848 5.003 14.778 1.547 4.604 3.071 9.215 4.636 13.813.105.308.47.526.714.786l.012 1.045c.058 8.082.115 16.167.171 24.251z'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        fill='#60B24F'
        d='M65.021 45.404c-.244-.26-.609-.478-.714-.786-1.565-4.598-3.089-9.209-4.636-13.813-1.656-4.93-3.322-9.856-5.003-14.778-.099-.287-.371-.514-.562-.769 1.969-1.928 3.877-3.925 5.925-5.764 1.821-1.634 3.285-3.386 3.352-5.968.003-.107.059-.214.145-.514l.519 1.306c-.013.661-.072 1.322-.029 1.979.075 1.143.259 2.28.311 3.423.096 2.127.142 4.258.185 6.388.069 3.428.132 6.856.175 10.285.067 5.478.111 10.956.18 16.434.008.861.098 1.718.152 2.577z'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        fill='#A9AA88'
        d='M62.598 107.085c.263-1.315.609-2.62.772-3.947.325-2.649.548-5.312.814-7.968l.066-.01.066.011a41.402 41.402 0 001.394 9.838c-.176.232-.425.439-.518.701-.727 2.05-1.412 4.116-2.143 6.166-.1.28-.378.498-.574.744l-.747-2.566.87-2.969z'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        fill='#B6B598'
        d='M62.476 112.621c.196-.246.475-.464.574-.744.731-2.05 1.417-4.115 2.143-6.166.093-.262.341-.469.518-.701l1.255 2.754c-.248.352-.59.669-.728 1.061l-2.404 7.059c-.099.283-.437.483-.663.722l-.695-3.985z'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        fill='#C2C1A7'
        d='M63.171 116.605c.227-.238.564-.439.663-.722l2.404-7.059c.137-.391.48-.709.728-1.061l1.215 1.037c-.587.58-.913 1.25-.717 2.097l-.369 1.208c-.168.207-.411.387-.494.624-.839 2.403-1.64 4.819-2.485 7.222-.107.305-.404.544-.614.812-.109-1.387-.22-2.771-.331-4.158z'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        fill='#CECDB7'
        d='M63.503 120.763c.209-.269.506-.508.614-.812.845-2.402 1.646-4.818 2.485-7.222.083-.236.325-.417.494-.624l-.509 5.545c-.136.157-.333.294-.398.477-.575 1.614-1.117 3.24-1.694 4.854-.119.333-.347.627-.525.938-.158-.207-.441-.407-.454-.623-.051-.841-.016-1.688-.013-2.533z'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        fill='#DBDAC7'
        d='M63.969 123.919c.178-.312.406-.606.525-.938.578-1.613 1.119-3.239 1.694-4.854.065-.183.263-.319.398-.477l.012 3.64-1.218 3.124-1.411-.495z'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        fill='#EBE9DC'
        d='M65.38 124.415l1.218-3.124.251 3.696-1.469-.572z'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        fill='#CECDB7'
        d='M67.464 110.898c-.196-.847.129-1.518.717-2.097l.337.23-1.054 1.867z'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        fill='#4FAA41'
        d='M64.316 95.172l-.066-.011-.066.01.155-.559-.023.56z'
      />
    </svg>
  )
}

export function ServerIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns='http://www.w3.org/2000/svg'
      viewBox='0 0 24 24'
      fill='none'
      stroke='currentColor'
      strokeWidth='2'
      strokeLinecap='round'
      strokeLinejoin='round'
    >
      <rect width='20' height='8' x='2' y='2' rx='2' ry='2' />
      <rect width='20' height='8' x='2' y='14' rx='2' ry='2' />
      <line x1='6' x2='6.01' y1='6' y2='6' />
      <line x1='6' x2='6.01' y1='18' y2='18' />
    </svg>
  )
}
